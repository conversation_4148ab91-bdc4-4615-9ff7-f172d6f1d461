/*输入框样式*/

.text {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.detail_row {
	height: 45px;
}


/*新增子项按钮*/

#btn_query,
#btn_new_sub,
#btn_driver_order {
	width: 99%;
	font-size: 16px;
	line-height: 1.8;
	float: left;
	margin: 2px;
}


/*列表显示*/

.mui-table-view-cell {
	font-size: 22px;
	padding: 6px 0px 8px 10px;
	text-align: left;
}

.mui-navigate-right:after {
	content: '';
}

#allocate_vehicle_id,
#allocate_vehicle_subid {
	font-size: 22px;
	color: blue;
	margin-right: 5%;
}

.icon {
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}

.row {
	margin: 5px;
	font-size: 18px;
}

.select:after {
	content: '\e472';
	color: red;
	font-size: 50px;
	font-weight: 600;
	right: 10px;
}

.mui-table-view-radio .mui-table-view-cell>a:not(.mui-btn) {
	margin-right: -75px;
}

.mui-control-content {
	background-color: white;
	min-height: 280px;
}


}