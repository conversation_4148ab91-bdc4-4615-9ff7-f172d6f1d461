#companyList label {
	font-size: 14px;
}

#steel_support_id {
	width: 71%;
	padding: 0px 5px;
	font-size: 20px;
}

#steel_support_id_1{
	font-size: 22px;
	margin-bottom: 4px;
	color: blue;
}
#steel_support_name{
	font-size: 16px;
}

#spec span{
	background-color: blue;
	color: white;
	margin-right: 6px;
}

#steel_support_name span{
	background-color: red;
	color: white;
	margin-right: 6px;
}
.left{
	float: left;
	width: 50%;
}
/** 明细样式 */
.detail_row{
	height: 50px;
}

.text{
	float: left;
	width: 29%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	color: #0000FF;
}
/*.mui-placeholder {
	margin-top: 10px;
}

.mui-placeholder span {
	font-size: 20px;
	color: #000000;
}*/

.search_btn {
	width: 19%;
}

/*.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}*/



/* 半透明的遮罩层 */
.overlay {
    background-color: #777777;
     opacity: 0.5; /* 透明度 */
    /*filter: alpha(opacity=50); /* IE的透明度 
    
    display: none;
   
    top: 0px;
    left: 0px;*/
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 99999; /* 此处的图层要大于页面 */
    /*display:none;*/
}
#paihao {
	width: 71%;
	padding: 0px 5px;
	font-size: 20px;
}