	/**
			 * 初始化变量信息  
			 */
			var successFlag = true;
			var if_judge_pack_location = "0"; //库位开关 
			var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
			var userId = localStorage.getItem("account");
			var location_id = "";
			var innerDiameterListHtml = "";
			var packObj = {};
			var paramObj = {};
			
			// 进入页面事件
			$(function() {
				mui.init({
					swipeBack: true //启用右滑关闭功能
				});
				//库位输入框获得焦点
				$("#pack_id")[0].focus();
				
			});
		    
		    // back事件
		    mui(document.body).on('tap','#back',function(){
				mui.back();
			});
		    
		    
           mui.back = function () {   
				if(scanPackList ==null || scanPackList==""){
				 mui.openWindow({
					url:'../public/index.html',
					id:'index',
					createNew:false
				  });
				}else{
					var btnArray = ['退出', '取消'];
					mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
					   if(e.index==0){
					     setTimeout(function() {
						  var ws=plus.webview.currentWebview();
						  plus.webview.close(ws)
						}, 0);
					   }
					},'div');
				}
			};
              
            
			//页面加载事件
			window.onload = function onload() {
				mui.plusReady(function() {
					//$("#up_down_flag").val(1);
				});
			}
			
//===========================================TODO 增加keypress监听 start=====================================================
			/**
			 * 倒库捆包字段监听
			 * 查询捆包信息
			 */
			var scanPackList = new Array();
			$("#pack_id").keypress(function(e) {
				if(e.keyCode == 13) {
					//判断捆包是否已经被扫描
					var pack_id  = $("#pack_id").val();
					if(pack_id == ""){
						mui.alert("倒库捆包号不能为空","提 示","确定",null,'div');
						$("#pack_id").val("");
						$("#pack_id")[0].focus();
						return false;
					}
					pack_id = formatPackId(pack_id);
					$("#pack_id").val(pack_id);
				   //已扫描列表追加一条捆包记录
					pack_location_target = $("#pack_location_new").val().trim(); //新庫位，目標庫位
				    queryLocationChangePack();
				}
			});
			
						
			/*
			 * 库位
			 */
			var pack_location_target = "";
			$("#pack_location_new").keypress(function(e) {
				if(e.keyCode == 13) {
					if($("#pack_location_new").val() == "" || $("#pack_location_new").val() == null) {
						mui.alert("请扫描或者输入库位（新）", "提示", "确定", null, "div");
						$("#pack_location_new")[0].focus();
						return;
					}
					var pack_id = $("#pack_id").val();
					if(pack_id != null && pack_id != ""){
						if(if_judge_pack_location == "1"){//库位开关打开时，判断库位是否存在
							judgeLocationExist();
						}
						/*pack_id = formatPackId(pack_id);
						pack_location_target = $("#pack_location_new").val().trim();
						scanPackList=[];
						queryLocationChangePack();*/
					}
					$("#new_loc_view_id")[0].focus();
				}
			});
			
			/**
			 *相邻捆包 
			 */
			var adjacent_pack_id  = "";
			$("#adjacent_pack_id").keypress(function(e) {
				if(e.keyCode == 13) {
					/*
					if($("#adjacent_pack_id").val() == "" || $("#adjacent_pack_id").val() == null){
						mui.alert("请扫描或者输入库位（新）", "提示", "确定", null, "div");
					}*/
					adjacent_pack_id = $("#adjacent_pack_id").val();
				}
			});
			
			/**
			 *  新坐标
			 */
			$("#x_point_start").keypress(function(e) {
				var x = $("#x_point_start").val();
				console.log(JSON.stringify(packObj.outer_diameter));
				var o = packObj.outer_diameter/10;
				var y;
				if(e.keyCode == 13) {
					if(o == null || o == ""){
						//mui.alert("无法获得捆包外径，请输入结束坐标", "提示", "确定", function() {$("#x_point_end")[0].focus()}, "div");
						plus.nativeUI.toast("无法获得捆包外径，请输入结束坐标");
						$("#x_point_end")[0].focus();
					}else{
						y = parseInt(x) + parseInt(o);
						$("#x_point_end").val(y);
					}
				}
			});
			
			
			
//===========================================TODO 增加keypress监听 end===============================================

//===========================================TODO 增加button监听 start===============================================
			/*
			 * comment by lal
			 * 倒库确认共呢个
			 */
			mui(document.body).on('tap', '#storage_but', function() {
				var x_point_start = $("#x_point_start").val();
				var x_point_end = $("#x_point_end").val();
				var old_x_point_start = packObj.x_point_start;
				var old_x_point_end = packObj.x_point_end;
				var up_down_flag = $("#up_down_flag").val();
				var new_loc_view_id = $("#new_loc_view_id").val();
				var pack_location_new = $("#pack_location_new").val();
				
				if($("#pack_id").val() == "" || $("#pack_id").val() == null){
					//mui.alert("请输入捆包信息", "提示", "确定", function() {$("#pack_id")[0].focus()}, "div");
					plus.nativeUI.toast("请扫描捆包信息");
					$("#pack_id")[0].focus();
					return false;
				}
				
				if($("#inner_diameter").val() == "" || $("#inner_diameter").val() == null){
					//mui.alert("请捆包卷内径", "提示", "确定", function() {}, "div");
					plus.nativeUI.toast("请捆包卷内径");
					$("#inner_diameter")[0].focus();
					return false;
				}
				
				if(pack_location_new == "" || pack_location_new == null){
					//mui.alert("请输入新库位", "提示", "确定", function() {$("#pack_location_new")[0].focus()}, "div");
					plus.nativeUI.toast("请输入新库位");
					$("#pack_location_new")[0].focus();
					return false;
				}
				
				if(new_loc_view_id == "" || new_loc_view_id == null){
					//mui.alert("请输入新库位", "提示", "确定", function() {$("#pack_location_new")[0].focus()}, "div");
					plus.nativeUI.toast("请输入精确库位坐标");
					$("#new_loc_view_id")[0].focus();
					return false;
				}
				
				if(direction != "L" && direction != "R"){
					plus.nativeUI.toast("请选择紧靠方向");
					return false;
				}
				
				//判断如果是下层库位，必须填起始坐标值
				if(up_down_flag == 1){
					if((new_loc_view_id !=null && new_loc_view_id != "") && parseInt((new_loc_view_id%2 ==0) ?"0":"1") == 0){
						plus.nativeUI.toast("库位精确坐标必须为奇数");
						$("#new_loc_view_id")[0].focus();
						return false;
					}
					if(x_point_start == null || x_point_start == ""){
						//mui.alert("必须输入库位起始坐标", "提示", "确定", function() {}, "div");
						plus.nativeUI.toast("下层库位必须输入库位起始坐标");
						$("#x_point_start")[0].focus();
						return false;
					}
					if(x_point_end == 0 || x_point_end == null ||x_point_end == ""){
						//mui.alert("请先扫描或者输入捆包号", "提示", "确定", function() {}, "div");
						plus.nativeUI.toast("下层库位必须输入库位结束坐标");
						$("#x_point_end")[0].focus();
						return false;
					}
					if(parseInt(x_point_start) >= parseInt(x_point_end) ){
						plus.nativeUI.toast("结束坐标必须大于起始坐标");
						$("#x_point_end")[0].focus();
						return false;
					}
				}else if(up_down_flag == 2){
					if(new_loc_view_id == 0 || new_loc_view_id == null ||new_loc_view_id == ""){
						plus.nativeUI.toast("库位精确坐标必填");
						$("#new_loc_view_id")[0].focus();
						return false;
					}
					if(parseInt((new_loc_view_id%2 ==0) ?"0":"1") == 1){
						plus.nativeUI.toast("库位精确坐标必须为偶数");
						("#new_loc_view_id")[0].focus();
						return false;
					}
					
					if(x_point_start == "" || x_point_start == null){
					   x_point_start = "0";
					}
					if(x_point_end == "" || x_point_end == null){
					   x_point_end = "0";
					}
				}else{
					plus.nativeUI.toast("请选择上下层库位信息");
					$("#up_down_flag").focus();
					return false;
				}
				
				if(old_x_point_start == "" || old_x_point_start == null){
				   old_x_point_start = "0";
				}
				
				if(old_x_point_end == "" || old_x_point_end == null){
				   old_x_point_end = "0";
				}
				
				var new_location = pack_location_new + "-" + new_loc_view_id;
				console.log(new_location);
				paramObj.pack_id = packObj.pack_id;
				paramObj.product_id = packObj.product_id;
				paramObj.old_location = packObj.location_id;//校验
				paramObj.new_location = new_location;//校验
				paramObj.old_loc_view_id = packObj.loc_view_id;
				paramObj.old_x_point_start = old_x_point_start;
				paramObj.old_x_point_end = old_x_point_end;
				paramObj.new_x_point_start = x_point_start;
				paramObj.new_x_point_end = x_point_end;
				paramObj.new_up_down_flag = $("#up_down_flag").val(); //校验
				paramObj.inner_diameter = $("#inner_diameter").val();//校验
				paramObj.ajoin_pack_id = $("#adjacent_pack_id").val();//校验
				paramObj.ajoin_direction = direction; //校验
				paramObj.seg_no = segNo;
				paramObj.user_id = userId;
				
				mui("#storage_but").button("loading");
				$("#overlay").addClass("overlay");
				exePackLocationChangeUpload();
			});
			
			/*
			 * comment by lal 
			 * 库位推荐算法目前还未完全做完，后续再补上
			 * 库位推荐事件 
			 */
			mui(document.body).on('tap', '#storage_realloc', function() {
				console.log(">>>>>>scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
				if(scanPackList == null || scanPackList ==""){
					mui.alert("已扫描列表中无捆包记录", "提示", "确定", null, 'div');
					return false;
				}
				if(scanPackList.length >1){
					mui.alert("库位推荐限制操作单个捆包！","提 示","确定",null,'div');
				}
				
				var reallocParams = {};
				$.each(scanPackList, function(i, item) {
					 reallocParams.pack_id = item.pack_id;
					 reallocParams.product_id = item.product_id;
					 reallocParams.pack_location = item.pack_location_new;
					 reallocParams.old_pack_locaiton  = item.location_id
					 reallocParams.loc_view_id = item.loc_view_id;
				});
				
				if(reallocParams.loc_view_id == null || reallocParams.loc_view_id == ""){
						mui.alert("手工指定库位不可再做库位推荐", "提示", "确定", function() {}, "div");
						return false;
				}
				console.log(">>>>>>reallocParams:" + JSON.stringify(reallocParams) + ">>>>>>>>>>>>");
				if(reallocParams.pack_location == reallocParams.old_pack_locaiton){
					mui.confirm('确定是否推荐库位？', '提示', ['确认','取消'], function(e) {
						if (e.index == 0) {
							exeReallocLocation(reallocParams);
						} else {
							return;
						}
					},'div');
				}else{
					mui.confirm('是否重新推荐库位？', '提示', ['确认','取消'], function(e) {
						if (e.index == 0) {
							exeReallocLocation(reallocParams);
						} else {
							return;
						}
					},'div');
				}
				
				//mui("#storage_realloc").button("loading");
				//$("#overlay").addClass("overlay");
				
			});
//===========================================TODO 增加button监听  end===============================================

//===========================================TODO 增加function start===============================================
			
			/*
			 * 校验捆包是否已经扫描，目前用不到    comment by lal 
			 */
		    function packIfAlreayScaned(pack_id){
			    var result = false;
				$.each(scanPackList,function(i,value){
					//console.log("数组 pack_id=========="+value.pack_id+",当前扫描pack_id========="+pack_id); 
					if(value.pack_id==pack_id){
						result = true;
						return;//跳出循环  return true 等价于  continue   
					}
				});
				return result;
		   };
			
			/*
			 * 倒库查詢捆包信息，带出外径和捆包库位相关信息
			 */
			function queryLocationChangePack() {
				mui.plusReady(function() {
					var curNetConnetType = plus.networkinfo.getCurrentType();
					if( curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW
						|| curNetConnetType ==plus.networkinfo.CONNECTION_NONE){
						queryPackNetNotConnect();
					}else{
						queryPackNetConnect();
					}
				});
			};
			
			//无网络连接时，只记录扫描捆包号
			function queryPackNetNotConnect(){
				var pack_id = $("#pack_id").val();
				$("#pack_id").val("");
				$("#pack_id").focus();				
				packObj = {
				    pack_id: pack_id,
				    adjacent_pack_id: $("#adjacent_pack_id").val(),
				    product_id: "",
				    scan_time: getnowtime(),
				    pack_location_new: pack_location_target,
				    location_id: ""
				};
				//scanPackList.push(curPackObj);
				//initData();
			};
			
			//有网络环境时，调用接口获取捆包信息
			function queryPackNetConnect() {
			<!-- 查询前先关闭软键盘-->
				//document.activeElement.blur();
				var webServiceUrl = localStorage.getItem("webServiceUrl");
				var outUri = domainName+"webService_test.jsp";
				var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
				var params = '{"seg_no":"' + segNo + '","pack_id":"' + $("#pack_id").val() + '"}';
				var method = "exeQueryPackInfoStrip";
				$.ajax({
                 	type:"get",
					async:true,
					url:outUri,
					dataType: "json",
					timeout:2000,
					data: {
					    innerUri:innerUri,
					    params:params,
					    method:method
				    },
				  success: function(data) {
				  	if(data != null) {
				  		console.log(">>>>>>>" + JSON.stringify(data));
						if(data.packList.length == 0) {
							mui.alert("未查询到对应捆包号，请确认", "提示", "确定", null, 'div');
							return;
						}else{
							var packList = data.packList;
							packObj = {
									    pack_id: packList[0].pack_id,
									    //label_id: packList[0].label_id,
									    product_id: packList[0].product_id,
									    spec: packList[0].spec,
									    putin_weight: packList[0].putin_weight,
									    inner_diameter: packList[0].inner_diameter,
									    outer_diameter: packList[0].outer_diameter,
									    location_id: packList[0].location_id,
									    loc_view_id: packList[0].loc_view_id,
									    x_point_start: packList[0].x_point_start,
									    x_point_end: packList[0].x_point_end,
									    avaliable_min_length: packList[0].avaliable_min_length,
									    scan_time: getnowtime()
								      };
								console.log("1111111111111111111111"+ JSON.stringify(packObj));
							$("#fp_location").val(packObj.location_id);
							$("#fp_location_s").val(packObj.loc_view_id);
							if(packObj.x_point_start != null && packObj.x_point_start != "" && packObj.x_point_end != null && packObj.x_point_end != ""){
								$("#up_down_flag").val(1);
							}
							// 增加内径选择
							if(packObj.inner_diameter == null || packObj.inner_diameter == ""){
								setInnerDiameterListHtml();
							}else{
								$("#inner_diameter").val(packObj.inner_diameter);
								$("#pack_location_new")[0].focus();
							}
							packObj.inner_diameter = $("#inner_diameter").val();
							
							
							
						}
					}else { //连接失败
						mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
						return;
					}
				 },
				error:function(XMLHttpRequest, textStatus, errorThrown) {
					//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
					//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
				  	 if(textStatus == "timeout"){
				  	 	queryPackNetNotConnect();
				  	 }else{
				  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
				  	 }
                	}
				 }); 
				 
			};

			/*
			 * 修改了超时请求的方式 170922 wangshengbo
			 */
			function exePackLocationChangeUpload() {
				// 查询前先关闭软键盘-->
				document.activeElement.blur();
				var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
				var user_id = localStorage.getItem("account"); //采用localStorage存储数据
				var webServiceUrl = localStorage.getItem("webServiceUrl");
				//var outUri = domainName+"webService.jsp?callback=?";
				var outUri = domainName+"webService_test.jsp";
				var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
				var method = "exeStripPackLocationChange";
				var params = JSON.stringify(paramObj);
				console.log("params>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+JSON.stringify(paramObj));
				var outtime = 10000;//默认超时未10秒 超过100条记录设置为30000
				if(scanPackList.length > 100 && scanPackList.length <= 200){
					outtime = 30000;//30000
				}else if(scanPackList.length > 200){
					outtime = 60000;//60000
				}
				try{
	               $.ajax({
	                 	type:"post",
						async:true,
						timeout:outtime,
						url:outUri,
						dataType: "json",
						data: {
						    innerUri:innerUri,
						    params:params,
						    method:method
					    },
					  success: function(data) {
					  	successFlag = true;
						if(data != null) {
							mui("#storage_but").button("reset");
					        $("#overlay").removeClass("overlay");
							if(data.resultStatus == "1") {
								var msg=data.resultDesc;
								console.log(msg)
								mui.alert(msg, "提示", "确定", function() {
									//清空本次已扫描捆包列表数据
									packObj = {};
									paramObj = {};
									$("#pack_id").val("");
									$("#adjacent_pack_id").val("");
									$("#inner_diameter").val("");
									$("#pack_location_new").val("");
									$("#up_down_flag").val(1);
									$("#x_point_start").val("");
									$("#x_point_end").val("");
									$("#new_loc_view_id").val("");
									$("#sj_location").val("");
									$("#sj_location_s").val("");
									direction = "";
									$("#left_location_btn").removeClass('active');
									$("#right_location_btn").removeClass('active');
								}, 'div');
								return;
							} else {
								mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {
								}, 'div');
								return; 
							}
						} else { //连接失败
							mui("#storage_but").button("reset");
					        $("#overlay").removeClass("overlay");
							mui.alert("工贸服务器处理异常", "提示", "确定", function() {
							}, 'div');
							return;
						}
					 },
					 error: function(xhr, textStatus, errorThrown) {
						console.log("xhr.readystate>>>>>"+xhr.readyState+"textStatus>>>>>>>>>>>>>"+textStatus); 
					  	mui.plusReady(function() {
							var curNetConnetType = plus.networkinfo.getCurrentType();
							if( curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW
								|| curNetConnetType ==plus.networkinfo.CONNECTION_NONE){
								// mui.alert("无网络连接。请检查网络后再次上传","提示","确定",function() {}, "div");
								plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
							}else if(textStatus == "timeout"){
								/*if(!successFlag){
									mui.alert("连接服务器异常","提示","确定",function() {}, "div");
								}*/
								xhr.abort();
								mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
							}else{
								mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
							}
							successFlag = true;
							mui("#storage_but").button('reset');
						    $("#overlay").removeClass("overlay");
						  });
	                   }
					})
				}catch(e){
					successFlag = true;
					alert("error:"+e.message);				
				}
			}	

			//判断库位是否存在
			function judgeLocationExist(){
				<!-- 查询前先关闭软键盘-->
				document.activeElement.blur();
				var webServiceUrl = localStorage.getItem("webServiceUrl");
				var outUri = domainName+"webService.jsp?callback=?";
				var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService'; 
				var params = '{"seg_no":"' + segNo + '","pack_location":"'+$("#pack_location_new").val()+'"}';
				var method = "exeQueryLocation";
				console.log("params"+params);
				$.getJSON(outUri, {innerUri: innerUri,params: params,method: method}, function(data) {
				if(null != data) {
			      	console.log("resultStatus:"+data.resultStatus);
			      	console.log("locationList:"+ JSON.stringify(data.locationList));
			        if(data.resultStatus == "0"){
			          	mui.alert("库位不存在","提示","确认",null,"div");
			          	$("#pack_location_new")[0].focus();
			          	return;
			        }else{
			        	$("#pack_id")[0].focus();
			        };
			        if(data.resultStatus == "1"){
			          	if(data.locationList.length > 0 ){
			          		$.each(data.locationList,function(i,item){
							location_id = item.location_id;
			        	});}
			          	}
				      } 
					}
				)}
			
			/*
			function exeReallocLocation(reallocParams) {
				var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
				var user_id = localStorage.getItem("account"); //采用localStorage存储数据
				var webServiceUrl = localStorage.getItem("webServiceUrl");
				var outUri = domainName+"webService.jsp?callback=?";
				var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService'; 
				var use_type = "PDA倒库推荐库位";
				var pack_location = "";
				if(reallocParams.pack_location == null || reallocParams.pack_location == ""){
					pack_location = reallocParams.old_pack_locaiton;
				}else{
					pack_location = reallocParams.pack_location;
				}
				var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id +  '","pack_id":"' + reallocParams.pack_id +  '","product_id":"' + reallocParams.product_id  + '","use_type":"' + use_type + '","loc_view_id":"' + reallocParams.loc_view_id  + '","pack_location":'+JSON.stringify(pack_location)+'}';
				console.log("params:" + JSON.stringify(params));
				params = encodeURI(params, 'utf-8');
				var method = "exeReallocLoacation";
				$.getJSON(outUri, {
					innerUri: innerUri,
					params: params,
					method: method
				}, function(data) { //如返回对象有一个username属性
					console.log("data:" + JSON.stringify(data));
					if(data != null) {
						if(data.resultStatus == 1) {
							if(data.resultLocationId != null && data.resultLocationId != ""){
								mui.alert("操作成功", "提示", "确定", function() {}, "div");
								$("#pack_location_new").val(data.resultLocationId);
								if(scanPackList[0].pack_location_new == null || scanPackList[0].pack_location_new == ""){
									scanPackList[0].location_id = scanPackList[0].location_id;
								}else{
									scanPackList[0].location_id = scanPackList[0].pack_location_new;
								}
								scanPackList[0].pack_location_new = data.resultLocationId;
								scanPackList[0].loc_view_id = data.resultLocViewId;
								initData();
								return true;
							}else{
								mui.alert("无可用库位信息,请手工指定库位", "提示", "确定", function() {}, "div");
							}
						} else if (data.resultStatus == 0){
							mui.alert(data.resultDesc, "提示", "确定", function() {}, "div");
							return false;
						}else {
							mui.alert("不满足库位预分配条件,无法分配库位", "提示", "确定", function() {}, "div");
							return false;
						}
					} else{
						mui.alert("连接失败", "提示", "确定", function() {}, "div");
						return false;
					}
				});
			}
				
			*/
			

//============================================================ TODO comment by lal 页面控件控制  ============================================
			
			//左右点击事件
			var direction;
			mui(document.body).on('click','#left_location_btn',function(){
	        	$(this).addClass('active').siblings().removeClass('active');
	        	direction = "L";//$(this).attr("L");
	        	$("#x_point_start")[0].focus();
	        	console.log(direction);
			});
			
			mui(document.body).on('click','#right_location_btn',function(){
	        	$(this).addClass('active').siblings().removeClass('active');
	        	direction = "R";//$(this).attr("R");
	        	$("#x_point_start")[0].focus();
	        	console.log(direction);
	        	
			});
			
			function valInnerDiameter(){
				var inner_diameter = $("#inner_diameter");
				if(inner_diameter == null || inner_diameter == "" || inner_diameter == undefined){
					mui.alert("卷内径不能为空");
				}
			}
			
			//智慧仓库:显示卷内径列表
	        mui(document.body).on('tap','#innerDiameterQueryBtn',function(){
	        	document.activeElement.blur(); 
	        	setInnerDiameterListHtml();
	        });
	        
	        //智慧仓库:
	        function setInnerDiameterListHtml(){
	        	var innerDiaObject = $("#inner_diameter");
	        	if(innerDiaObject.attr("readOnly")!="readonly"){
		        	if(!innerDiameterListHtml){
		        		loadInnerDiameter();//需要的时候才加载卷内径下拉框列表
		        	}
		        	$("#InnerDiameterDiv").toggleClass('show');
	        	}
	        }
	        
			//智慧仓库:加载卷内径列表信息
			function loadInnerDiameter(){
				//var innerDiameterAry = [420,508,610,760,762];//常用卷内径值集
				var innerDiameterAry = [508,610,760,420];
				for(var idx in innerDiameterAry) {
					innerDiameterListHtml = innerDiameterListHtml +
						'<li class="mui-table-view-cell">' +
							'<a class="mui-navigate-right">' +
								'<div style="width: 48%; float: left; text-align: left;" >' +
									'<label class ="innerDiameterValue">' + innerDiameterAry[idx] + '</label>' +
								'</div>' +
							'</a>' +
						'</li>';
					};
				$("#InnerDiameterList").html(innerDiameterListHtml);
			}
	        
	        //智慧仓库:卷内径确认按钮点击事件
			mui(document.body).on('tap', '#innerDiameterConfirm',function() {
				if(!inner_diameter) {
					mui.alert("请选择卷内径", "提示", "确认", function() {}, "div");
					return false;
				} else {
					$("#InnerDiameterDiv").toggleClass('show');
					$("#inner_diameter").val(inner_diameter);
					$("#pack_location_new")[0].focus();
				}
			});
			
			 //智慧仓库:卷内径取消按钮点击事件
			mui(document.body).on('tap', '#cancel',function() {
				$("#InnerDiameterDiv").toggleClass('show');
			});
				
		    //智慧仓库:绑定卷内径列表选中事件
			mui(document.body).on('selected', '#innerDiameterInfo .mui-table-view.mui-table-view-radio', function(e) {
				var el = e.detail.el;
				var el_J = $(el);//dom元素转化成jquery对象
				inner_diameter = el_J.find(".innerDiameterValue").text();
			});
			
			
			function validate(){
				if($("#pack_id").val() == "" || $("#pack_id").val() == null){
					mui.alert("请输入捆包信息", "提示", "确定", function() {$("#pack_id")[0].focus()}, "div");
					return false;
				}
				
				if($("#inner_diameter").val() == "" || $("#inner_diameter").val() == null){
					mui.alert("请捆包卷内径", "提示", "确定", function() {}, "div");
					return false;
				}
				
				if($("#pack_location_new").val() == "" || $("#pack_location_new").val() == null){
					mui.alert("请输入新库位", "提示", "确定", function() {$("#pack_location_new")[0].focus()}, "div");
					return false;
				}
				
				if(direction != "L" && direction != "R"){
					mui.alert("请选择紧靠方向", "提示", "确定", function() {}, "div");
					return false;
				}
			}