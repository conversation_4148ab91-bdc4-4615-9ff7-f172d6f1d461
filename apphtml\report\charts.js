mui.init({
	gestureConfig:{
	  	tap: true, //默认为true
		doubletap: true, //默认为false
		swipeBack:true //启用右滑关闭功能
	}
});
$(document).ready(function() {
	var newDay = new Date();
	//$("#query_data").val(newDay.getFullYear()+"-"+pzero(newDay.getMonth()+1)+"-"+pzero(newDay.getDate()));//设置当前时间
	mui(document.body).on('tap', '.mui-navigate-right', function() {
		var p_index = $(this).attr("p_index");
		switch (p_index){
			case "0"://捆包查询
				showPackFind();
				break;
			case "1"://当前库存
				showStockPie();
				break;
			case "2"://当前吞吐
				showStockRadar();
				break;
			case "3"://当日加工
				alert(1);
				break;
			case "4"://当前收入
				alert(1);
				break;
			default:
				break;
		}
		mui('.mui-off-canvas-wrap').offCanvas().close();
	});
	
	
	mui(document.body).on('tap', '#query_button', function() {//捆包查询
		var pack_id = $.trim($("#pack_id").val());
		if(pack_id==""){
			alert("请输入捆包号");
			return false;
		}
		querypack(pack_id);
		$("#pack_id").val("");
	});
	showPackFind();
	//showStockPie();
	
	
	document.querySelector('#query_data').addEventListener('tap',function () {
		var datevalue = $("#query_data").val();
		var dtPicker = new mui.DtPicker({"type":"date","value":datevalue}); 
	    dtPicker.show(function (selectItems) { 
	       $("#query_data").val(selectItems.text);
	       showStockRadar(1);
	    })
	})
	
	$("#pack_id").keypress(function(e){
    	if(e.keyCode==13){
    		querypack($(this).val());
    		$("#pack_id").val("");
    	}
    });
    
//		        mui(document.body).on('tap', '.mean', function() {//菜单按钮移动
//					var pack_id = $.trim($("#pack_id").val());
//					if(pack_id==""){
//						alert("请输入捆包号");
//						return false;
//					}
//					//querypack(pack_id);
//				});
	
//				mui(document.body).on('mousemove', '.mean', function(event) {//菜单按钮移动
//					var abs_x = $(this).offset().left; 
//      			var abs_y = $(this).offset().top; 
//					$(this).css({'left':event.pageX - abs_x, 'top':event.pageY - abs_y}); 
//				});
	//移动菜单按钮事件
	/*
	var _move=false;//移动标记  
	var _x,_y;//鼠标离控件左上角的相对位置  
	$(".mean").click(function(){  
		//alert("click");//点击（松开后触发）  
	}).mousedown(function(e){
		_move=true;  
		_x=e.pageX-parseInt($(".mean").css("left"));  
		_y=e.pageY-parseInt($(".mean").css("top"));  
		$(".mean").fadeTo(20, 0.5);//点击后开始拖动并透明显示  
	});  
	$(document).mousemove(function(e){  
		if(_move){  
			var x=0;//移动时根据鼠标位置计算控件左上角的绝对位置  
			var y=e.pageY-_y;
			if(y<0){
				y=0;
			}
			$(".mean").css({top:y,left:x});//控件新位置  
		}  
	}).mouseup(function(){  
		_move=false;  
		$("mean").fadeTo("fast", 1);//松开鼠标后停止移动并恢复成不透明  
	});
	*/
});
mui.back = function(){
	var ws=plus.webview.currentWebview();
	plus.webview.close(ws);
	var w = plus.webview.getWebviewById('index');
	if(w != null){
		plus.webview.show(w);
	}else{
		w = plus.webview.create('../public/index.html','index');
		plus.webview.show(w);
	}
}