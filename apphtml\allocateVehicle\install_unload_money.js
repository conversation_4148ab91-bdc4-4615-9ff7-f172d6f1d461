//滚动效果
(function($) {
	$(".mui-scroll-wrapper").scroll({
		bounce: false, //滚动条是否有弹力默认是true
		indicators: true, //是否显示滚动条,默认是true
	});
})(mui);

var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
//登陆的用户名
var user_id = localStorage.getItem("account"); //采用localStorage存储数据
var patch_flag = "1"; //跳转页面加单标记
var allocate_vehicle_id; //配车单号
var AllocateInfo = new Map(); //配车单信息 <allocate_vehicle_id,AllocateInfo>
var AllocateList = new Array(); //选中配车单信息

var vehicle_id; //车牌号
var driver_name; //驾驶人姓名
var vehicle_state;//车辆状态

/*
 * 页面初始化
 */
window.onload = function onload() {
	mui.plusReady(function() {
		mui('.mui-slider').slider().setStopped(true); //禁止tab滑动左右切换
		exeQueryAllocateVehicleId(); //页面加载时查询
	});
}
//查询子项
mui(document.body).on('tap', '#query_button', function() {
	if("" == allocate_vehicle_id || null == allocate_vehicle_id || undefined == allocate_vehicle_id) {
		mui.alert("请选择配车单号");
		return false;
	}
	exeQueryAllocateVehicleIdSub();
});

/**
 * 查询配车单据
 */
function exeQueryAllocateVehicleId() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAVehiclePointService';
	var method = "exeQueryAllocateVehicleId";
	var params = '{"seg_no":"' + seg_no + '"}';
	var AllocateHtml = "";
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			//console.log(JSON.stringify(data.resultDesc));
			if(data != null) {
				console.log(data.resultStatus);
				$.each(data.resultDesc, function(i, item) {
					if(item.start_status =='0' && item.end_status == '0'){
						vehicle_state='';
					}
					if(item.end_status == '1'){
						vehicle_state='已结束装卸货';
					}
					if(item.start_status =='1' && item.end_status == '0'){
						vehicle_state='已开始装卸货';
					}
					AllocateInfo.set(item.allocate_vehicle_id, item);
					AllocateHtml = AllocateHtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<div class="row"><span id="allocate_vehicle_id">' + item.allocate_vehicle_id + '</span><span style="color: red;">' + item.hand_big_type + '</span></div>' +
						'<div class="row"><span class="icon">车</span><span id="vehicle_id">' + item.vehicle_id + '</span><span class="icon" style="margin-left: 30px;">司</span>' + item.driver_name + '<span class="icon" style="margin-left: 30px;">交</span>' + item.deliver_type + '</div>' +
						'<div class="row"><span class="icon">车辆状态</span>' + vehicle_state + '</div>' +						
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#allocate_list").html(AllocateHtml);
			} else {
				mui.toast("没有查询到相应物流提单，请联系管理员！");
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//开始装卸货
mui(document.body).on('click', '#btn_new_sub', function() {
	allocate_vehicle_id = AllocateList.allocate_vehicle_id;
	if("" == allocate_vehicle_id || null == allocate_vehicle_id || undefined == allocate_vehicle_id) {
		mui.alert("请选择配车单号");
		return false;
	}
	exeVeicleStartHandPoint();	
});

//结束装卸货
mui(document.body).on('click', '#btn_driver_order', function() {
	vehicle_id = AllocateList.vehicle_id;
	driver_name = AllocateList.driver_name;
	allocate_vehicle_id = AllocateList.allocate_vehicle_id;
	if("" == allocate_vehicle_id || null == allocate_vehicle_id || undefined == allocate_vehicle_id) {
		mui.alert("请选择配车单！");
		return false;
	}
	exeEndLoadAndUnload();	
});

//选中配车单信息获取
var list = document.querySelector('.mui-table-view.mui-table-view-radio');
list.addEventListener('selected', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	allocate_vehicle_id = el_J.find("#allocate_vehicle_id").text();
	AllocateList = AllocateInfo.get(allocate_vehicle_id);
	vehicle_id = el_J.find("#vehicle_id").text();
	//console.log(JSON.stringify(AllocateInfo.get(allocate_vehicle_id)));
});

/**
 * 查询配车单据子项信息
 */
function exeQueryAllocateVehicleIdSub() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAVehiclePointService';
	var method = "exeQueryAllocateVehicleIdSub";
	var params = '{"seg_no":"' + seg_no + '","allocate_vehicle_id":"' + allocate_vehicle_id + '"}';
	var AllocateHtmlSub = "";
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			//console.log(JSON.stringify(data.resultDesc));
			if(data != null) {
				console.log(JSON.stringify(data.resultDesc));
				$.each(data.resultDesc, function(i, item) {
					var subid = item.allocate_vehicle_subid.substring(20, 25);
					AllocateHtmlSub = AllocateHtmlSub + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<div class="row"><span class="icon">子</span><span id="allocate_vehicle_subid">' + subid + '</div>' +
						'<div class="row"><span class="icon">提</span>' + item.voucher_num + '<span class="icon" style="margin-left: 30px;">量</span>' + item.allocate_vehicle_weight + '</div>' +
						'<div class="row"><span class="icon">客</span>' + item.cust_name +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#shtml").html(AllocateHtmlSub);
			} else {
				mui.toast("没有查询到相应物流提单，请联系管理员！");
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//开始装卸货
function exeVeicleStartHandPoint() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAVehiclePointService';
	var method = "exeStartLoadAndUnload";
	var params = '{"seg_no":"' + seg_no + '","allocate_vehicle_id":"' + allocate_vehicle_id +'","user_id":"' + user_id + '","vehicle_id":"' + vehicle_id + '"}';
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			console.log(data.resultStatus);
			if(data.resultStatus == "0") {
				mui.toast(data.resultDesc);
			} else if(data.resultStatus == "1"){				
				mui.toast(data.resultDesc);
			}else if(data.resultStatus == "3") {			
				mui.toast(data.resultDesc);
				window.location.reload();
			} else if(data.resultStatus == "4"){
				mui.toast(data.resultDesc);
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//结束装卸货
function exeEndLoadAndUnload() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAVehiclePointService';
	var method = "exeEndLoadAndUnload";
	var params = '{"seg_no":"' + seg_no + '","allocate_vehicle_id":"' + allocate_vehicle_id +'","user_id":"' + user_id + '","vehicle_id":"' + vehicle_id + '"}';
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			console.log(data.resultStatus);
			if(data.resultStatus == "0") {
				mui.toast(data.resultDesc);
			} else if(data.resultStatus == "1"){				
				mui.toast(data.resultDesc);
			}else if(data.resultStatus == "2") {			
				mui.toast(data.resultDesc);
				window.location.reload();
			} else if(data.resultStatus == "3"){
				mui.toast(data.resultDesc);
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}