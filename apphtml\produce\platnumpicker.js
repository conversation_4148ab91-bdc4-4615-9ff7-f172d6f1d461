var pickedPlat;
//选中的垛位号
var pickedPlatNumCode;

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false,
});

//获取传递的参数
mui.plusReady(function() {
	var self = plus.webview.currentWebview();
	pickedPlat = self.pickedPlat;
	showPlatNumList();
});

//垛位号选中事件
mui(document.body).on('selected', '#plat-ul', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	pickedPlatNumCode = el_J.find("#ul_item_name").text();
	console.log("垛位号选择：" + pickedPlatNumCode);
});

//提交事件
mui(document.body).on('click', '#commit_btn', function(e) {

	if (pickedPlatNumCode == null || pickedPlatNumCode == '') {
		mui.alert("请选择垛位号");
		return;
	}

	var prePage = plus.webview.getWebviewById("produce_menus");
	mui.fire(prePage, 'onPlatNumPicked', {
		pickedPlatNum: pickedPlatNumCode
	});

	//关闭子页面
	mui.back();
});

/**
 * 展示垛台号列表
 */
function showPlatNumList() {
	var innerHtml = '';
	for (var index = 1; index < 9; index++) {
		var platNum = pickedPlat + index;
		innerHtml = innerHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<label id="ul_item_name">' + platNum + '</label>' +
			'</a>' +
			'</li>';
	}

	$("#plat-ul").html(innerHtml);
}
