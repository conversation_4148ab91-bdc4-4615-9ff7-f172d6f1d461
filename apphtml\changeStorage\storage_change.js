/**
 * 初始化变量信息  
 */
var successFlag = true;
var if_judge_pack_location = "0"; //库位开关 
var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
var location_id = "";
var storage_location_switch = "";
var reallocLocationListHtml = ""; //推荐库位下拉列表
var locationAry = new Array();
var realloc_location = "";
var global_bale_count =0;//定义全局捆包数，默认为0
//var scanPackList = new Array();
//scanPackList[0] = {"pack_id":"PWB18081009001","product_id":"","scan_time":"2018-08-09 20:23:35","new_location_desc":"2RA21001","location_desc":"","loc_view_id":""};
//initData();
$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});

	//库位输入框获得焦点
	$("#pack_location_m")[0].focus();

});

//页面加载事件
window.onload = function onload() {
	mui.plusReady(function() {
		if(segNo != '00115') { //add by penglei gqg隐藏库位推荐 2018-10-16 13:39:45
			document.getElementById('cpsm').classList.add('mui-hidden');
		}
		//原库位显示库位名称 1/是  
		storage_location_switch = getSwitchValue(segNo, 'SHOW_LOCATION_NAME');
		if(segNo == '00138') { //add by penglei gqg隐藏库位推荐 2018-10-16 13:39:45
			$("#storage_realloc").css("display", "none");
		}
		queryConfigPDAVoucherCountMax();
	});
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	if(scanPackList == null || scanPackList == "") {
		var ws = plus.webview.currentWebview();
		plus.webview.close(ws);
		var w = plus.webview.getWebviewById('index');
		w = plus.webview.create('../public/index.html', 'index');
		plus.webview.show(w);
//		mui.openWindow({
//			url: '../public/index.html',
//			id: 'index',
//			createNew: false
//		});
	} else {
		var btnArray = ['退出', '取消'];
		mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			if(e.index == 0) {
				setTimeout(function() {
					var ws = plus.webview.currentWebview();
					plus.webview.close(ws)
				}, 0);
			}
		}, 'div');
	}
};

function deleteLi(ele, productId, packId) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '提示', btnArray, function(e) {
		if(e.index == 0) {
			li.parentNode.removeChild(li); //删除DOM节点，
			//但是同时也要删除数据容器
			for(var i = 0; i < scanPackList.length; i++) {
				if(scanPackList[i].product_id == productId && scanPackList[i].pack_id == packId) {
					scanPackList.splice(i, 1);
					break;
				}
			}
			global_bale_count = document.getElementById('storage_list').getElementsByTagName('li').length; 
            $("#bale_count").html(global_bale_count);
			initData();
		}
	}, 'div');
}

function initData() {
	$("#storage_list li").remove();
	//var lihtml ="";
	console.log(">>>>>>scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
	//scanPackList = (scanPackList.reverse());
	scanPackList = scanPackList.reverse();
	var loca = "";
	$.each(scanPackList, function(i, item) {
		if(storage_location_switch == "1") {
			$("#pack_location_d").val(item.location_name);
			var li = $(' <li class="mui-table-view-cell mui-media li-text">' +
				' <div class="mui-slider-right mui-disabled">' +
				' <a class="mui-btn mui-btn-red" onclick=' + 'deleteLi(this,"' + item.product_id + '","' + item.pack_id + '")' + '>删除</a>' +
				' </div>' +
				' <div class="mui-slider-handle">' +
				' <div  class="storage_pack mui-pull-left">' +
				'<span id="packLabel">捆</span><label>' + item.pack_id + '</label>' +
				' </div>' +
				' <div class="mui-media-body li-height">' +
				' <div class="pack_location_target">' +
				'<span id ="newLocationLabel">新</span><label>' + item.new_location_desc + '</label>' +
				'</div>' +
				' <div class="pack_location_now">' +
				'<span id ="oldLocationLabel">原</span><label>' + item.location_name + '</label>' +
				'</div> ' +
				' </div>' +
				' </div>' +
				' <div style="display:none">' +
				' <label id="product_id">' + item.product_id + '</label>' +
				' </div>' +
				' </li>');
		} else {
			$("#pack_location_d").val(item.location_desc);
			var li = $(' <li class="mui-table-view-cell mui-media li-text">' +
				' <div class="mui-slider-right mui-disabled">' +
				' <a class="mui-btn mui-btn-red" onclick=' + 'deleteLi(this,"' + item.product_id + '","' + item.pack_id + '")' + '>删除</a>' +
				' </div>' +
				' <div class="mui-slider-handle">' +
				' <div  class="storage_pack mui-pull-left">' +
				'<span id="packLabel">捆</span><label>' + item.pack_id + '</label>' +
				' </div>' +
				' <div class="mui-media-body li-height">' +
				' <div class="pack_location_target">' +
				'<span id ="newLocationLabel">新</span><label>' + item.new_location_desc + '</label>' +
				'</div>' +
				' <div class="pack_location_now">' +
				'<span id ="oldLocationLabel">原</span><label>' + item.location_desc + '</label>' +
				'</div> ' +
				' </div>' +
				' </div>' +
				' <div style="display:none">' +
				' <label id="product_id">' + item.product_id + '</label>' +
				' </div>' +
				' </li>');
		}

		$("#storage_list").append(li);
		global_bale_count = document.getElementById('storage_list').getElementsByTagName('li').length; 
        $("#bale_count").html(global_bale_count);
	});
	
	    //
		var isActive = document.getElementById("mySwitch").classList.contains("mui-active");
		if(isActive){
			console.log("打开状态");		 
		}else{
			console.log("关闭状态");  
			$("#pack_id").val("");
			$("#pack_location_m").val("");
			$("#pack_location_m")[0].focus();
		}
	
}

//增加keypress监听
//库位
var pack_location_target = "";
$("#pack_location_m").keypress(function(e) {
	if(e.keyCode == 13) {

		if($("#pack_location_m").val() == "" || $("#pack_location_m").val() == null) {
			mui.alert("请扫描或者输入库位（新）", "提示", "确定", null, "div");
			$("#pack_location_m")[0].focus();
			return;
		}
		if(segNo == '00138') {
			
			if(if_judge_pack_location == "1") { //库位开关打开时，判断库位是否存在
				judgeLocationExist();
			} else {
				//捆包输入框获得焦点
				$("#pack_id")[0].focus();
			}
			// 当捆包为P 或者 R开头
			var pack_id = $("#pack_id").val();
					if(pack_id.substr(0, 1) == "P" || pack_id.substr(0, 1) == "R") {
						console.log(pack_id.substr(0, 1));
						//不做判断直接将捆包保存
						queryPackNetNotConnect();
					} else {
						queryLocationChangePack();
					}
		} else {
			var pack_id = $("#pack_id").val();
			if(pack_id != null && pack_id != "") {
				if(if_judge_pack_location == "1") { //库位开关打开时，判断库位是否存在
					judgeLocationExist();
				}
				pack_id = formatPackId(pack_id);
				pack_location_target = $("#pack_location_m").val().trim();
				scanPackList = [];
				if(segNo == "00138") { //高强钢判断捆包是否合法   penglei
					// 当捆包为P 或者 R开头
					if(pack_id.substr(0, 1) == "P" || pack_id.substr(0, 1) == "R") {
						console.log(pack_id.substr(0, 1));
						//不做判断直接将捆包保存
						queryPackNetNotConnect();
					} else {
						queryLocationChangePack();
					}
				} else {
					queryLocationChangePack();
				}

			} else {
				//捆包输入框获得焦点
				$("#pack_id")[0].focus();
			}
		}
	}
});

$("#pack_id").focus(function(e) {
	if(segNo == "00138") { //add by penglei gqg必须输入库位 2018-10-16 13:38:05
		var pack_location_m = $("#pack_location_m").val();
		if(pack_location_m == "" || pack_location_m == null) {
			//mui.alert("请扫描或者输入库位（新）","提 示","确定",null,'div');
			$("#pack_id").val("");
			$("#pack_location_m").focus();
			return false;
		}
	}
});

//已扫描捆包列表
var scanPackList = new Array();
$("#pack_id").keypress(function(e) {
	console.log(123)
	if(e.keyCode == 13) {
		//判断捆包是否已经被扫描
		var pack_id = $("#pack_id").val();
		if(pack_id == "") {
			mui.alert("扫描或手工输入的捆包号不能为空。", "提 示", "确定", null, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		console.log(pack_id)
		pack_id = formatPackId(pack_id);
		//add by xuhuaijun 20180925 武钢拼焊
		pack_id = trimPackId(pack_id);
		$("#pack_id").val(pack_id);
		if(segNo == "00138" || segNo == "00119" || segNo == "00125") { //add by penglei gqg控制
			if(packIfAlreayScaned(pack_id)) {
				mui.alert("捆包已被扫描，不能重复操作。", "提 示", "确定", null, 'div');
				$("#pack_id").val("");
				$("#pack_id")[0].focus();
				return false;
			}
		}
		//已扫描列表追加一条捆包记录
		console.log(pack_id.substr(0, 1));
		pack_location_target = $("#pack_location_m").val().trim();
		if(segNo == "00138") { //高强钢判断捆包是否合法   penglei
			// 当捆包为P 或者 R开头
			if(pack_id.substr(0, 1) == "P" || pack_id.substr(0, 1) == "R") {
				console.log(pack_id.substr(0, 1));
				//不做判断直接将捆包保存
				queryPackNetNotConnect();
			} else {
				queryLocationChangePack();
			}
		} else {
			queryLocationChangePack();
		}
	}
});

function packIfAlreayScaned(pack_id) {
	var result = false;
	$.each(scanPackList, function(i, value) {
		//console.log("数组 pack_id=========="+value.pack_id+",当前扫描pack_id========="+pack_id); 
		if(value.pack_id == pack_id) {
			result = true;
			return; //跳出循环  return true 等价于  continue   
		}
	});
	return result;
};

function queryLocationChangePack() {
	mui.plusReady(function() {
		var curNetConnetType = plus.networkinfo.getCurrentType();
		if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
			curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
			queryPackNetNotConnect();
		} else {
			queryPackNetConnect();
		}
	});
};

//无网络连接时，只记录扫描捆包号
function queryPackNetNotConnect() {
	var pack_id = $("#pack_id").val();
	$("#pack_id").val("");
	$("#pack_id").focus();
	var curPackObj = {
		pack_id: pack_id,
		product_id: "",
		scan_time: getnowtime(),
		new_location_desc: pack_location_target, //location_id,
		location_desc: "",
		location_name: ""
	};
	/*var i = 0 ;
	while(i<1000){		      
		scanPackList.push(curPackObj);
		i++;
	}*/
	scanPackList.push(curPackObj);
	initData();
};

//有网络环境时，调用接口获取捆包信息
function queryPackNetConnect() {
	<!-- 查询前先关闭软键盘-->
	//document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + $("#pack_id").val() + '"}';
	var method = "exeQueryPackInfo";
	//console.log("wervice>>>>>>"+webServiceUrl);
	//console.log("params>>>>>>>>>"+params);
	/*$.getJSON(outUri, {
					innerUri: innerUri,
					params: params,
					method: method
				}, function(data) { //如返回对象有一个username属性 
					if(data != null) {
						if(data.packList.length == 0) {
							mui.alert("未查询到对应的捆包信息", "提示", "确定", function() {
								$("#pack_id").val("");
								$("#pack_location_d").val("");
								$("#pack_id").focus();
							}, 'div');
							return;
						} else {
							$("#pack_id").val("");
							$("#pack_id").focus();
							var scan_time = getnowtime();
							$.each(data.packList, function(i, item) {
								console.log("item"+item);
		//						var j=0;
		//					while(j<=100){
							   scanPackList.push({pack_id:item.pack_id,product_id:item.product_id,scan_time:scan_time,new_location_desc:pack_location_target,location_desc:item.location_desc});
		//					       j++; 
		//					}
							  initData();
							});
						}
					}else { //连接失败
						mui.alert("连接服务器异常", "提ote示", "确定", null, 'div');
						return;
					}
				});*/

	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 4000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data != null) {
				if(data.packList.length == 0) {
					if(segNo == "00138") { //高强钢查询不到对应捆包
						mui.alert("捆包号不合法", "提示", "确定", function() {
							$("#pack_id").val("");
							$("#pack_location_d").val("");
							$("#pack_id").focus();
						}, 'div');
						return;
					}
					var pack_id = $("#pack_id").val();
					$("#pack_id").val("");
					$("#pack_id").focus();
					var packObj = {
						pack_id: pack_id,
						product_id: "",
						scan_time: getnowtime(),
						new_location_desc: pack_location_target, //location_id,//
						location_desc: "",
						loc_view_id: "",
						location_name: ""
					};
					scanPackList.push(packObj);
					initData();
				} else {
					if(pack_location_target == null || pack_location_target == "") {
						$.each(data.packList, function(i, item) {
							if(storage_location_switch == "1") {
								$("#pack_location_d").val(item.location_name);
							} else {
								$("#pack_location_d").val(item.location_desc);
							}
							var packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: item.location_desc,
								loc_view_id: item.loc_view_id,
								location_name: item.location_name
							};
							scanPackList.push(packObj);
							initData();
							console.log("1111111111111111111111" + JSON.stringify(scanPackList));
						});
						plus.nativeUI.toast("请手工指定或推荐目标库位");
						$("#pack_location_m").focus();
					} else {
						//$("#pack_id").val("");
						$.each(data.packList, function(i, item) {
							var packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: item.location_desc,
								loc_view_id: item.loc_view_id,
								location_name: item.location_name
							};
							scanPackList.push(packObj);
							initData();
						});
					}
					console.log("22222222222222222222222" + segNo);
					if(segNo != "00106") {
						$("#pack_id").val("");
					}
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if(textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});

};

//库位推荐事件
mui(document.body).on('tap', '#storage_realloc', function() {
	document.activeElement.blur();
	console.log(">>>>>>scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
	if(scanPackList == null || scanPackList == "") {
		mui.alert("已扫描列表中无捆包记录", "提示", "确定", null, 'div');
		return false;
	}
	if(scanPackList.length > 1) {
		mui.alert("库位推荐限制操作单个捆包！", "提 示", "确定", null, 'div');
	}

	var reallocParams = {};
	$.each(scanPackList, function(i, item) {
		reallocParams.pack_id = item.pack_id;
		reallocParams.product_id = item.product_id;
		reallocParams.pack_location = item.new_location_desc;
		reallocParams.old_pack_locaiton = item.location_desc
		reallocParams.loc_view_id = item.loc_view_id;
	});

	if(reallocParams.loc_view_id == null || reallocParams.loc_view_id == "") {
		mui.alert("手工指定库位不可再做库位推荐", "提示", "确定", function() {}, "div");
		return false;
	}
	console.log(">>>>>>reallocParams:" + JSON.stringify(reallocParams) + ">>>>>>>>>>>>");
	if(reallocParams.pack_location == reallocParams.old_pack_locaiton) {
		mui.confirm('确定是否推荐库位？', '提示', ['确认', '取消'], function(e) {
			if(e.index == 0) {
				exeReallocLocation(reallocParams);
			} else {
				return;
			}
		}, 'div');
	} else {
		mui.confirm('是否重新推荐库位？', '提示', ['确认', '取消'], function(e) {
			if(e.index == 0) {
				exeReallocLocation(reallocParams);
			} else {
				return;
			}
		}, 'div');
	}

	//mui("#storage_realloc").button("loading");
	//$("#overlay").addClass("overlay");

});

//按钮绑定事件
mui(document.body).on('tap', '#storage_but', function() {
	console.log("目标库位pack_location_target" + pack_location_target);
	pack_location_target = scanPackList[0].new_location_desc;
	if(pack_location_target == null || pack_location_target == "") {
		mui.alert("已扫描列表中无捆包记录", "提 示", "确定", null, 'div');
		return false;
	}

	// note by lal 2018-9-10 16:19:03 
	// 捆包校验  缺少label_id 先不校验，后续修改
	/*
				var pack_id = $("#pack_id").val();
				if(pack_id!=""){
					pack_id = formatPackId(pack_id);
	        		$("#pack_id").val(pack_id);
	        		console.log(">>>>>>>>>>>>>>>123123" + JSON.stringify(scanPackList));
					if(!packIfAlreayScaned(pack_id)){
						mui.alert("手工输入的捆包号,须手工按回车键","提 示","确定",null,'div');
						return false;
				    }
				}
				*/

	if(scanPackList == null || scanPackList == "") {
		mui.alert("已扫描列表中无捆包记录", "提示", "确定", null, 'div');
		return false;
	}
	mui("#storage_but").button("loading");
	$("#overlay").addClass("overlay");
	exePackLocationChangeUpload();
});

/*
 * 修改了超时请求的方式 170922 wangshengbo
 */
function exePackLocationChangeUpload() {
	// 查询前先关闭软键盘-->
	//startTime = new Date();变量没用到，屏蔽掉170922 wangshengbo
	/*部分机型出现请求超时的现状，这里使用定时主要是为了实现
	 如果这个方法请求时间超过10秒就停止上传，避免出现点击上传后页面就一直转圈的情况
	 */
	/*successFlag = false;
				if(scanPackList.length>100){
				 setTimeout(function(){
					if(!successFlag){
						myAjaxXHR.abort();
						successFlag=true;
				       	mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
						mui("#storage_but").button("reset");
				        $("#overlay").removeClass("overlay");
					}
			     },30000);
				}else{
				 setTimeout(function(){
					if(!successFlag){
						myAjaxXHR.abort();
						successFlag=true;
				       	mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
						mui("#storage_but").button("reset");
				        $("#overlay").removeClass("overlay");
					 }
					},10000);
				}*/

	document.activeElement.blur();
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	//var outUri = domainName+"webService.jsp?callback=?";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + seg_no + '","user_id":"' + user_id + '","pack_list":' + JSON.stringify(scanPackList) + '}';
	var method = "exePackLocationChangeUpload";
	console.log("params======================="+params);
	var outtime = 10000; //默认超时未10秒 超过100条记录设置为30000
	if(scanPackList.length > 100 && scanPackList.length <= 200) {
		outtime = 30000; //30000
	} else if(scanPackList.length > 200) {
		outtime = 60000; //60000
	} else if (segNo = "00125"){
		outtime = 3000000; 
	}
	try {
		//				$.getJSON(outUri, {
		//					innerUri: innerUri,
		//					params: params,
		//					method: method
		//				}, function(data) {  
		//					successFlag = true;
		//					if(data != null) {
		//						mui("#storage_but").button("reset");
		//				        $("#overlay").removeClass("overlay");
		//						if(data.resultStatus == "1") {
		//							mui.alert("操作成功", "提示", "确定", function() {
		//								//清空本次已扫描捆包列表数据
		//								$("#storage_list").html("");
		//								scanPackList = [];
		//								$("#pack_location_d").val("");
		//							}, 'div');
		//							return;
		//						} else {
		//							mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {
		//							}, 'div');
		//							return; 
		//						}
		//					} else { //连接失败
		//						mui("#storage_but").button("reset");
		//				        $("#overlay").removeClass("overlay");
		//						mui.alert("连接服务器异常", "提示", "确定", function() {
		//						}, 'div');
		//						return;
		//					}
		//				});
		$.ajax({
			type: "post",
			async: true,
			timeout: outtime,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(data) {
				successFlag = true;
				//console.log("data>>>>>>>>>>>>>>>"+data);
				if(data != null) {
					mui("#storage_but").button("reset");
					mui("#storage_realloc").button("reset");
					$("#overlay").removeClass("overlay");
					if(data.resultStatus == "1") {
						var msg = data.resultDesc;
						console.log(msg)
						mui.alert(msg, "提示", "确定", function() {
							//清空本次已扫描捆包列表数据
							$("#storage_list").html("");
							scanPackList = [];
							$("#pack_location_d").val("");
							$("#pack_id").val("");
							$("#pack_location_m").val("");
							global_bale_count = document.getElementById('storage_list').getElementsByTagName('li').length; 
                            $("#bale_count").html(global_bale_count);
						}, 'div');
						return;
					} else {
						mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
						return;
					}
				} else { //连接失败
					mui("#storage_but").button("reset");
					mui("#storage_realloc").button("reset");
					$("#overlay").removeClass("overlay");
					mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
					return;
				}
			},
			error: function(xhr, textStatus, errorThrown) {
				console.log("xhr.readystate>>>>>" + xhr.readyState + "textStatus>>>>>>>>>>>>>" + textStatus);
				mui.plusReady(function() {
					var curNetConnetType = plus.networkinfo.getCurrentType();
					if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
						curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
						// mui.alert("无网络连接。请检查网络后再次上传","提示","确定",function() {}, "div");
						plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
					} else if(textStatus == "timeout") {
						/*if(!successFlag){
							mui.alert("连接服务器异常","提示","确定",function() {}, "div");
						}*/
						xhr.abort();
						mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
					} else {
						mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
					}
					successFlag = true;
					mui("#storage_but").button('reset');
					mui("#storage_realloc").button("reset");
					$("#overlay").removeClass("overlay");
				});
			}
		})
	} catch(e) {
		successFlag = true;
		alert("error:" + e.message);
	}
}
//页面加载时判断库位开关
function queryConfigPDAVoucherCountMax() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","switch_type":"IF_JUDGE_PACK_LOCATION"}';
	var method = "exeConfigPDAvoucherMaxCount";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if(null != data) {
			console.log("switch_con:" + data.switch_con);
			if_judge_pack_location = data.switch_con;
		}
	})
};

//判断库位是否存在
function judgeLocationExist() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_location":"' + $("#pack_location_m").val() + '"}';
	var method = "exeQueryLocation";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if(null != data) {
			console.log("resultStatus:" + data.resultStatus);
			console.log("locationList:" + JSON.stringify(data.locationList));
			if(data.resultStatus == "0") {
				mui.alert("库位不存在", "提示", "确认", null, "div");
				$("#pack_location_m").val("");
				$("#pack_location_m")[0].focus();
				return;
			} else {
				$("#pack_id")[0].focus();
			};
			if(data.resultStatus == "1") {
				if(data.locationList.length > 0) {
					$.each(data.locationList, function(i, item) {
						location_id = item.location_id;
					});
				}
			}
		}
	})
};

function exeReallocLocation(reallocParams) {
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var use_type = "PDA倒库推荐库位";
	var pack_location = "";
	if(reallocParams.pack_location == null || reallocParams.pack_location == "") {
		pack_location = reallocParams.old_pack_locaiton;
	} else {
		pack_location = reallocParams.pack_location;
	}
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + reallocParams.pack_id + '","product_id":"' + reallocParams.product_id + '","use_type":"' + use_type + '","loc_view_id":"' + reallocParams.loc_view_id + '","pack_location":' + JSON.stringify(pack_location) + '}';
	console.log("params:" + JSON.stringify(params));
	params = encodeURI(params, 'utf-8');
	var method = "exeReallocLoacation";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("data:" + JSON.stringify(data));
		if(data != null) {
			if(data.resultStatus == 1) {
				if(data.resultLocation != null && data.resultLocation != "") {
					/**
					 * ERP_54149 推荐3个库位
					 * 执行推荐库位后，从反馈的信息的列表中选择其中的一个，点击确认，再进行更新库位操作
					 */
					/*mui.alert("操作成功", "提示", "确定", function() {}, "div");
					$("#pack_location_m").val(data.resultLocationId);
					if(scanPackList[0].new_location_desc == null || scanPackList[0].new_location_desc == ""){
						scanPackList[0].location_desc = scanPackList[0].location_desc;
					}else{
						scanPackList[0].location_desc = scanPackList[0].new_location_desc;
					}
					scanPackList[0].new_location_desc = data.resultLocationId;
					scanPackList[0].loc_view_id = data.resultLocViewId;
					initData();
					return true;*/

					locationAry = data.resultLocation;
					console.log(JSON.stringify(locationAry));
					//document.activeElement.blur(); 
					loadLocation();
					$("#ReallocLocationDiv").toggleClass('show');

				} else {
					mui.alert("无可用库位信息,请手工指定库位", "提示", "确定", function() {}, "div");
				}
			} else if(data.resultStatus == 0) {
				mui.alert(data.resultDesc, "提示", "确定", function() {}, "div");
				return false;
			} else {
				mui.alert("不满足库位预分配条件,无法分配库位", "提示", "确定", function() {}, "div");
				return false;
			}
		} else {
			mui.alert("连接失败", "提示", "确定", function() {}, "div");
			return false;
		}
	});
}

//-------------------------------------------------------------------------------------------------------------------------

/*//智慧仓库:推荐多个库位
	        mui(document.body).on('tap','#storage_realloc',function(){
	        	document.activeElement.blur(); 
	        	loadLocationProcess();
	        	//setReallocLocationListHtml();
	        	$("#ReallocLocationDiv").toggleClass('show');
	        });
	        
	        //智慧仓库:推荐多个库位
	        function setReallocLocationListHtml(){
	        	var reallocLocationObject = $("#product_process_id");
	        	if(reallocLocationObject.attr("readOnly")!="readonly"){
		        	if(!reallocLocationListHtml){
		        		loadLocationProcess();//需要的时候才加载卷内径下拉框列表
		        	}
		        	$("#ReallocLocationDiv").toggleClass('show');
	        	}
	        }*/

//智慧仓库:推荐多个库位
function loadLocation() {
	for(var idx in locationAry) {
		reallocLocationListHtml = reallocLocationListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label class ="reallocLocationValue">' + locationAry[idx] + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#LocationList").html(reallocLocationListHtml);
}

//智慧仓库:推荐多个库位确认按钮点击事件
mui(document.body).on('tap', '#LocationConfirm', function() {
	if(!realloc_location) {
		mui.alert("请选择精确库位信息", "提示", "确认", function() {}, "div");
		return false;
	} else {
		$("#ReallocLocationDiv").toggleClass('show');
		$("#pack_location_m").val(realloc_location);
		realloc_location = $("#pack_location_m").val();
		console.log(">>>>>>>>>>>>>>>>>>>>>>>realloc_location:" + realloc_location);
		scanPackList[0].new_location_desc = realloc_location;
		//执行库位变更操作
		mui("#storage_realloc").button("loading");
		$("#overlay").addClass("overlay");
		exePackLocationChangeUpload();
		locationAry = new Array();
		reallocLocationListHtml = "";
	}
	$("#pack_id").focus();
	
});

//智慧仓库:推荐多个库位
mui(document.body).on('tap', '#cancel', function() {
	$("#ReallocLocationDiv").toggleClass('show');
	locationAry = new Array();
	reallocLocationListHtml = "";
});

//智慧仓库:推荐多个库位
mui(document.body).on('selected', '#LocationInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el); //dom元素转化成jquery对象
	realloc_location = el_J.find(".reallocLocationValue").text();
	document.activeElement.blur();
	console.log(JSON.stringify(scanPackList));
});