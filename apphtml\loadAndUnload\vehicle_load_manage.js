$(document).ready(function() {
	var segNo = localStorage.getItem("segNo");
	if (segNo != "00126") {
		$("#electric_signature").css("display", "none");
		$("#wait_work_vehicle").css("display", "none"); 
	}
	if (segNo != "00181") {
		$("#fenpei").css("display", "none");
		}
});

/**
 * 初始化变量信息 
 */
var putinPackList = new Array(); //入库捆包列表
var vehicle_id = "";
var car_trace_no = "";
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var hand_point_id = localStorage.getItem("hand_point_id");
var hand_point_name = localStorage.getItem("hand_point_name");
var factory_area_id = localStorage.getItem("factory_area_id");

var putoutVoucherList = new Array(); //车辆信息
var queryVoucherList = new Array(); //查询出的车辆信息

var reason_list = new Array();
var carnum = ""; //弹出层的车牌号
//var queryType ="";//10pda扫描  20手动输入

mui.init({
	swipeBack: false //启用右滑关闭功能
});

window.onload = function onload() {
	mui.plusReady(function() {
		console.log("hand_point_id：" + hand_point_id + ",hand_point_name:" + hand_point_name + ",");
		$("#hand_point_name").val(hand_point_name);

		if (segNo == "00138" || segNo == "00181") {
			$("#leave_unload").show();
		} else if ("00118" == segNo || segNo == '00152') {
			$("#leave_unload").show(); //ERP_62392 显示未装离场按钮
			$("#vehicle_no_display").html("装载车辆");
		}
		var putinScan = plus.webview.getWebviewById('putin_scan');
		var putoutScan = plus.webview.getWebviewById('putout_scan_new');
		var handPointList = plus.webview.getWebviewById('hand_point_list');
		var putoutPackListNew = plus.webview.getWebviewById('putout_pack_list_new');
		plus.webview.close(putinScan);
		plus.webview.close(putoutScan);
		plus.webview.close(handPointList);
		plus.webview.close(putoutPackListNew);
		queryVehicleId(10);
	});

}

$("#vehicle_id").keypress(function(e) {
	if (e.keyCode == 13) {
		if ($("#vehicle_id").val() == null || $("#vehicle_id").val() == "") {
			alert("请输入车牌号");
			return false;
		}
		//queryVehicleId(20);
	}
});
//进入装卸点选择界面
mui(document.body).on("tap", ".icon-setting", function() {
	mui.openWindow({
		url: 'select_hand_point.html',
		id: 'select_hand_point',
		extras: {
			openType: 'vehicle_load_manage',
		},
		createNew: true
	});
});
/*
 * 开始装货
 * 
 */
mui(document.body).on('tap', '#start_load', function() {
	var vehicle_id = $("#vehicle_id").val();
	if (hand_point_id == null || hand_point_id == "") {
		mui.toast("请输入装卸点");
		return false;
	}
	if (vehicle_id == null || vehicle_id == "") {
		mui.toast("请输入车牌号");
		return false;
	}
	localStorage.setItem("vehicle_id", vehicle_id);
	localStorage.setItem("car_trace_no", $("#car_trace_no").val());
	localStorage.setItem("check_type", "10");
	localStorage.setItem("allocate_vehicle_id", $("#allocate_vehicle_id").val());
	localStorage.setItem("allocate_scope", $("#allocate_scope").val());
	var type = "start_load"; //start_load开始装货   start_unload 开始卸货
	exeConfigVehicleLoadGood(type);
	/*mui.openWindow({
						url:'hd_putout_scan.html',
						id:'hd_putout_scan',
						extras:{
							openType:'vehicle_load_manage',
						},
						createNew:true
						});*/
});
/*
 * 开始卸货
 */
mui(document.body).on('tap', '#start_unload', function() {
	var vehicle_id = $("#vehicle_id").val();
	if (hand_point_id == null || hand_point_id == "") {
		mui.toast("请输入装卸点");
		return false;
	}
	if (vehicle_id == null || vehicle_id == "") {
		mui.toast("请输入车牌号");
		return false;
	}

	localStorage.setItem("vehicle_id", vehicle_id);
	localStorage.setItem("car_trace_no", $("#car_trace_no").val());
	localStorage.setItem("check_type", "20");
	localStorage.setItem("allocate_vehicle_id", $("#allocate_vehicle_id").val());
	localStorage.setItem("allocate_scope", $("#allocate_scope").val());
	var type = "start_unload";
	exeConfigVehicleLoadGood(type);

});
/*
 * 结束装卸货
 */
mui(document.body).on('tap', '#end_load', function() {
	var vehicle_id = $("#vehicle_id").val();
	if (hand_point_id == null || hand_point_id == "") {
		mui.toast("请输入装卸点");
		return false;
	}
	if (vehicle_id == null || vehicle_id == "") {
		mui.toast("请输入车牌号");
		return false;
	}
	mui.openWindow({
		url: 'hand_point_end.html',
		id: 'hand_point_end',
		createNew: true,
	});
});
//查询车牌号
function queryVehicleId(queryType) {
	console.info("查询车牌号。。。。。。。。。");
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	if (queryType == '20') {
		vehicle_id = $("#vehicle_id").val();
	} else {
		vehicle_id = "";
	}
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","hand_point_id":"' + hand_point_id + '"}';
	var method = "exeQueryVehicleId";
	console.log("queryVehicleId:"+params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
	console.log("查询车牌返回"+JSON.stringify(data));
		if (data != null) {
			$("#vehicle").html(" ");
			if (data.codeList.length == 0) {
				//mui.alert("该装卸点下未查询到车牌号信息。", "提示", "确定", function() {
				$("#vehicle_id").val("");
				$("#vehicle_id").focus();
				//}, 'div');
				return;
			} else {
				var html = "";
				if (queryType == '20') {
					$.each(data.codeList, function(i, item) {
						/*html=html+'<button type="button" class="mui-btn"  p_id="'+item.vehicle_no+'" p_no="'+item.car_trace_no+'">'+item.vehicle_no+'</button>';*/
						$("#vehicle_id").val(item.vehicle_no);
						//缓存车牌号
						cacheVehivle();
						$("#car_trace_no").val(item.car_trace_no);
					});
				} else {
					if (data.codeList.length > 0 && segNo == "00126") { //佛山宝钢个性化，要求页面加载将车辆信息填充在车牌号上
						console.log(data.codeList.length + ">>>>>>>>>>>>>>>>>>>>>>>" + data.codeList[0].vehicle_no);
						$("#vehicle_id").val(data.codeList[0].vehicle_no);
						$("#allocate_vehicle_id").val(data.codeList[0].allocate_vehicle_id);
						$("#allocate_scope").val(data.codeList[0].allocate_scope);
						$("#car_trace_no").val(data.codeList[0].car_trace_no);
						$("#check_type").val(data.codeList[0].check_type);
						if ($("#check_type").val() == "10") { //装货
							$("#start_unload").hide();
							$("#start_load").show();
						} else if ($("#check_type").val() == "20") { //卸货
							$("#start_load").hide();
							$("#start_unload").show();
						} else {
							$("#start_load").show();
							$("#start_unload").show();
						}
					}
					$.each(data.codeList, function(i, item) {
						html = html + '<button type="button" class="mui-btn" style="width:31%;margin:1px ;" p_id="' + item.vehicle_no +
							'" p_no="' + item.car_trace_no + '" p_check="' + item.check_type + '" p_allv="' + item.allocate_vehicle_id +
							'">' + item.vehicle_no + '</button>';
					});
				}
				$("#vehicle").html(html);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//显示的已入厂车辆赋值
mui(document.body).on('tap', '#vehicle button', function() {
	$('#vehicle_id').val($(this).text());
	//缓存车牌号
	cacheVehivle();
	$("#car_trace_no").val($(this).attr("p_no"));
	$("#check_type").val($(this).attr("p_check"));
	$("#allocate_vehicle_id").val($(this).attr("p_allv"));
	$("#allocate_scope").val($(this).attr("p_scope"));
	if ($("#check_type").val() == "10") { //装货
		$("#start_unload").hide();
		$("#start_load").show();
	} else if ($("#check_type").val() == "20") { //卸货
		$("#start_load").hide();
		$("#start_unload").show();
	} else {
		$("#start_load").show();
		$("#start_unload").show();
	}
});
//开始装卸货
//type start_unload开始卸货      start_load 开始装货
function exeConfigVehicleLoadGood(type) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = "";
	var innerUri = "";
	var vehicle_id = "";
	var car_trace_no = "";
	//如果当前业务编码属于佛山宝钢，则改变请求参数
	if (segNo == "00126") {
		outUri = domainName + "webService.jsp?callback=?";
		innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
		vehicle_id = $("#vehicle_id").val();
		car_trace_no = $("#car_trace_no").val(); //车辆跟踪号

		var params = JSON.stringify({
			seg_no: segNo,
			user_id: user_id,
			vehicle_id: vehicle_id,
			car_trace_no: car_trace_no,
			hand_point_id: hand_point_id
		});
		var method = "exeVehicleLoadAndUnload";
		params = encodeURI(params, 'utf-8');
	} else {
		outUri = domainName + "webService.jsp?callback=?";
		innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
		car_trace_no = $("#car_trace_no").val();
		params = '{"seg_no":"' + segNo + '","user_name":"' + user_id + '","car_trace_no":"' + car_trace_no +
			'","hand_point_id":"' + hand_point_id + '"}';
		method = "exeVeicleStartHandPoint";
		params = encodeURI(params, 'utf-8');
	}
	console.log(method + "->params:" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("method返回：" + JSON.stringify(data));
		if (data != null) {
			if (data.resultStatus == "1") {
				if (type == "start_load") {
					//高强钢采用以前的
					if (segNo == '00138' || segNo == '00181') {
						mui.openWindow({
							url: '../putout/putout_list_new.html',
							id: 'putout_list_new',
							extras: {
								openType: 'vehicle_load_manage',
							},
							createNew: true
						});
					} else if (segNo == '00118' || segNo == '00152') {
						mui.openWindow({
							url: 'EDS_putin_putout.html',
							id: 'hd_putout_scan',
							extras: {
								openType: 'vehicle_load_manage',
							},
							createNew: true
						});
					} else if (segNo == "0000") {
						mui.openWindow({
							url: '../putout/putout_list_new.html',
							id: 'putout_list_new',
							extras: {
								openType: 'vehicle_load_manage',
							},
							createNew: true
						});
					} else {
						mui.openWindow({
							url: 'hand_point_list.html',
							id: 'hand_point_list',
							createNew: true,
							show: {
								autoShow: false
							}
						});
					}
				} else {
					if (segNo == '00118' || segNo == '00152') {
						mui.openWindow({
							url: 'EDS_putin_putout.html',
							id: 'hd_putout_scan',
							extras: {
								openType: 'vehicle_load_manage',
							},
							createNew: true
						});
					} else {
						mui.openWindow({
							url: '../putin/putin_scan.html',
							id: 'putin_scan',
							extras: {
								openType: 'vehicle_load_manage',
							},
							createNew: true
						});
					}
				}
				return;
			} else {
				var errorinfo = data.resultDesc;
				mui.alert("失败！原因：" + errorinfo, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
mui(document.body).on('tap', '#back', function() {
	mui.back();
});
mui.back = function() {
	mui.openWindow({
		id: "load_unload_menus",
		url: "load_unload_menus.html",
		createNew: true
	});
}

//更多按钮
mui(document.body).on('tap', '#morebut', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#car_no").val("");
	//查询车牌号
	queryEnterFacoryVehicleNo();
});

function queryEnterFacoryVehicleNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var vehicle_id = $("#car_no").val();
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","hand_point_id":""}';
	var method = "exeQueryVehicleId";
	console.log("queryEnterFacoryVehicleNo:" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("data:" + JSON.stringify(data));
			if (data.codeList.length == 0) {
				/*mui.alert("未查询到对应的车牌号信息", "提示", "确定", function() {}, 'div');
				return ;*/
			} else {
				var lihtml = "";
				$.each(data.codeList, function(i, item) {
					var pli_thid = "";
					var pli_thname = "";
					if (item.target_hand_point_id == "" || item.target_hand_point_id == null) {

						if (item.current_hand_point_id == "" || item.current_hand_point_id == null) {
							pli_thid = " ";
							pli_thname = " ";
						} else {
							pli_thid = item.current_hand_point_id;
							pli_thname = item.current_hand_point_name;
						}
					} else {
						pli_thid = item.target_hand_point_id;
						pli_thname = item.target_hand_point_name;
					}
					lihtml = lihtml +
						'<li class="mui-table-view-cell" >' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;"  >' +
						'<label><span class="vehicle_num">' + item.vehicle_no + '</span>' +
						'<span class="">&nbsp;&nbsp;&nbsp;' + pli_thname + '</span>' +
						'<span class="peichenum" hidden="hidden">' + item.allocate_vehicle_id + '</span>' +
						'<span class="zxtype" hidden="hidden">' + item.check_type + '</span>' +
						'<span class="genzong" hidden="hidden">' + item.car_trace_no + '</span>' +
						'<span class="p_thid" hidden="hidden">' + pli_thid + '</span>' +
						'<span class="p_scope" hidden="hidden">' + item.allocate_scope + '</span>' +
						'</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#carList").html(lihtml);
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {

	var el = e.detail.el;
	var el_J = $(el);
	var thid = el_J.find(".p_thid").text();
	if (hand_point_id == thid) {
		$("#confirm").attr("disabled", false);
		carnum = el_J.find(".vehicle_num").text();
		peiche = el_J.find(".peichenum").text();
		zxtype = el_J.find(".zxtype").text();
		genz = el_J.find(".genzong").text();
		p_scope = el_J.find(".p_scope").text();
		$("#car_no").val(carnum);
		$("#genzonghao").val(genz);
		$("#peichedanhao").val(peiche);
		$("#zhuangxietype").val(zxtype);
		$("#p_thid").val(thid);
		$("#p_scope").val(p_scope);
		console.log(p_scope);
	} else {
		$("#confirm").attr("disabled", true);
	}
});
//搜索
mui(document.body).on('tap', '#query_button', function() {
	queryEnterFacoryVehicleNo();
});
//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	var thid = $("#p_thid").val();
	if (hand_point_id == thid) {
		$("#vehicle_id").val(carnum);
		//缓存车牌号
		cacheVehivle();
		$("#car_trace_no").val($("#genzonghao").val());
		$("#check_type").val($("#zhuangxietype").val());
		$("#allocate_vehicle_id").val($("#peichedanhao").val());
		$("#allocate_scope").val($("#p_scope").val());
		if ($("#zhuangxietype").val() == "10") {
			$("#start_unload").hide();
			$("#start_load").show();
		} else if ($("#zhuangxietype").val() == "20") {
			$("#start_load").hide();
			$("#start_unload").show();
		} else {
			$("#start_load").show();
			$("#start_unload").show();
		}
	}
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	/* 高强钢要求只提供查看功能就可以了。故此注释掉
	 * */
});

//返回事件
mui(document.body).on('tap', '#backbutton', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');

	/* 高强钢要求只提供查看功能就可以了。故此注释掉
	 * $("#vehicle_id").val(carnum);
	 $("#car_trace_no").val($("#genzonghao").val());
	 $("#check_type").val($("#zhuangxietype").val());
	 $("#allocate_vehicle_id").val($("#peichedanhao").val());
	 if($("#zhuangxietype").val()=="10"){
	 	$("#start_unload").hide();
	 	$("#start_load").show();
	 }else if($("#zhuangxietype").val()=="20"){
	 	$("#start_load").hide();
	 	$("#start_unload").show();
	 }else{
	 	$("#start_load").show();
	 	$("#start_unload").show();
	 }*/
});

//未装离厂
mui(document.body).on('tap', '#leave_unload', function() {
	//三水，花都弹出未装离场车辆信息页面
	if ("00118" == segNo || segNo == '00152') {
		//查询车牌号
		queryEnterFacoryVehicleNo2();
	} else {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var vehicle_id = $("#vehicle_id").val();
		if (vehicle_id == null || vehicle_id == "") {
			mui.toast("请输入车牌号");
			return false;
		}
		if (!confirm("车辆" + vehicle_id + "确定要未装离厂吗？")) {
			return false;
		}
		if (hand_point_id == null || hand_point_id == "") {
			mui.toast("请输入装卸点");
			return false;
		}

		var outUri = domainName + "webService.jsp?callback=?";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
		var vehicle_id = $("#vehicle_id").val();
		var car_trace_no = $("#car_trace_no").val();
		var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","car_trace_no":"' + car_trace_no +
			'","user_id":"' + user_id + '"}';
		var method = "exeLeaveUnloadCar";
		console.log("exeLeaveUnloadCar:" + params);
		params = encodeURI(params, 'utf-8');
		$.getJSON(outUri, {
			innerUri: innerUri,
			params: params,
			method: method
		}, function(data) {
			console.log(data + "123123123123" + JSON.stringify(data));
			//如返回对象有一个username属性
			if (data != null) {
				if (data.resultStatus == "1") {
					mui.alert("操作成功", "提示", "确定", function() {
						$("#vehicle_id").val("");
						queryVehicleId(10);
					}, 'div');

				} else {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						$("#vehicle_id").val("");
						queryVehicleId(10);
					}, 'div');
					return;
				}
			} else {
				mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
				return;
			}
		});
	}
});

function queryEnterFacoryVehicleNo2() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var vehicle_id = $("#car_no").val();
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","hand_point_id":""}';
	var method = "exeQueryVehicleId2";
	console.log("queryEnterFacoryVehicleNo:" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			//console.log("data:" + JSON.stringify(data));
			console.log("data:" + data.codeList.length);
			if (data.codeList.length == 0) {
				mui.alert('没有查询到单据信息', "没有查询到单据信息");
				return false;
			} else {
				queryVoucherList = data.codeList;
				//进入单据选择页面
				mui.openWindow({
					url: 'not_put_off.html',
					id: "not_put_off",
					createNew: true,
					extras: {
						open_from_url: "vehicle_load_manage.html",
						open_from_id: "vehicle_load_manage",
						queryVoucherList: queryVoucherList,
						putoutVoucherList: putoutVoucherList
					}
				});
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

/**
 * 电子签名 
 */
mui(document.body).on('tap', '#electric_signature', function() {
	var vehicle_id = $("#vehicle_id").val();
	var car_trace_no = $("#car_trace_no").val();
	if (vehicle_id == "" || vehicle_id == null) {
		mui.toast("请选择车牌号！");
		return false;
	} else {
		mui.openWindow({
			// url: "signature.html",
			url: "electric_signature.html",
			id: "signature",
			extras: {
				vehicle_id: vehicle_id,
				car_trace_no: car_trace_no,
			},
			createNew: true
		});
	}
});

//等待作业车辆看板
mui(document.body).on('click', '#wait_work_vehicle', function() {
	mui.openWindow({
		url: 'wait_work_vehicle_kanban.html',
		id: "wait_work_vehicle_kanban",
		createNew: true
	});
});

function cacheVehivle() {
	var vehicle = $("#vehicle_id").val();
	if ('' != vehicle) {
		localStorage.setItem("putin_vehicle_no", vehicle);
	}
}
//add by gll 上海不锈出厂物流
mui(document.body).on("tap", "#fenpei", function() { 
mui.openWindow({
		url: '../buxiu/loading_distribution.html',
		id: 'loading_distribution',
		extras: {
			openType: 'vehicle_load_manage',
		},
		createNew: true  
	});
});