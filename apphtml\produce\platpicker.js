//料台状态接口ip配置
var interfaceIpConfig = {
	//华东区域
	'00100': '',
	'00134': '',
	'00135': '',
	'00136': '',
	'00137': '',
	'00138': '',
	'00141': '',
	'00147': '',
	'00153': '',
	'00171': '',

	//南方区域
	'00107': '**************:8082',
	'00111': '**************:8082',
	'00118': '************',
	'00119': '**************:8082',
	'00120': '**************:8082',
	'00121': '**************:8082',
	'00122': '**************:8082',
	'00123': '**************:8082',
	'00124': '**************:8082',
	'00125': '**************:8082',
	'00126': '**************:8082',
	'00159': '**************:8082',
	'00165': '**************:8082',
	'00166': '**************:8082',
	'00172': '**************:8082',

	//西部区域
	'00105': '',
	'00112': '',
	'00114': '',
	'00132': '',
	'00133': '',
	'00149': '',
	'00164': '',
	
	//南昌宝江
	'00116': '************',
	//烟台宝井
	'00131': '************',
	//华中公司
	'00106': '*************',
	//花都宝井
	'00118': '***********',
	//三水宝钢
	'00152': '************',


};
//选中的料台
var pickedChargePlatCode;
var interfaceIp;

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false,
});

//获取传递的参数
mui.plusReady(function() {
	var self = plus.webview.currentWebview();
	var segNo = localStorage.getItem("segNo");
	interfaceIp = interfaceIpConfig[segNo];
	console.log("当前接口ip:" + interfaceIp);

	queryChargePlat();
});

//料台号选中事件
mui(document.body).on('selected', '#plat-ul', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	pickedChargePlatCode = el_J.find("#ul_item_name").text();
	console.log("料台选择：" + pickedChargePlatCode);
});

//提交事件
mui(document.body).on('click', '#commit_btn', function(e) {

	if (pickedChargePlatCode == null || pickedChargePlatCode == '') {
		mui.alert("请选择料台");
		return;
	}
	var segNo = localStorage.getItem("segNo");
	if(segNo == "00152" || segNo == "00112"){
		onBack();
	}
	else{
	 queryPlatStatus();
	}
});

/**
 * 查询料台
 */
function queryChargePlat() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no
	};
	params = JSON.stringify(params);
	var method = "queryChargePlat";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log(method + "---返回：" + JSON.stringify(result));
			if (result != null) {
				if (result.returnValue == 1 &&
					getJsonLength(result.chargePlatList) > 0) {
					//查询到数据
					chargePlatList = result.chargePlatList;
					showChargePlatList();
				} else {
					chargePlatList = null;
					showChargePlatList();
					mui.alert(result.wtlPackInfoErrorDetai);
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			chargePlatList = null;
			showChargePlatList();
			mui.alert("料台数据查询失败");
		}
	})
};

function showChargePlatList() {
	var innerHtml = '';
	if (chargePlatList != null) {
		for (var index in chargePlatList) {
			innerHtml = innerHtml +
				'<li class="mui-table-view-cell">' +
				'<a class="mui-navigate-right">' +
				'<label id="ul_item_name">' + chargePlatList[index].codeDesc + '</label>' +
				'</a>' +
				'</li>';
		};
	}

	$("#plat-ul").html(innerHtml);
}

/**
 * 查询料台
 */
function queryPlatStatus() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据

	<!-- 查询前先关闭软键盘-->
	var outUri = 'http://' + interfaceIp + '/ot/getStackNoStatus';
	console.log("请求路径：" + outUri);
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		data: {},
		dataType: "json",
		success: function(result) {
			console.log("获取料台状态：" + JSON.stringify(result));
			if (result != null &&
				result.flag == 1 &&
				result.result != null) {
				console.log("获取料台状态：" + JSON.stringify(result));
				var platArray = result.result;
				for (var i in platArray) {
					var plat = platArray[i];
					if (plat.packStackPosition == pickedChargePlatCode) {
						//占用状态
						if (plat.status == 1) {
							var message = pickedChargePlatCode + '料台号是占用状态是否强制解锁？';
							mui.confirm(message, '', ['是', '否'], function(e) {
								if (e.index == 0) {
									onBack();
								}
							});
						} else {
							onBack();
						}


					}
				}

			} else {
				mui.alert("料台状态查询失败");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			chargePlatList = null;
			showChargePlatList();
			mui.alert("料台状态查询失败");
		}
	})
};

function onBack() {
	//ERP_64963 
	var segNo = localStorage.getItem("segNo");
	var putin_method_switch = getSwitchValue(segNo, 'PDA_PICKED_PLAT_NUM');
	if(putin_method_switch == 1){
		console.log(segNo);
		var prePage = plus.webview.getWebviewById("produce_menus");
		mui.fire(prePage, 'pickedChargePlatCode', {
			pickedPlat: pickedChargePlatCode
		});
	}else{
		var prePage = plus.webview.getWebviewById("produce_menus");
		mui.fire(prePage, 'onPlatPicked', {
			pickedPlat: pickedChargePlatCode
		});
	}
	//关闭子页面
	mui.back();
}

function getJsonLength(jsonData) {
	var jsonLength = 0;
	for (var item in jsonData) {
		jsonLength++;
	}
	return jsonLength;
}
