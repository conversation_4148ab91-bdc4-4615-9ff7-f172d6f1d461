var checkId = "";
var checkType = "";
var webServiceUrl;
var segNo;
var userName;

$(document).ready(function() {
	initParms();
	swipeBack(true);
});

function initParms() {
	checkId = localStorage.getItem("checkId");
	checkType = localStorage.getItem("checkType");
	webServiceUrl = localStorage.getItem("webServiceUrl");
	segNo = localStorage.getItem("segNo");
	userName = localStorage.getItem("account");
	if ('4' == checkType) {
		$("#checkTitle").html("无单盘库");
	}
	$("#checkId").html(checkId);
}
/**捆包下载
 */
function downLoadPack() {
	$("#pipt").html("正在下载,请稍等");
	$("#overlay").addClass("overlay");
	var outUri = domainName + "fileDownload.jsp?callback=?";
	var innerUri = "http://" + webServiceUrl + "/sm/ws/PDACheckStockService";
	var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","check_id":"' + checkId + '","client_id":"1"}';
	var method = "exeCheckStockPackDownload";
	//alert(outUri+"\n"+innerUri+"\n"+params);
	console.log("exeCheckStockPackDownload->param:" + params);
	console.log("exeCheckStockPackDownload->innerUri:" + innerUri);
	console.log("exeCheckStockPackDownload->outUri:" + outUri);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
		console.log("捆包下载exeCheckStockPackDownload返回：" + JSON.stringify(data.status)+" 捆包个数"+data.packList.length);
		$("#pipt").html("");
		if (null != data) {
			//			console.log(data.status);
			// console.log("捆包下载exeCheckStockPackDownload返回：" + JSON.stringify(data.packList));
			//连接成功
			if (data.status == '1') { //读取数据成功
				if (null != data.packList) {
					localStorage.setItem("packList", JSON.stringify(data.packList));
					// localStorage.setItem("packData", JSON.stringify(data));
					localStorage.setItem("packDataStatus", data.status);
					message = "下载完成" + data.packList.length + "个捆包";
					localStorage.setItem("downLoadCount", data.packList.length);
					localStorage.setItem("uploadingCount", "0");
					localStorage.setItem("uploadedCount", "0");
					localStorage.setItem("recentPackId", '无');
					$("#overlay").removeClass("overlay");
					//setTimeout('location.href="check_scanning.html"', 2000); //这里跳转到盘库页面
					mui.openWindow({
						url: 'check_scanning.html',
						id: 'check_scanning',
						createNew: true
					});
				} else {
					message = "末下载到相关数据";
				}
			} else {
				message = "末下载到相关数据";
			}
		} else { //连接失败
			message = "服务器连接异常";
		}
		mui.toast(message);
	});
	
	// $.ajax({
	// 	type: "get", 
	// 	async: true,
	// 	url: outUri,
	// 	data: {
	// 		innerUri: innerUri,
	// 		params: params,
	// 		method: method,
	// 		targetnamespace: 'http://service.pda.gmscservice.baosight.com'
	// 	},
	// 	dataType: "json", 
	// 	success: function(data) {    
	// 		console.log(JSON.stringify(data));
			 
	// 	},
	// 	error: function(XMLHttpRequest, textStatus, errorThrown) {
	// 		console.log(JSON.stringify(XMLHttpRequest));
	// 		console.log(textStatus);
	// 		console.log(errorThrown);
	// 	}
	// });
}

function goBack() {
	if ($("#pipt").html() == "正在下载,请稍等") {
		var btnArray = ['继续', '取消'];
		mui.confirm('操作会取消下载，是否继续？', '警告', btnArray, function(e) {
			if (e.index == 0) {
				mui.doAction('backs'); //返回
			}
		});
	} else {
		mui.doAction('backs');
	}
}

mui.back = function() {
	goBack();
};
