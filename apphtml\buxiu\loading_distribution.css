
.mui-table-view-cell {
	padding: 6px 4px; 
}


/** 按钮样式 */




/** 修改、删除按钮样式 */

.mui-table-view-cell .mui-disabled {
	width: 50%;
}

.mui-table-view-cell .mui-disabled a {
	width: 50%;
	font-size: 20px!important;
	padding-left: 18px!important;
}

.left {
	float: left;
	width: 33%;
	font-size: 42px;
	margin-bottom: 4px;
	color: blue;
	
}
.left2 {
	float: left;
	width: 33%;
	font-size: 42px;
	margin-bottom: 4px;
	color: blue;
	font-size: 18px;
	color: black;
}

#pack_id {
	font-size: 22px;
	margin-bottom: 4px;
	color: blue;
	
}

#factory_product_id,
#spec,
#pack_location,
#weight {
	font-size: 22px;
}

#factory_product_id span {
	background-color: red;
	color: white;
	margin-right: 6px;
}

#spec span {
	background-color: blue;
	color: white;
	margin-right: 6px;
}

#pack_location span {
	background-color: green;
	color: white;
	margin-right: 6px;
}

#weight span {
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}
#vehicle_no,
#start_date,
#hand_point_id,
.qing
 {
	font-size: 18px;
	color: black;
}

#vehicle_no label,
#start_date label
{
	color: black;
	/* margin-left: 10px; */
	text-align: center;
}
#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}
#backbutton {
	width: 45%;
	font-size: 22px;
	line-height: 1.8;
	margin-left: 10px;
	margin-right: 10px;
}

#confirm {
	width: 45%;
	font-size: 22px;
	line-height: 1.8;
	margin-left: 0px;
}
.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}
.mui-navigate-right:after{
				content: '';
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

.vehicle_num {
	color: blue;
	font-size: 22px;
	margin: 25%;
}

.active {
	background-color: #2AC845;
	color: white;
}