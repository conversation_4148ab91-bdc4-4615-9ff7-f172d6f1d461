var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var team_id = localStorage.getItem("team_id");
var class_id = localStorage.getItem("class_id");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var phtml = "";
var length = 0;
var spec = " ";
var partId = " ";
var winccInfoList = new Array();
var ylList = new Array(); //原料捆包信息
var gdList = new Array(); //工单信息
var tlList = new Array(); //投料信息                                                                             
var ccList = new Array(); //产出信息
//工单号
var productionOrderCode = '';

$(function() {
	mui.plusReady(function() {
		var self = plus.webview.currentWebview();
		productionOrderCode = self.productionOrderCode;
		console.log(productionOrderCode)
		var umcLoginName = "admin";
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			umcLoginName: umcLoginName,
			productionOrderCode: productionOrderCode
		};
		params = JSON.stringify(params);
		var method = "queryDetail";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				var phtml = "";
				console.log(JSON.stringify(result.rmPartPackList));
				ylList = result.rmPartPackList;
				tlList = result.rmPartList;
				gdList = result.orderProcessList;
				ccList = result.fmPartList;
				$.each(result.fmPartList, function(i, e) {
					console.log(e.spec)
					console.log(e.partId)
				});
				var phtml1 = "";
				console.log("rmPartPackList="+JSON.stringify(ylList));
				$.each(result.rmPartPackList, function(i, e) {
					console.log(e.businessTypeName)
					if(i == 0) {
						phtml1 = phtml1 + '<li>' +
							'<div class="mui-input-row mui-radio"  style="height: 50px;font-size: 20px;"><br>&nbsp;&nbsp;&nbsp;<span>' + e.packId + '</span>' +
							//'<input id="id1" type="text" disabled="disabled" style="height: 30px;width: 30%;margin-left: 10%;font-size: 20px;"/>' +
							'<input id="check1" name="checkbox1" value="' + e.packId + "," + e.productId + "," + e.productionOrderCode +
							"," + e.partId + "," + e.spec + "," + e.numberNo + '" type="radio" style="margin-top: 10px;"></div></li>';
					} else {
						phtml1 = phtml1 + '<li style="margin-top: 10px;">' +
							'<div class="mui-input-row mui-radio" style="height: 50px;font-size: 20px;"><br>&nbsp;&nbsp;&nbsp;<span>' + e.packId + '</span>' +
							//'<input id="id1" type="text" disabled="disabled" style="height: 30px;width: 30%;margin-left: 10%;margin-top: 10px;font-size: 20px;"/>' +
							'<input id="check1" name="checkbox1" value="' + e.packId + "," + e.productId + "," + e.productionOrderCode +
							"," + e.partId + "," + e.spec + "," + e.numberNo + '" type="radio" style="margin-top: 10px;"></div></li>';
					}
				});
				$("#phtml1").html(phtml1);
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	});
})

mui(document.body).on('tap', '#del', function() {
	var list = document.getElementById('phtml').querySelectorAll('li')
	//list[0].parentNode.removeChild(list[0]);
	list[list.length - 1].parentNode.removeChild(list[list.length - 1]);
	length--;
	if(list.length - 1 == 0) {
		phtml = "";
	}
	console.log(list.length)
});

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//新增捆包
mui(document.body).on('tap', '#add', function() {
	var eachWeight = "";
	$.each(ylList, function(i, e) {
		eachWeight = e.eachWeight;
	});
	var chk_value = new Array();
	$('input[name="checkbox1"]:checked').each(function() {
		var ckval = {};
		ckval.partId = $(this).val().split(",")[3];
		chk_value.push(ckval);
	});
	var productProcessId = "";
	$.each(gdList, function(ix, ex) {
		//console.log(ex.productProcessId)
		productProcessId = ex.productProcessId;
	});
	if(ylList.length == 0) {
		mui.alert("没有投料捆包信息，请先领料！", "提示", "确定", function() {}, "div");
	} else if(chk_value.length == 0) {
		mui.alert("请先勾选投料捆包信息！", "提示", "确定", function() {}, "div");
	} else {
		phtml = $("#phtml").html();
		phtml = '<li class="mui-table-view-cell">' +
			'张数:<input id="zhangshu' + length + '" type="number" oninput="mychange()" style="height: 40px;width: 20%;margin-top:10px;font-size: 20px;" placeholder="" />' +
			'净重:<input id="jingzhong' + length + '" type="number" style="height: 40px;width: 30%;margin-top:10px;font-size: 20px;" placeholder="" />(kg)</li>';
		$("#phtml").append(phtml);
		if(eachWeight == 0) {
			$("#zhangshu" + length + "").val("1");
		}
		length++;
		$("input[type=radio]").each(function() {
			$(this).attr("disabled", true);
		});
		/*console.log(eachWeight)
		if(chk_value.length > 1 && productProcessId != "P") {
			phtml = $("#phtml").html();
			//console.log("phtml = "+phtml);
			phtml = '<li class="mui-table-view-cell">' +
				'张数:<input id="zhangshu' + length + '" type="number" oninput="mychange()" style="height: 30px;width: 20%;margin-top:10px;font-size: 20px;" placeholder="" />' +
				'净重:<input id="jingzhong' + length + '" type="number" style="height: 30px;width: 30%;margin-top:10px;font-size: 20px;" placeholder="" />(kg)</li>';
			$("#phtml").append(phtml);
			if(eachWeight == 0){
				$("#zhangshu" + length + "").val("1");
			}
			length++;
			$("input[type=radio]").each(function() {
				$(this).attr("disabled", true);
			});
			$("#add").attr('disabled', true);
		} else {
			
		}*/
	}
});

// input 值改变
function mychange() {
	var productDeciPlace = "";
	var eachWeight = "";
	$.each(ylList, function(i, e) {
		console.log(e.productDeciPlace)
		productDeciPlace = e.productDeciPlace;
		if(e.productDeciPlace == "") {
			productDeciPlace = 6;
		}
		eachWeight = e.eachWeight;
		console.log(e.eachWeight)
	});

	if(eachWeight != 0) {
		for(var i = 0; i < length; i++) {
			var zhangshu = $("#zhangshu" + i + "").val();
			var netWeight = ((eachWeight * zhangshu).toFixed(productDeciPlace)) * 1000000 / 10000;
			//console.log((eachWeight * zhangshu).toFixed(productDeciPlace))
			$("#jingzhong" + i + "").val(netWeight);
		}
	}
}

//判断数组中不重复的数据记录数
function checknum(arrlist) {
	var num = 1;
	for(var i = 0; i < arrlist.length - 1; i++) {
		if(arrlist[i].partId != arrlist[i + 1].partId) {
			num++;
		}
	}
	return num;
}

//提交
mui(document.body).on('tap', '#save', function() {
	var productDeciPlace = "";
	var eachWeight = "";
	$.each(ylList, function(i, e) {
		productDeciPlace = e.productDeciPlace;
		if(e.productDeciPlace == "") {
			productDeciPlace = 6;
		}
		eachWeight = e.eachWeight;
	});
	var umcLoginName = "admin";
	var packType = "4";
	var status = "";
	var productProcessId = "";
	$.each(gdList, function(ix, ex) {
		productProcessId = ex.productProcessId;
		status = ex.status;
	});
	var quantity_value = new Array();
	for(var i = 0; i < length; i++) {
		if($("#zhangshu" + i + "").val() != "" && $("#zhangshu" + i + "").val() != "" &&
			$("#jingzhong" + i + "").val() != null && $("#jingzhong" + i + "").val() != "") {
			var ckval = {};
			ckval.quantity = $("#zhangshu" + i + "").val();
			ckval.netWeight = $("#jingzhong" + i + "").val();
			quantity_value.push(ckval);
		}
	}
	var chk_value = new Array();
	$('input[name="checkbox1"]:checked').each(function() {
		var ckval = {};
		ckval.rmPackId = $(this).val().split(",")[0];
		ckval.productId = $(this).val().split(",")[1];
		ckval.productionOrderCode = $(this).val().split(",")[2];
		ckval.partId = $(this).val().split(",")[3];
		ckval.spec = $(this).val().split(",")[4];
		chk_value.push(ckval);
	});
	console.log(eachWeight)
	/*var sum = 99;
	if(chk_value.length > 1) {
		sum = 0;
		$.each(chk_value, function(i, e) {
			sum = sum + Number($("#" + e.rmPackId + "").val());
		});
		var quatity = $("#zhangshu0").val();
		var netWeight = $("#jingzhong0").val();
		if(eachWeight == 0) {
			if(sum != netWeight) {
				sum = 0;
			}
		} else {
			if(sum != quatity) {
				sum = 0;
			}
		}
	}
	console.log(sum)*/
	if(ylList.length == 0) {
		mui.alert("没有投料捆包信息，请先领料！", "提示", "确定", function() {}, "div");
	} else if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
		localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
		//班组班次不存在，跳转选择班组班次
		mui.openWindow({
			url: 'select_class.html',
			id: 'select_class',
			createNew: true
		});
	} else if(status != "30") {
		mui.alert("工单不为启动状态，不能录入实绩！", "提示", "确定", function() {}, "div");
	} else if(quantity_value.length == 0) {
		mui.alert("张数或净重不能为空！", "提示", "确定", function() {}, "div");
	} else if(quantity_value.length < length) {
		mui.alert("请检查页面数据是否输入完整！", "提示", "确定", function() {}, "div");
	} else if(chk_value.length == 0) {
		mui.alert("请先勾选投料捆包信息！", "提示", "确定", function() {}, "div");
	} else {
		var billId = 0;
		$.each(quantity_value, function(i, e) {
			var quantity = e.quantity;
			var netWeight = e.netWeight;
			billId++;
			$.each(chk_value, function(i, e) {
				var winccInfo = {};
				winccInfo.partId = e.partId;
				winccInfo.productionOrderCode = e.productionOrderCode;
				winccInfo.rmPackId = e.rmPackId;
				winccInfo.productId = e.productId;
				winccInfo.spec = e.spec;
				winccInfo.quantity = quantity;
				winccInfo.netWeight = netWeight;
				winccInfo.billId = billId;
				winccInfoList.push(winccInfo);
			});
		});
		/*console.log(productProcessId)
		if(productProcessId == "P") {
			var billId = 0;
			$.each(quantity_value, function(i, e) {
				var quantity = e.quantity;
				var netWeight = e.netWeight;
				billId++;
				$.each(chk_value, function(i, e) {
					var winccInfo = {};
					winccInfo.partId = e.partId;
					winccInfo.productionOrderCode = e.productionOrderCode;
					winccInfo.rmPackId = e.rmPackId;
					winccInfo.productId = e.productId;
					winccInfo.spec = e.spec;
					winccInfo.quantity = quantity;
					winccInfo.netWeight = netWeight;
					winccInfo.billId = billId;
					winccInfoList.push(winccInfo);
				});
			});
		} else {
			if(chk_value.length == 1) {
				
			} else {
				if(eachWeight != 0) {
					console.log(666)
					则要输入每个原料捆包的占用张数，占用重量根据占用张数*投料捆包零件号单张重量，
					所有原料捆包的占用张数要等于页面输入的实绩张数
					var billId = 0;
					$.each(quantity_value, function(i, e) {
						var quantity = e.quantity;
						var netWeight = e.netWeight;
						billId++;
						$.each(chk_value, function(i, e) {
							var quantity = ($("#" + e.rmPackId + "").val());
							var netWeight = ((eachWeight * quantity).toFixed(productDeciPlace)) * 1000000 / 10000;
							var winccInfo = {};
							winccInfo.partId = e.partId;
							winccInfo.productionOrderCode = e.productionOrderCode;
							winccInfo.rmPackId = e.rmPackId;
							winccInfo.productId = e.productId;
							winccInfo.spec = e.spec;
							winccInfo.quantity = quantity;
							winccInfo.netWeight = netWeight;
							winccInfo.billId = billId;
							winccInfoList.push(winccInfo);
						});
					});
				} else {
					console.log(666)
					var billId = 0;
					$.each(quantity_value, function(i, e) {
						var quantity = e.quantity;
						//var netWeight = e.netWeight;
						billId++;
						$.each(chk_value, function(i, e) {
							var winccInfo = {};
							winccInfo.partId = e.partId;
							winccInfo.productionOrderCode = e.productionOrderCode;
							winccInfo.rmPackId = e.rmPackId;
							winccInfo.productId = e.productId;
							winccInfo.spec = e.spec;
							winccInfo.quantity = quantity;
							winccInfo.netWeight = ($("#" + e.rmPackId + "").val());
							winccInfo.billId = billId;
							winccInfoList.push(winccInfo);
						});
					});
				}
			}*/
		/*1、实绩录入页面可以勾选多个或者一个。如果勾选一个那么传入参数里的quantity就是张数netWeight就是净重
		2、如果页面候选多个投料捆包，要输入每个投料的占用张数和占用重量
		(如果投料捆包为卷则占用张数1，
		占用重量为页面输入的每个原料捆包的重量，所有原料捆包的占用重量要等于页面输入的净重。*/
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			umcLoginName: umcLoginName,
			packType: packType,
			workingShift: class_id,
			teamId: team_id,
			winccInfoList: winccInfoList
		};
		params = JSON.stringify(params);
		var method = "saveProcessResult";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				console.log(JSON.stringify(result));
				if(result != null) {
					var btnArray = ['确认'];
					mui.confirm(result.returnDesc, '提示', btnArray, function(e) {
						location.reload()
					})
				} else {
					var btnArray = ['确认'];
					mui.confirm('后台系统错误', '提示', btnArray, function(e) {
						location.reload()
					})
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	}
});

/**
 * image按钮事件
 */
mui(document.body).on('tap', '#down', function() {
	var liArray = new Array();
	var packArray = new Array();
	$('input[name="checkbox1"]:checked').each(function() {
		liArray.push($(this).parent().parent());
		var ckval = {};
		ckval.packId = $(this).val().split(",")[0];
		ckval.numberNo = $(this).val().split(",")[5];
		packArray.push(ckval);
		productionOrderCode = $(this).val().split(",")[2];
	});

	if (liArray.length < 1) {
		mui.toast("请选择要操作的捆包");
		return;
	} else if (liArray.length > 1) {
		mui.toast("请选择1个捆包进行操作");
		return;
	}

	var pickedLi = liArray[0];
	var nextLi = pickedLi.next();
	if (typeof(nextLi.html()) == "undefined" || nextLi.html() == null) {
		return;
	}

	//构造参数

	var checkbox = nextLi.find("#check1");
	var ckval = {};
	ckval.packId = checkbox.val().split(",")[0];
	ckval.numberNo = checkbox.val().split(",")[5];
	packArray.push(ckval);

	var pickedIndex = packArray[0].numberNo;
	var toIndex = packArray[1].numberNo;

	if (pickedIndex == '' || toIndex == '') {
		pickedIndex = pickedLi.index() + 1;
		toIndex = nextLi.index() + 1;
	}

	packArray[0].numberNo = toIndex;
	packArray[1].numberNo = pickedIndex;

	console.log("封装参数：" + JSON.stringify(packArray));

	changePackNum(packArray, pickedLi, null, nextLi);


});

mui(document.body).on('tap', '#up', function() {
	var liArray = new Array();
	var packArray = new Array();
	$('input[name="checkbox1"]:checked').each(function() {
		liArray.push($(this).parent().parent());
		var ckval = {};
		ckval.packId = $(this).val().split(",")[0];
		ckval.numberNo = $(this).val().split(",")[5];
		packArray.push(ckval);
		productionOrderCode = $(this).val().split(",")[2];
	});

	if (liArray.length < 1) {
		mui.toast("请选择要操作的捆包");
		return;
	} else if (liArray.length > 1) {
		mui.toast("请选择1个捆包进行操作");
		return;
	}
	var pickedLi = liArray[0];
	var preLi = pickedLi.prev();
	if (typeof(preLi.html()) == "undefined" || preLi.html() == null) {
		return;
	}

	//构造参数
	var checkbox = preLi.find("#check1");
	var ckval = {};
	ckval.packId = checkbox.val().split(",")[0];
	ckval.numberNo = checkbox.val().split(",")[5];
	packArray.push(ckval);

	var pickedIndex = packArray[0].numberNo;
	var toIndex = packArray[1].numberNo;
	if (pickedIndex == '' || toIndex == '') {
		pickedIndex = pickedLi.index();
		toIndex = preLi.index();
	}

	packArray[0].numberNo = toIndex;
	packArray[1].numberNo = pickedIndex;

	console.log("封装参数：" + JSON.stringify(packArray));

	changePackNum(packArray, pickedLi, preLi, null);
});

/**
 * 改变捆包顺序
 */
function changePackNum(packList, pickedLi, preLi, nextLi) {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	// var seg_no = '00118';
	// var user_id = 'admin';
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: productionOrderCode,
		packList: packList
	};
	params = JSON.stringify(params);
	var method = "adjustPackNumberNo";
	console.log("outUri：" + outUri);
	console.log("innerUri：" + innerUri);
	console.log("params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log(method + "返回result：" + JSON.stringify(result));
			if(result != null) {
				if(result.returnValue == 1) {
					//成功
					pickedLi.remove();
					if(null == nextLi) {
						pickedLi.insertBefore(preLi);
					} else {
						pickedLi.insertAfter(nextLi);
					}

				} else {
					mui.alert(result.returnDesc);
				}
			} else {
				mui.alert("调整失败，请重试");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("");
		}
	})
};