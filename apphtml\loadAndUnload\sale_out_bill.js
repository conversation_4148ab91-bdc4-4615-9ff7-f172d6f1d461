var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var scanBillList = new Array(); //已扫描出库单集合
var ownPackInfoList = new Array(); //自带捆包


//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//出厂确认按钮
mui(document.body).on('tap', '#confirm', function() {
	var chuku_id = "";
	console.log(JSON.stringify(scanBillList))
	$.each(scanBillList, function(i, item) {
		if (i == 0) {
			chuku_id += item;
		} else {
			chuku_id += "," + item;
		}
	});
	console.log(chuku_id)
	if (chuku_id != "" && chuku_id != null) {
		mui("#confirm").button('loading');
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var outUri = domainName + "webService_test.jsp";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		var params = '{"seg_no":"' + segNo + '","chuku_id":"' + chuku_id + '"}';
		console.log(params);
		params = encodeURI(params, 'utf-8');
		var method = "saleOutKunbao";
		$.ajax({
			type: "post",
			//contentType:"application/json",
			async: true,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(result) {
				mui("#confirm").button('reset');
				ownPackInfoList = result.saleOutList;
				console.log("saleOutKunbao返回："+JSON.stringify(result));
				var btnArray = ['确认', '取消'];
				mui.confirm('确认出库单已扫完成？', '提示', btnArray, function(e) {
					if (e.index == 0) {
						mui.openWindow({
							url: 'sale_out.html',
							createNew: true,
							extras: {
								ownPackInfoList: ownPackInfoList,
							},
						});
					}
				}, 'div');
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
				this;
			}
		});
	} else {
		mui.alert("请扫描出库单号！", "提示", "确定", function() {}, "div");
	}
});

$(function() {
	//出库单号输入框获得焦点
	$("#to_scan_outStock")[0].focus();
});

//出库单扫描输入按下监听
$("#to_scan_outStock").keypress(function(e) {
	var chuku_id = $("#to_scan_outStock").val();
	if (e.keyCode == 13) {
		if (chuku_id != "" && chuku_id != null) {
			if (!IsInArray(scanBillList, chuku_id)) {
				scanBillList.push(chuku_id);
				showMateSuccessPackInfo();
			} else {
				mui.alert("该出库单号已扫描！", "提示", "确定", function() {}, "div");
			}
		} else {
			mui.alert("请扫描正确的出库单号！", "提示", "确定", function() {}, "div");
		}
	}
});

// 显示要出厂的捆包信息
function showMateSuccessPackInfo() {
	console.log(JSON.stringify(scanBillList))
	$("#to_scan_outStock").val("");
	$("#to_scan_outStock")[0].focus();
	//绘制已扫捆包信息
	var phtml = "";
	var prot = "";
	$.each(scanBillList, function(i, item) {
		phtml = phtml + '<li class="mui-table-view-cell">' +
			'<div class="mui-slider-handle">' +
			'<div id="chuku_id" style=" line-height: 45px;color: blue;width:300px; height:45px;"  data="' + item + '">' + item +
			'<button id="delButton" type="button" class="mui-btn mui-btn-danger" onclick=' +
			'deleteLi("' + item + '")' + '>删除</button>' +
			'</div>' +
			'</li>';
	});
	$("#phtml").html(phtml);
}

//删除已扫描的出库单
function deleteLi(chuku_id) {
	var index = scanBillList.indexOf(chuku_id);
	if (index > -1) {
		scanBillList.splice(index, 1);
		showMateSuccessPackInfo();
	}
}

//判断是否重复扫描出库单
function IsInArray(arr, val) {
	console.log(JSON.stringify(arr));
	var testStr = ',' + arr.join(",") + ",";
	return testStr.indexOf("," + val + ",") != -1;
}
