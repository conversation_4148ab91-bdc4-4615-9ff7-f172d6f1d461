/**
 * 初始化变量信息 
 */
var team_id = ""; //班组
var class_id = ""; //班次
var team_name = ""; //班组
var class_name = ""; //班次
var putin_method_switch = ""; //青岛宝井入库方式开关

var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");

$(function() {
	queryWorkShift();
	putin_method_switch = getSwitchValue(segNo, 'PDA_PUTIN_METHOD_CHOICE');
	//				mui.init({
	//					swipeBack:true //启用右滑关闭功能
	//				});
	
	//生产跳转逻辑
	locationsc();
});

//跳转生产方法
function locationsc(){
	if(localStorage.getItem("unit") == null || localStorage.getItem("unit") == "") {
		//机组不存在，跳转选择机组
		mui.openWindow({
			url: '../produce/select_unit.html',
			id: 'select_unit',
			createNew: true,
		});
	} else {
		// 班组班次机组都不为空，进入生产加工页面
		mui.openWindow({
			url: '../produce/produce_menus.html',
			id: 'produce_menus',
			createNew: true
		});
	}
}

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	if(team_id == null || team_id == "" || team_id == "00") {
		mui.alert("请选择班组信息", " ", "确定", function() {}, 'div');
		return false;
	}
	if(class_id == null || class_id == "") {
		mui.alert("请选择班次信息", " ", "确定", function() {}, 'div');
		return false;
	}
	localStorage.setItem("team_id", team_id);
	localStorage.setItem("class_id", class_id);
	localStorage.setItem("team_name", team_name);
	localStorage.setItem("class_name", class_name);
	var wprovider_name = localStorage.getItem("wprovider_name");
	var hand_point_name = localStorage.getItem("hand_point_name");
	var self = plus.webview.currentWebview();
	var popclass = self.openType;
	if("pop_team_update" == popclass) {
		mui.openWindow({
			url: 'index.html',
			id: 'index',
			createNew: true
		});
	} else {
		if(localStorage.getItem("name") == 'putin_scan') {
			//add ERP_55764 xiesenpeng 成都宝钢
			if(segNo == '00112'){						
					mui.openWindow({
						url: 'select_vehicle_no.html',
						id: 'select_vehicle_no',
						createNew: true
					});	
			}else
			//end xiesenpeng
			if(wprovider_name == "" || wprovider_name == null) {
				mui.openWindow({
					url: 'select_wprovider_id.html',
					id: 'select_wprovider_id',
					createNew: true,
					extras: {
						startfrom: 'menu'
					}
				});
			} else {				
				//add by Luo Yinghui at 2018-11-03
				//青岛宝井PDA改造，如果班组信息与仓库信息已经完善，并且入库方式选择开关已经打开，将进入入库选择页面
				if(putin_method_switch == "1" && segNo == '00113') {
					mui.openWindow({
						url: '../putin/putin_menus.html',
						id: 'putin_menus',
						createNew: true
					});
				} else {
						mui.openWindow({
							url: '../putin/putin_scan.html',
							id: 'putin_scan',
							createNew: true
						});					
				};
				//end by Luo Yinghui 下方注释的为原代码
				/*mui.openWindow({
							url:'putin_scan.html',
							id:'putin_scan',
							createNew:true
				    	});*/
			}
		} else if(localStorage.getItem("name") == 'putout_scan') {
			mui.openWindow({
				url: '../putout/putout_list_new.html',
				id: 'putout_list_new',
				createNew: true
			});
		} else if(localStorage.getItem("name") == 'load_menus') {
			if(wprovider_name == "" || wprovider_name == null) {
				mui.openWindow({
					url: 'select_wprovider_id.html',
					id: 'select_wprovider_id',
					extras: {
						openType: 'popHandPoint',
					},
					createNew: true
				});
			} else if(hand_point_name == "" || hand_point_name == null) {
				mui.openWindow({
					url: 'select_hand_point.html',
					id: 'select_hand_point',
					extras: {
						openType: 'popHandPoint',
					},
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../loadAndUnload/load_unload_menus.html',
					id: 'load_unload_menus',
					createNew: true
				});
			}
		} else if(localStorage.getItem("name") == 'synergy_cc') {
			if(wprovider_name == "" || wprovider_name == null) {
				mui.openWindow({
					url: 'select_wprovider_id.html',
					id: 'select_wprovider_id',
					createNew: true,
					extras: {
						startfrom: 'synergy'
					}
				});
			} else {
				mui.openWindow({
					url: '../changChunSynergy/changchun_synergy_scan.html',
					id: 'changchun_synergy_scan',
					createNew: true
				});
			}
		} else if(localStorage.getItem("name") == 'sgm_gf_putin') {
			if(wprovider_name == "" || wprovider_name == null) {
				mui.openWindow({
					url: 'select_wprovider_id.html',
					id: 'select_wprovider_id',
					createNew: true,
					extras: {
						startfrom: 'sgm_gf_putin'
					}
				});
			} else {
				mui.openWindow({
					url: '../sgmGFPutin/sgm_cch.html',
					id: 'sgm_cch',
					createNew: true
				});
			}
		}else {
			mui.openWindow({
				url: 'index.html',
				id: 'index',
				createNew: true
			});
		}
	}
});

/**
 * 用mui带的tap 好一些  如果元素重叠 会出现认为连续按下的感觉 
 */
//班组li点击事件
mui(document.body).on('tap', '#banzu button', function() {
	$(this).addClass('active').siblings().removeClass('active');
	//赋值
	$('#select_team_name').css("color", "black");
	$('#select_team_name').text($(this).text());
	team_id = $(this).attr("data");
	team_name = $(this).text();
});
//班次li点击事件
mui(document.body).on('tap', '#banci button', function() {
	$(this).addClass('active').siblings().removeClass('active');
	//赋值
	$('#select_class_name').css("color", "black");
	$('#select_class_name').text($(this).text());
	class_id = $(this).attr("data");
	class_name = $(this).text();
});

/**
 * 查询班组班次的接口
 */
function queryWorkShift() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAUserService';
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryWorkShift";
	console.log(params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(data);
		if(null != data) {
			var thtml = ""; //modify by Luo Yinghui at 2018-11-20 with 青岛宝井PDA改造专项
			if(segNo == "00113") {
				thtml = "<select id='class_info' name='' class='mui-btn mui-btn-block' onchange='selectClassInfo(this)'>";
				thtml = thtml + "<option value='00' selected>-班组选择-</option>";
				$.each(data.teamInfo, function(i, item) {
					thtml = thtml + '<option value="' + item.team_id + '">' + item.team_name + '</option>';
				});
				thtml = thtml + "</select>";
			} else {
				$.each(data.teamInfo, function(i, item) {
					if(i <= 3) {
						thtml = thtml + '<button type="button" class="mui-btn" data="' + item.team_id + '">' + item.team_name + '</button>';
					}
				});
			}
			//下方注释为原来的代码
			/*var thtml = "";
			$.each(data.teamInfo, function(i, item) {
				if(i <= 3) {
					thtml = thtml + '<button type="button" class="mui-btn" data="' + item.team_id + '">' + item.team_name + '</button>';
				}
			});*/
			$("#banzu").html(thtml);
		} else { //连接失败
			mui.alert("连接服务器异常");
		}
	});
}

/**
 * 青岛宝井下拉框数据选择事件
 * modify by Luo Yinghui at 2018-11-19 with 青岛宝井PDA改造专项
 */
function selectClassInfo(selectObj) {
	var optionsValue = document.getElementById("class_info").value;
	var optionText = document.getElementById("class_info").options[document.getElementById("class_info").options.selectedIndex].text;
	$('#select_team_name').css("color", "black");
	if(optionsValue == "00") {
		optionText = "班组";
		$('#select_team_name').css("color", "#38A4FF");
		//return;
	}
	//赋值
	console.log(optionText);
	$('#select_team_name').text(optionText);
	console.log(optionsValue + "||" + optionText);
	team_id = optionsValue;
	team_name = optionText;
}