<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>帐套选择</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link href="../../css/mui.picker.css" rel="stylesheet" />
		<link href="../../css/mui.poppicker.css" rel="stylesheet" />
		<link href="select_seg_no.css" rel="stylesheet" />
	</head>
	<body>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">帐套选择</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;">
				<div class="mui-input-row">
					<input id="area" class="mui-input" style="margin-bottom: 0px; padding: 2px 10px; font-size: 22px;" type="text" readonly="true" placeholder="选择区域"/>
				</div>
				<div style="margin: 5px; height: 230px; overflow: auto;">
					<ul id="companyList" class="mui-table-view mui-table-view-radio"></ul>
				</div>
				<div class="mui-input-row" style="margin-top: 20px;">
					<button id="confirm" type="button" class="mui-btn mui-btn-primary" style="width: 100%;font-size: 22px; line-height: 1.8;">确&nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp; &nbsp;定</button>
				</div>	
			</div>
		</div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/mui.picker.js"></script>
		<script src="../../js/mui.poppicker.js"></script>
		<script src="../../js/util/public.js"></script>
		<script src="select_seg_no.js"></script>
	</body>
</html>