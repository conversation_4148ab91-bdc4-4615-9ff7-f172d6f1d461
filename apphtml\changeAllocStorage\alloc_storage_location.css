/** 明细样式 */
			#storage_but,
			#storage_realloc{
				width: 95%;
				font-size: 22px;
				line-height: 1.8;
				margin: 10px 10px 5px 1px;
			}
			
			#left_location_btn,
			#right_location_btn{
				float: left;
				width: 33%;
				height: 37px;
				font-size: 20px;
				line-height: 1.0;
				margin: 2px 5px 0px 0px;
			}
			
			.detail_row {
				height: 45px;
			}
			
			.text {
				float: left;
				width: 22%;
				font-size: 22px;
				padding: 5px 0px;
				text-align: center;
			}
			/**四个字的 */
			
			.fourtext {
				float: left;
				width: 31%;
				height: 35px;
				font-size: 22px;
				padding: 10px 0px;
				text-align: center;
				border-right: 0px;
			}
			
			#adjacent_pack_id,
			#pack_location_new,
			#new_loc_view_id,
			#left_location,
			#right_location {
				width: 27%;
				padding: 0px 5px;
				font-size: 22px;
			}
			
			
			.pack_list {
				height: 75px;
				overflow: auto;
				border: 1px solid #AAAAAA;
			}
			
			.storage_pack {
				width: 60%;
				margin-top: 12px;
				text-align: left;
				padding-left:4px; 
			}
			
			.li-text {
				font-size: 20px;
				padding: 10px 0px;
				text-align: center;
				border-bottom: 1px solid #CBCBCB;
				height: 77px;
			}
			
			.li-text p span font {
				text-align: center;
				font-size: 20px;
				color: #000000;
			}
			
			.item a {
				display: block;
				float: left;
				background-color: #ff0000;
				color: #fff;
				width: 20%;
				margin-left: 0.16rem;
				font-size: 0.14rem;
				text-align: center;
				text-decoration: none;
			}
			
			.li-height {
				margin-top: 4px;
				text-align: left;
			}
			/** 列表样式 */
			
			.pack_location_target {
				color: black;
				font-size: 20px;
				margin-right: 5px;
			}
			
			.pack_location_now {
				color: black;
				font-size: 20px;
				margin-top: 8px;
				margin-right: 5px;
			}
			
			.mui-btn-red {
				font-size: 22px;
			}
			
			/* 半透明的遮罩层 */
			.overlay {
			    background-color: #777777;
			    opacity: 0.5; /* 透明度 */
			    position: absolute;
			    width: 100%;
			    height: 100%;
			    z-index: 99999; /* 此处的图层要大于页面 */
			}
			
			#packLabel{
				background-color: darkblue;
				color: white;
				font-size: 20px;
				margin-right:2px;
			}
			#newLocationLabel{
				background-color: orangered;
				color: white;
				font-size: 20px;
				margin-right:5px;
			}
			
			#oldLocationLabel{
				background-color: forestgreen;
				color: white;
				font-size: 20px;
				margin-right:5px;
			}
			
			/** lal 条状库位推荐样式*/
			#pack_id,#inner_diameter{
				padding: 0px 0px;
				margin: 0px 0px 7px 0px;
			}
			#InnerDiameterDiv.show{
				visibility: visible;
				opacity: 1;
			}
			/** 弹出图层设置 */
			#InnerDiameterDiv{
				position: absolute;
				z-index: 999;
				width: 270px;
				/*height: 245px;*/
				left: 42%;
				top: 40%;
				margin-left: -100px;
				margin-top: -122px;
				border-radius: 10px;  
				background: #FFFFFF;
				box-shadow: 0px 10px 12px rgba(0,0,0,.4);
				/** 动画效果 */
				visibility: hidden;
				opacity: 0; 
				/** 文字效果 */
				font-size: 20px;
				text-align: left;
			}
			
			#InnerDiameterDiv > .title{ 
				text-align: center;
				padding: 8px 0px;
				font-size: 22px;
				border-bottom: 1px solid;
				border-color: #D8D8D8;
			}
			
			.mui-input-row .mui-icon-search {
				font-size: 24px;
				position: absolute;
				z-index: 1;
				top: 10px;
				right: 0;
				width: 42px;
				/*height: 30px;*/
				/*text-align: left;*/
				color: #999;
			}
			
			.mui-input-clear{
				width: 45%;
			}
			
			.select {
				width:18%!important;
				font-size: 20px!important;
				background-color: white;
				border: 1px solid rgba(0,0,0,.2)!important;
			}
			.active{
				background-color: red;
				color: white;
			}
			
			/*
			 * add by lal 
			 */
			#fp_location{
				width: 42%;
				background-color: #fff;
				/*border: 1px solid rgba(0, 0, 0, .2);
				border-radius: 3px;*/
				font-size: 20px;
				/*line-height: 21px;*/
			}
			
			#fp_location_s {
				width: 26%;
				background-color: #fff;
				/*border: 1px solid rgba(0, 0, 0, .2);
				border-radius: 3px;*/
				font-size: 20px;
				/*line-height: 21px;*/
			}
			
			#x_point_start,
			#x_point_end{
				float: left;
				width: 33.8%;
				font-size: 20px;
			}
			
			#allocContext,#confirmContext{
				font-size:0px;
			}
			#allocLabel{
				color: blue;
			}
			#fp_location, #fp_location_s{
				background-color: #CCCCCC;
			}