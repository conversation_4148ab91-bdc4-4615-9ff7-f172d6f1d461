var area;//默认的区域
var segNo = "";
var segNoList,areaList;
var segNoInfo = "";
var providerid="";
var warehouse = "";

(function($, doc) {
	
	var areaPicker = new $.PopPicker();//加载区域下拉框
	initLocalData();
	areaPicker.setData(areaList);
	selectArea();
	mui(document.body).on('tap','#area',function(){//绑定事件
			areaPicker.show(function(items) {
			area = items[0];
			selectArea();//添加信息到公司列表中
		});
	});
	addListener();
	swipeBack(true); //启用右滑关闭功能
})(mui, document);
			
function addListener(){
	mui(document.body).on('selected','.mui-table-view.mui-table-view-radio',function(e){//绑定列表选中事件
		var str = e.detail.el.innerText;
		segNoInfo = str;
		segNo = str.substr(0,5);
		localStorage.clear();//先清除之前的缓存内容
	});
	mui(document.body).on('tap','#confirm',function(){//确认按钮绑定事件
		if(segNo==null||segNo==""){
			mui.toast("请选择组织单元代码");
			return false;
		}
		console.log(segNo+","+area.url+","+segNoInfo);
		localStorage.setItem("segNo",segNo);
		localStorage.setItem("webServiceUrl",area.url);
		localStorage.setItem("segNoInfo",segNoInfo);
		console.log(area.imesUrl);
		localStorage.setItem("imesWebServiceUrl",area.imesUrl);
		
		mui.openWindow({//打开登录页面
			url:'login.html',
			extras:{
				segNoInfo:segNoInfo //扩展参数
			},
			createNew:true
		});
	});
}
function selectArea(){//取列表选中公司名称
	$('#area').val(area.text);
	reInitDoc();//这里刷新加载公司列表
}
function reInitDoc(){
	$('#companyList li').remove();
	var companyListUL = $('#companyList');
	for(var i=0;i<segNoList.length;i++){
		if(segNoList[i].area == area.text){
			var li = $('<li class="mui-table-view-cell">'
							+'<a class="mui-navigate-right">'
								+'<div style="width: 45%; float: left; text-align: center;" ><label >'+segNoList[i].segNo+'</label></div>'
								+'<div style="width: 1%; float: left;"><label >|</label></div>'
								+'<div style="width: 45%;float: right; text-align: center;"><label >'+segNoList[i].segName+'</label></div>'
							+'</a>'
						+'</li>');
			companyListUL.append(li);
		}
	}
}
function initLocalData(){
	areaList = [
				{url: '10.30.92.21:7001',text:'华东区域',imesUrl:'10.30.91.67:8088'}, //正式
				// {url: '10.30.96.96:7001',text:'华东区域'},	//测试			
				// {url: '10.30.96.201:7001',text:'华东区域'},//测试-NEW
				{url: '10.30.91.20:7001',text: '南方区域',imesUrl:'10.30.91.68:8088'},//正式
				// {url: '10.30.91.29:7001',text: '南方区域',imesUrl:'10.30.96.92:8088'},//测试
				// {url: '10.30.96.202:7001',text: '南方区域'},//测试-NEW
				// {url:'10.30.184.244:7001',text:'西部区域'},
				// {u‘
				{url: '10.30.91.172:7001',text: '西部区域',imesUrl:'10.30.91.76:8088'},//西部正式
				// {url: '10.30.96.94:7001',text: '西部区域'},//西部测试
				{url: '10.30.91.156:7001',text: '北方区域',imesUrl:'10.30.91.65:8088'},//正式
				// {url: '10.30.90.133:7001',text: '北方区域'},//测试
				 {url: '10.30.96.204:7001',text: '北方区域'},//北方新测试机NEW
				{url: '10.30.91.24:7001',text: '华中区域',imesUrl:'10.30.91.74:8088'},//正式
				  // {url: '10.30.96.95:7001',text: '华中区域'},//華中測試 
				{url: '10.30.92.36',text: '东北区域',imesUrl:'10.30.91.69:8088'},//正式
				 // {url: '10.30.90.16:7001',text: '东北区域'},//测试
				//  {url: '10.30.96.54:7001',text: '东北区域'},//测试
				  // {url: '10.30.96.206:7001',text: '东北区域'},//测试-NEW 20210622
				{url:  '10.70.27.3:7001',text: '不锈钢'},//正式
				 // {url:  '10.60.176.102:7001',text: '不锈钢'},//测试
				{url:   '10.30.92.201', text: '宝钢零部件'},//零部件正式
				// {url:   '10.70.248.65', text: '宝钢零部件'},//10.30.92.203
				// {url:   '10.70.88.5:9080', text: '宝钢零部件'},
				{url: '10.30.91.180:7001',text: '浦东国贸'},
				{url: '10.70.38.6',text: '无锡宝美锋',imesUrl:'10.70.38.6'},
				];
	segNoList= [{segNo: '00100',area: '华东区域',segName:'上海钢贸'},
				{segNo: '00134',area: '华东区域',segName:'安徽宝钢'}, 
				{segNo: '00135',area: '华东区域',segName:'南京宝钢'},
				{segNo: '00136',area: '华东区域',segName:'杭州宝井'},
				{segNo: '00137',area: '华东区域',segName:'上海宝井'},
				{segNo: '00138',area: '华东区域',segName:'上海高强钢'},
				{segNo: '00141',area: '华东区域',segName:'上海钢带'},
				{segNo: '00147',area: '华东区域',segName:'宁波宝井'},
				{segNo: '00153',area: '华东区域',segName:'常熟宝升'},
				{segNo: '00171',area: '华东区域',segName:'芜湖威仕科'},
				
				{segNo: '00107',area: '南方区域',segName:'南方公司'},
				{segNo: '00111',area: '南方区域',segName:'揭阳宝钢'},
				{segNo: '00118',area: '南方区域',segName:'花都宝井'},
				{segNo: '00119',area: '南方区域',segName:'柳州宝钢'},
				{segNo: '00120',area: '南方区域',segName:'厦门宝钢'},
				{segNo: '00121',area: '南方区域',segName:'东莞宝钢'},
				{segNo: '00122',area: '南方区域',segName:'广州宝井'},
				{segNo: '00123',area: '南方区域',segName:'广州宝丰井'},
				{segNo: '00124',area: '南方区域',segName:'广州宝井昌'},
				{segNo: '00125',area: '南方区域',segName:'福州宝井'},
				{segNo: '00126',area: '南方区域',segName:'佛山宝钢'},
				{segNo: '00152',area: '南方区域',segName:'三水宝钢'},
				{segNo: '00159',area: '南方区域',segName:'东莞部件'},
				{segNo: '00165',area: '南方区域',segName:'柳州零部件'},
				{segNo: '00166',area: '南方区域',segName:'湛宝高科'},
				{segNo: '00172',area: '南方区域',segName:'广州武钢'},
				{segNo: '00121',area: '南方区域',segName:'东莞宝钢'},
				
				{segNo: '00105',area: '西部区域',segName:'西部公司'},
				{segNo: '00112',area: '西部区域',segName:'成都宝钢'},
				{segNo: '00114',area: '西部区域',segName:'西安宝钢'},
				{segNo: '00132',area: '西部区域',segName:'重庆宝钢'},
				{segNo: '00133',area: '西部区域',segName:'重庆宝井'},
				{segNo: '00149',area: '西部区域',segName:'巴南宝钢'},
				{segNo: '00164',area: '西部区域',segName:'重庆宝吉'},
				{segNo: '00185',area: '西部区域',segName:'重庆宝钢巴南分公司'},
				
				{segNo: '00104',area: '北方区域',segName:'天津公司'},
				{segNo: '00142',area: '北方区域',segName:'北方公司'},
				{segNo: '00113',area: '北方区域',segName:'青岛宝井'},
				{segNo: '00129',area: '北方区域',segName:'天津宝钢'},
				{segNo: '00130',area: '北方区域',segName:'济南宝钢'},
				{segNo: '00131',area: '北方区域',segName:'烟台宝井'},
				{segNo: '00145',area: '北方区域',segName:'天津宝井'},
				{segNo: '00148',area: '北方区域',segName:'山东宝华'},
				{segNo: '00174',area: '北方区域',segName:'天津武钢'},
				{segNo: '00177',area: '北方区域',segName:'青岛宝钢'},
				{segNo: '00186',area: '北方区域',segName:'天津宝钢北辰分公司'},
				
				{segNo: '00106',area: '华中区域',segName:'华中公司'},
				{segNo: '00115',area: '华中区域',segName:'郑州宝钢'},
				{segNo: '00116',area: '华中区域',segName:'南昌宝江'},
				{segNo: '00117',area: '华中区域',segName:'长沙宝钢'},
				{segNo: '00150',area: '华中区域',segName:'襄阳宝泓'},
				{segNo: '00161',area: '华中区域',segName:'武汉宝章'},
				{segNo: '00168',area: '华中区域',segName:'武钢诺贝（武汉）激光拼焊技术有限公司'},
				{segNo: '00169',area: '华中区域',segName:'武汉威仕科钢材加工配送有限公司'},
				{segNo: '00170',area: '华中区域',segName:'开封威仕科材料技术有限公司'},
				{segNo: '00176',area: '华中区域',segName:'芜湖威仕科材料技术有限公司开封分公司'},
				{segNo: '00179',area: '华中区域',segName:'武钢激光拼焊(武汉)'},
				{segNo: '00180',area: '华中区域',segName:'武钢激光拼焊(重庆)'},
				
				
				{segNo: '00109',area: '东北区域',segName:'沈阳宝钢'},
				{segNo: '00143',area: '东北区域',segName:'大连宝友'},
				{segNo: '00128',area: '东北区域',segName:'吉林宝钢'},
				{segNo: '00146',area: '东北区域',segName:'沈阳宝钢-1'},
				{segNo: '00108',area: '东北区域',segName:'长春宝钢'},
				{segNo: '00127',area: '东北区域',segName:'一汽宝友'},
				
				{segNo: '00110',area: '不锈钢',segName:'上海不锈'},
				{segNo: '00140',area: '不锈钢',segName:'宁波宝钢'},
				{segNo: '00126',area: '不锈钢',segName:'佛山宝钢'},
				{segNo: '00181',area: '不锈钢',segName:'欧冶上海不锈'},
				
				{segNo: '00101',area: '浦东国贸',segName:'浦东国贸'},
				{segNo: '00162',area: '宝钢零部件',segName:'宝钢零部件'},
				{segNo: '00163',area: '宝钢零部件',segName:'宝新零部件'},
				{segNo: '00178',area: '无锡宝美锋',segName:'无锡宝美锋'}];
//	willlist= [{provider_id:'3G01',provider_name:'重庆宝钢成品库',
//	           team_id:'',team_name:'',	
//	            work_shift:'',work_shift_name:''}];
	area = areaList[0];
}
