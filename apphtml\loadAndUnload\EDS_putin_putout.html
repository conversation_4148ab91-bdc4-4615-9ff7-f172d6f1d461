<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" type="text/css" href="EDS_putin_putout.css" />
		<title>出库单据</title>
	</head>
	<body>
		<div id="overlay"></div>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">EDS现场出入库</h4>
		</div>
		<div class="mui-content" style="margin-top: 4px;padding-top: 0px;">
			<div style="text-align: left; height: 40px;"> <span style="width: 23%; display: inline-block;">装卸点</span>
				<input class="my_input" type="text" disabled="disabled" style="background-color: #CCCCCC;" id="hand_point_name" />
				<input hidden="hidden" type="text" id="hand_point_id" />
			</div>
			<div style="text-align: left; height: 40px;"> <span style="width: 23%; display: inline-block;">配车单</span> <i class="icon-search"></i>
				<input class="my_input" type="text" id="allocate_vehicle_id" />
				<div style="display:none;" id="query_div">
					<a href="#" style="position:absolute; top: 165px;z-index: 10;right: 12px;" id="colse_process_bug_id"></a>
					<div class="mui-scroll-wrapper" style="height: 150px;top: 155px">
						<div class="mui-scroll">
							<ul class="mui-table-view allvi" id="selectdata">
							</ul>
						</div>
					</div>
				</div>
			</div>
			<div style="text-align: left; height: 40px;"> <span style="width: 23%; display: inline-block;">库&nbsp;&nbsp;&nbsp;位</span>
				<!--<input class="my_input" type="text" id="pack_location" />-->
				<!-- <select class="my_input" id="pack_location" style="margin-top: 10px;" onchange="location_id_change(this.options[this.options.selectedIndex].value)">
					<option value="0" selected="true" >--请选择--</option>	
				</select> -->
				<input class="my_input" type="text" onclick="selectLocation()" readonly="readonly" placeholder="请选择库位" id="location" />
			</div>
			<div style="text-align: left; height: 40px;"> <span style="width: 23%; display: inline-block;">捆包号</span>
				<input class="my_input" type="text" id="pack_id" />
			</div>
			<div style="text-align: left;margin-top: 20px;"> 已扫描列表 <span id="weiscancount"></span> </div>
			<div class="mui-card" style="border-radius: 3px;border: 1px solid #CCCCCC;height: 200px;overflow-y: auto;">
				<ul class="mui-table-view" id="pack_Ul">
					<!-- <li class="mui-table-view-cell packLi" style=" line-height:  45px; height:  45px;">123 <span>
					<button type="button"class="mui-btn mui-btn-danger packbutton" data="123">删除</button>
					<button type="button"class="mui-btn mui-btn-primary">详情</button>
					</span> </li> -->
				</ul>
			</div>
			<button id="zhuang" type="button" class="mui-btn mui-btn-blue">继续装货</button>
			<button id="xiehuo" type="button" class="mui-btn mui-btn-blue">继续卸货</button>
			<button id="jieshu" type="button" class="mui-btn mui-btn-blue">装卸结束</button>
			<div style="text-align: left;margin-top: 20px;"> 总捆包个数： <span id="packCount">0</span> （个） </div>
			<div style="text-align: left;margin-top: 20px;"> 总捆包件数： <span id="packNumber">0</span> （件） </div>
			<div style="text-align: left;margin-top: 20px;"> 总捆包重量： <span id="packWeight">0</span> （吨） </div>
			<div id="div">
			</div>
			<div id="popover" class="mui-popover" style="height: 420px;width: 350px;">
				<div class="mui-popover-arrow"></div>
				<div class="mui-scroll-wrapper">
					<div class="mui-scroll">
						<div style="margin-top: 10px;">捆包详情</div>
						<ul class="mui-table-view" style="text-align: left;" id="detail">
							<!-- <li style="margin-top:20px; height:  40px;">捆包号： <span>PVK18803623</span>
							<li style="margin-top:20px; height:  40px;">标签号： <span>PVK18803623</span>
							<li style="margin-top:20px; height:  40px;">重量： <span>0.099</span>
							<li style="margin-top:20px; height:  40px;">件数： <span>1</span>
							<li style="margin-top:20px; height:  40px;">规格： <span>1.0*C*C</span>
							<li style="margin-top:20px; height:  40px;">牌号： <span>B280VK</span>
							<li style="margin-top:20px; height:  40px;">客户零件号： <span></span>
							<li style="margin-top:20px; height:  40px;">客户名称： <span>长春宝钢钢材贸易有限公司</span> -->
						</ul>
					</div>
				</div>
			</div>
		</div>
		<!-- 弹出装卸点信息图层 -->
		<div id="pop_car_info">
			<div class="title"><span>装卸点信息</span>
				<button id="morebut" type="button" class="mui-btn morebut_s">更多</button>
			</div>
			<div style="text-align: left;">
				<div class="mui-input-row mui-search divnone">
					<input id="hid" type="text" />
					<input id="hidname" type="text" />
					<a href="#"><span class="mui-icon mui-icon-search" id="query_button"></span></a>
				</div>
				<!--显示列表-->
				<div style="margin: 5px; height: 220px; overflow: auto; background-color: #ECECEC;" id="carinfo">
					<ul id="carList" class="mui-table-view mui-table-view-radio" />
				</div>
				<!--装卸点确认-->
				<div class="mui-input-row" style="margin-top: 1px; height: 60px">
					<button id="confirm" type="button" class="mui-btn mui-btn-primary" style="width: 100%;font-size: 22px; line-height: 1.8;">确认</button>
				</div>
			</div>
		</div>
		<!-- 覆盖背景图层 -->
		<div id="pop_car"></div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script src="EDS_putin_putout.js" type="text/javascript" charset="UTF-8"></script>
		<script src="../../js/pda/putin.js" type="text/javascript" charset="UTF-8"></script>
		<script src="../../js/pda/time.js" type="text/javascript" charset="UTF-8"></script>
		<script src="../../js/layer.js" type="text/javascript" charset="UTF-8"></script>
		<script>
		</script>
	</body>
</html>
