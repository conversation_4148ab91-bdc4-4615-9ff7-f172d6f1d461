var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");

var factory_area_id = localStorage.getItem("factory_area_id");

var vehicle_id = "";

var ownPackInfoList = new Array(); //车辆自带捆包
var scanPackList = new Array(); //扫描捆包
var checkList = new Array();
var pack_count = ""; //捆包合计
var scan_number = ""; //已扫描个数

var pack_id = "";
var pack_type = "";
var car_trace_no = "";

//页面加载时的值
window.onload = function onload() {
	mui.plusReady(function() {
		ownPackInfoList = plus.webview.currentWebview().packInfoList;
		console.log("<<<<<<<<<<<<<<ownPackInfoList:" + JSON.stringify(ownPackInfoList));
		pack_count = plus.webview.currentWebview().pack_count;
		vehicle_id = plus.webview.currentWebview().vehicle_id;
		car_trace_no = plus.webview.currentWebview().car_trace_no;
		console.log(">>>>>>>>>>>>pack_count:" + pack_count);
		$("#sum_number").text(pack_count); //捆包合计的数值
	});
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

mui(document.body).on('tap', '#confirm', function() {
	/*mui("#confirm").button('reset');
	mui.alert("出厂成功!", "提示", "确定", function() {
		var page = plus.webview.getWebviewById('pack_leave_vehicle_scan.html');
	     mui.fire(page,'refresh',{
	});
	}, "div");*/
	/*var wvs=plus.webview.all();
	for(var i=0;i<wvs.length;i++){
		console.log('webview'+i+': '+wvs[i].getURL());
	}*/
	/*if (scan_number < pack_count){
		mui.alert( pack_id + "捆包未完全匹配，无法出库！", "提示", "确定", function() {}, "div");
		return false;
	}*/
	if(scan_number < pack_count) {
		//佛宝 不需要完全匹配捆包
		//上海不锈出厂的时候判断是全匹配还是挑几个捆包出厂，加个开关判断
		var pdaPrint = getSwitchValue(segNo, 'PDA_OUT_FACT_OPEN');
		if(segNo == "00126") {
			if(!confirm("捆包未完全匹配,是否确认出库")) {
				return false;
			}
		} else if( pdaPrint == '1'){
			console.log("上海不锈开关直接出库不管是否全部扫完");
		}else {
			mui.alert(pack_id + "捆包未完全匹配，无法出库！", "提示", "确定", function() {}, "div");
			return false;
		}
	}
	if(segNo == "00126" || pdaPrint == '1') {
		exeOwnGoodVehicleOutFSBG();
	} else {
		exeOwnGoodVehicleOut();
	}
});

$(function() {
	//库位输入框获得焦点
	$("#to_scan_pack")[0].focus();
});

//出厂捆包扫描输入按下监听
$("#to_scan_pack").keypress(function(e) {
	console.log($("#to_scan_pack").val());
	if(scan_number == pack_count) {
		mui.alert(pack_id + "捆包已扫完成，请执行出厂操作！", "提示", "确定", function() {}, "div");
		return false;
	} else {
		if(e.keyCode == 13) {
			pack_id = $("#to_scan_pack").val();
			matePutoutPack(pack_id, scanPackList);
			$("#to_scan_pack").val("");
			showMateSuccessPackInfo();
			$("#to_scan_pack")[0].focus();
		}
	}

});

// 显示匹配成功捆包信息
function showMateSuccessPackInfo() {
	console.log("scanPackList:" + JSON.stringify(scanPackList));
	//绘制已扫捆包信息
	var phtml = "";
	var prot = "";
	$.each(scanPackList, function(i, item) {
		console.log("item:" + JSON.stringify(item['map']));
		var data = item['map'];
		if(data.pack_type == 1) {
			prot = "出厂";
		} else {
			prot = "自带";
		}
		phtml = phtml + '<li class="mui-table-view-cell">' +
			'<div class="mui-slider-handle">' +
			'<div id="pack_id"  data="' + data.pack_id + '">' + data.pack_id + '</div>' +
			'<div>' +
			'<div id="status" class="left"><span>状态</span><label>' + data.status + '</label></div>' +
			'<div id="prot" ><span>属性</span><label>' + prot + '</label></div>' +
			'</div>' +
			'</a>' +
			'</li>';
	});
	$("#phtml").html(phtml);

}

// 匹配应出厂捆包明细
function matePutoutPack(pack_id, scanPackList) {
	var scanPackInfo = new HashMap();
	var flag = 0;
	$.each(ownPackInfoList, function(i, value) {
		if(value.pack_id == pack_id) {
			pack_type = value.pack_type;
			//如果捆包有
			flag = 1;
			return true;
		}
	});
	if(flag == 1) {
		if(IsInArray(checkList, pack_id)) {
			mui.alert(pack_id + "捆包已扫描过", "提示", "确定", function() {}, "div");
			return false;
		} else {
			scanPackInfo.put("pack_id", pack_id);
			scanPackInfo.put("pack_type", pack_type);
			scanPackInfo.put("status", "匹配成功");
			scanPackList.push(scanPackInfo);
			console.log("scanPackList:" + JSON.stringify(scanPackList));
			checkList.push(pack_id);
			console.log("checkList:" + JSON.stringify(checkList));
			scan_number = scanPackList.length;
			console.log(scan_number);
			$("#scan_number").text(scan_number);
		}
	} else {
		mui.alert(pack_id + "此捆包与车辆不匹配", "提示", "确定", function() {}, "div");
		return false;
	}
	showMateSuccessPackInfo();
}

function IsInArray(arr, val) {
	console.log(JSON.stringify(arr));　　
	var testStr = ',' + arr.join(",") + ",";　　
	return testStr.indexOf("," + val + ",") != -1;
}

// 出厂
function exeOwnGoodVehicleOut() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","factory_area_id":"' + factory_area_id + '","car_trace_no":"' + car_trace_no + '","vehicle_id":"' + vehicle_id + '","pack_list":' + JSON.stringify(ownPackInfoList) + '}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "exeOwnGoodVehicleOut";
	$.ajax({
		type: "post",
		//contentType:"application/json",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if(null != result) {
				mui("#confirm").button('reset');
				mui.alert("出厂成功!", "提示", "确定", function() {
					var page = plus.webview.getWebviewById('pack_leave_vehicle_scan.html');
					mui.fire(page, 'refresh', {});
				}, "div");
				//							var wvs=plus.webview.all();
				//							for(var i=0;i<wvs.length;i++){
				//								console.log('webview'+i+': '+wvs[i].getURL());
				//							}

			} else { //连接失败
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this;
		}
	});
}

//佛宝出厂
function exeOwnGoodVehicleOutFSBG() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","car_trace_no":"' + car_trace_no + '","vehicle_id":"' + vehicle_id + '" }';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "exeOwnGoodVehicleOutFSBG";
	$.ajax({
		type: "post",
		//contentType:"application/json",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if(null != result) {
				mui("#confirm").button('reset');
				if(result.returnStatus == 1) {
					mui.alert("出厂成功!", "提示", "确定", function() {
						var page = plus.webview.getWebviewById('pack_leave_vehicle_scan.html');
						mui.fire(page, 'refresh', {});
					}, "div");
					/*var wvs=plus.webview.all();
					for(var i=0;i<wvs.length;i++){
						console.log('webview'+i+': '+wvs[i].getURL());
					}*/
				} else {
					mui.alert(result.returnDesc, "提示", "确定", function() {}, "div");
				}
			} else { //连接失败
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this;
		}
	});
}