/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var car_trace_no = "";
var vehicle_id = "";
var hand_point_id = "";
// add by gll
var type = "";
var desc = "";
var pack_id = "";
var user_id = localStorage.getItem("account");
// end by gll

$(function() {
	mui.init({
		swipeBack: false //启用右滑关闭功能
	});
	$("#physical_num")[0].focus();
	vehicle_id = localStorage.getItem("vehicle_id");
	$("#vehicle_id").html(vehicle_id);
	if ('' != vehicle_id) {
		localStorage.setItem("putin_vehicle_no", vehicle_id);
	}
	car_trace_no = localStorage.getItem("car_trace_no");
	hand_point_id = localStorage.getItem("hand_point_id");

	console.log("vehicle_id:" + vehicle_id + "  car_trace_no:" + car_trace_no);
	//佛宝个性化自动带出下个目标装卸点
	putoutVoucherDownLoad();

});

window.onload = function onload() {
	mui.plusReady(function() {
		type = plus.webview.currentWebview().type;
		console.log("d7777ss" + type);
		desc = plus.webview.currentWebview().desc;
		pack_id = plus.webview.currentWebview().pack_id;
	});

}

function putoutVoucherDownLoad() {
	console.log(webServiceUrl);
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","car_trace_no":"' + car_trace_no + '","vehicle_id":"' + vehicle_id + '"}';
	console.log(params);
	var method = "exeQueryPutoutNUM";
	console.log(webServiceUrl);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params, 
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(data);
		if (null != data) {
			if (data.out_result == "1") {
				console.log(JSON.stringify(data.packCount));
				$("#putout_num").val(data.packCount);
			} else {
				mui.alert(data.packCount, "提示", "确认", function() {}, "div");
				console.log(data.packCount);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确认", function() {}, "div");
		}
	});
}

//确定
mui(document.body).on("tap", "#query_button", function() {
	console.log("iijijji" + car_trace_no + "56" + vehicle_id);
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var physical_num = $("#physical_num").val();
	var putout_num = $("#putout_num").val();
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","car_trace_no":"' + car_trace_no +
		'","physical_num":"' + physical_num + '","putout_num":"' + putout_num + '"}';
	var method = "addQueryPutoutInfo";
	console.log("parms：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("parms：" + data.out_result + "参数：" + ", " + type + ", " + desc + ", " +
			pack_id);
			//跳转签字签名页面
			mui.openWindow({
				url: "electric_signature.html",
				id: "electric_signature",
				extras: {
					vehicle_no: vehicle_id,
					car_trace_no: car_trace_no,
					type: type,
					desc: desc,
					pack_id: pack_id,
					openType: "signature"
				},
				createNew: true
			});
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
});

//结束装卸货
function exeConfigVeicleFinishHandPoint() {
	<!-- 查询前先关闭软键盘-->
	console.log("进来了");
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var sspointid = "";
	var next_target ="20";
	
	var params = '{"seg_no":"' + segNo + '","user_name":"' + user_id + '","car_trace_no":"' + car_trace_no +
		'","hand_point_id":"' + sspointid + '","next_target":"' + next_target + '"}';
	var method = "exeVeicleFinishHandPoint";
	console.log(params);
	params = encodeURI(params, 'utf-8');
	//TODO 测试 
	console.log("exeConfigVeicleFinishHandPoint结束装卸货方法调用...");
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
	console.log("exeConfigVeicleFinishHandPoint结束装卸货方法调用..."+data);
		if (data != null) {
			console.log(JSON.stringify(data));
			if (data.resultStatus == "1") {
				localStorage.removeItem('vehicle_id');
				localStorage.removeItem('car_trace_no');
				mui.openWindow({
					url: 'vehicle_load_manage.html',
					id: 'vehicle_load_manage',
					createNew: true
				});

			} else {
				var errorinfo = data.codeList;
				mui.alert("失败！原因：" + errorinfo, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}