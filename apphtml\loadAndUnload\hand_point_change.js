var factory_area_id = localStorage.getItem("factory_area_id");
var vehicle_no = ""; //车牌号
var car_trace_no = ""; //车辆跟踪号
var next_target = ""; //下个目标
var next_target_value = ""; //下个目标value值
var next_hand_point_id = ""; //下个装卸点

mui.init({
	swipeBack: true //启用右滑关闭功能
});

//页面加载时查询车辆装卸点信息
window.onload = function onload() {
	mui.plusReady(function() {
		queryVehicleNoInfo();
	});
}

//勾选装卸点信息
mui(document.body).on('selected', '#vehicle_info', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	vehicle_no = $(this).attr("vehicle_no"); //el_J.find("#hand_point_id").text();
	car_trace_no = $(this).attr("car_trace_no");
	//console.log(vehicle_no + ">>>>>>>>>>>>>>>>." + car_trace_no);
});

//更换装卸点弹窗
mui(document.body).on('tap', '#query_hand_point', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	queryzhuangxiedNo();
});

//车辆装卸点信息
function queryVehicleNoInfo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryVehicleNoInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			console.log(JSON.stringify(data.resultDesc));
			var lihtml = "";
			$("#hand_point_List").html(lihtml);
			$.each(data.resultDesc, function(i, item) {
				lihtml = lihtml +
					'<li class="mui-table-view-cell" id = "vehicle_info" vehicle_no =' + item.vehicle_no +
					' car_trace_no =' + item.car_trace_no +
					' >' +
					'<a class="mui-navigate-right">' +
					'<div id="hand_point_name"  style="width: 100px;float: left;">' +
					item.vehicle_no +
					'</div>' +
					'<div id="query_hand_point" style="width:50px;float: right; margin-right: 10px;">' +
					item.hand_point_name +
					'</div>' +
					'</a>' +
					'</li>';
			});
			$("#hand_point_List").html(lihtml);
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

function queryzhuangxiedNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var outUri = domainName + "webService.jsp?callback=?";
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var openType = '0'; //openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
	var lihtml = '<li class="mui-table-view-cell" id = "hand_point_info">' +
		'<a class="mui-navigate-right">' +
		'<div style="width: 48%; float: left; text-align: left;" >' +
		'<label><span class="vehicle_num">离厂</span>' +
		'<span class="point_name"  hidden="hidden">离厂</span>' +
		'</label>' +
		'</div>' +
		'</a>' +
		'</li>';
	var params = '{"seg_no":"' + segNo + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no + '","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	console.log("parms：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		//console.log("data:"+JSON.stringify(data));
		if(data != null) {
			$.each(data.resultList, function(i, item) {
				lihtml = lihtml +
					'<li class="mui-table-view-cell" id = "hand_point_info">' +
					'<a class="mui-navigate-right">' +
					'<div style="width: 48%; float: left; text-align: left;" >' +
					'<label><span class="vehicle_num" style="display:none;">' + item.hand_point_id + '</span>' +
					'<span class="point_name">' + item.hand_point_name + '</span>' +
					'<span class="work_status" style="padding-left:5px">' + item.work_status + '</span>' +
					'</label>' +
					'</div>' +
					'</a>' +
					'</li>';
			});
			$("#carList").html("");
			$("#carList").append(lihtml);
		} else {
			$("#carList").html("");
			$("#carList").append(lihtml);
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//绑定列表选中事件
mui(document.body).on('selected', '#hand_point_info', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	next_hand_point_id = el_J.find(".vehicle_num").text();
	next_target = el_J.find(".point_name").text();
	if(next_target == "离厂") {
		next_target_value = "20";
	} else {
		next_target_value = "10";
	}
	//console.log(next_hand_point_id + "》》》》》》》》》》》》" + next_target);
});

//装卸点变更
function ChangeVehicleHand() {
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var userId = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","modi_person":"' + userId + '","vehicle_no":"' + vehicle_no + '","car_trace_no":"' + car_trace_no + '","next_target":"' + next_target_value + '","target_hand_id":"' + next_hand_point_id + '"}';
	params = encodeURI(params, 'utf-8');
	var method = "exeChangeVehicleHand";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			//	mui("#call_number").button("reset");
			if(data.returnStatus == "1") {
				mui.alert("操作成功", "提示", "确定", function() {
					mui.openWindow({
						id: "load_unload_menus",
						url: "load_unload_menus.html",
						createNew: true
					});
				}, 'div');
				return;
			} else {
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui("#confirm").button("reset");
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//关闭
mui(document.body).on('tap', '#close_pop', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
});

//装卸点变更按钮
mui(document.body).on('tap', '#confirm', function() {
	ChangeVehicleHand();
});