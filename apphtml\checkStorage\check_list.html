<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>盘点列表</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link href="../../css/mui.picker.css" rel="stylesheet" />
		<link href="../../css/mui.poppicker.css" rel="stylesheet" />
		<link rel="stylesheet" href="check_list.css" />
	</head>
	<body>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">盘点列表</h4>
		</div>
		<div class="mui-content" style="margin-top: 3px;padding-top: 0px; " >
			<div style="text-align: left;">
				<div style="margin: 5px; height: 300px; overflow: auto;">
					<ul id="checkList" class="mui-table-view"></ul>
				</div>
			</div>
		</div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/mui.picker.js"></script>
		<script src="../../js/mui.poppicker.js"></script>
		<script src="../../js/util/public.js"></script>
		<script src="check_list.js"></script>
		<!--
        	作者：<EMAIL>
        	时间：2017-07-27
        	描述：
        <script type="text/javascript" charset="utf-8">  
			mui.init({  
		   	beforeback: function() {  
	   			//获得列表界面的webview   
				var list = plus.webview.currentWebview().opener();  
				//触发列表界面的自定义事件（refresh）,从而进行数据刷新  
				mui.fire(list, 'refresh');  
				//返回true，继续页面关闭逻辑  
				return true;  
			}  
		});  
		</script>  -->
	</body>
</html>