/**
 * 初始化变量信息 
 */


var webServiceUrl;
var segNo;
var status;
var user_id;
var isFocus = "-1";


var globalVehicleID = ""; //定义全局车牌号
var globalSysID = "";  //定义全局车辆流水号
var globalCount = "0"; //定义全局车辆数量，默认为0

$(function(){
	mui.init({
		swipeBack:true, //启用右滑关闭功能
	});
});
(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);
window.onload = function onload(){
	mui.plusReady(function(){
		//添加单据信息到ul中
		//showVoucherList(queryVoucherList);
	});
}

/**
 * 初始化页面
 */
$(document).ready(function(){
	webServiceUrl = localStorage.getItem("webServiceUrl");
	segNo = localStorage.getItem("segNo");
	user_id = localStorage.getItem("account");
	getHandType(); //查询页面装卸类型下拉框数据
	queryVehicleList(); //查询车辆列表
});


/**
 * 失去焦点 
 */
$("#car_num").blur(function () {
	isFocus = "0";
});
/**
 * 获得焦点 
 */
$("#car_num").focus(function() {
	isFocus = "1";
});

/**
 * 键盘监听事件 
 */
/*$(document).keyup(function(event){
	var keyCode = event.keyCode;
	if (keyCode == "13") {
		document.activeElement.blur();
	}
	if (isFocus == "1" && keyCode == "229" || keyCode == "8") {
    	queryVehicleList();
	}
});*/

mui(document.body).on('tap','#back',function(){
	mui.back();
});

mui.back = function(){
	var ws=plus.webview.currentWebview();  
	plus.webview.close(ws);
}

/**
 * 查询装卸类型 
 */
function getHandType() {
	var params = "{seg_no:'" + segNo + "',code_type:'HAND_TYPE'}";
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; 
	var method = "exeQueryHandTypeForFSBG";
	params = encodeURIComponent(params);
	var handTypeHtml = "";
	$.ajax({
		type: "post",
		url : outUri,
		data : {innerUri:innerUri,params:params,method:method},
		dataType : "json",
		timeout : 3000,
		async : true,
		cache : false,
		success : function (data) {
			if (data != null) {
				$.each(data.list, function(i,item) {
					handTypeHtml = handTypeHtml + "<option value='" + item.VALUE + "'>" + item.LABEL + "</option>";
				});
				$("#hand_type").html(handTypeHtml);
			} else {
				mui.toast("网络超时，请联系管理员！")
			}
		},
		error : function () {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}


/**
 * 查询车辆列表 
 */
function queryVehicleList(){
	var tempHtml = "";
	globalCount = "0";
	var car_num = $.trim($("#car_num").val());
	// if (car_num.length > 6 || car_num == "") {
	// 	console.info(car_num);
	// 	$("#vehicle_list").html(tempHtml);
	// 	$("#count").html(globalCount);
	// 	return;
	// }
	
	var province = $.trim($("#province_name").text());// 获取车牌信息的省份简称
	var vehicle_id = province + car_num.toUpperCase(); //车牌号
	var params = "{seg_no:'" + segNo + "',vehicle_id:'" + vehicle_id + "'}";
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; 
	var method = "exeQueryVehicleWeatherExists";
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url : outUri,
		data : {innerUri:innerUri,params:params,method:method},
		dataType : "json",
		timeout : 3000,
		async : true,
		cache : false,
		success : function (data) {
			if (data.result == "1") {
				$.each(data.resultData, function(i,item) {
					//voucherinfo.put(item.voucher_id,item);
					globalCount = data.resultData.length;
					tempHtml = tempHtml +	'<li class="mui-table-view-cell">' +
												'<a class="mui-navigate-right">' +
													'<div>' +
														'<div class="row"><span id="voucher_num">' + item.vehicle_id + '</span></div>' +
														'<div class="row"><input type="hidden" value="' + item.sys_id + '"/></div>' +
														//'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
													'</div>' +
												'</a>' +
											'</li>';
				});
			} 
			$("#vehicle_list").html(tempHtml);
			$("#count").html(globalCount);
		},
		error : function () {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//绑定单据点击事件
mui(document.body).on('tap','li',function(){
	var a = $(this).children('a');
	$("#car_num").blur();
	if(a.hasClass("select") == false){
		globalVehicleID = a.children('div').children('div').children('span').html().trim();
		globalSysID = a.children('div').children('div').next().children('input').val().trim();
		console.log(globalSysID);
		$(".mui-navigate-right").removeClass("select"); //移除其他的车辆信息的红勾
		a.addClass("select"); //为当前的li元素添加勾选样式
	}else{
		globalVehicleID = "";
		globalSysID = "";
		a.removeClass("select");
	}
});

/**
 * 车辆进厂 
 */
mui(document.body).on('tap','#confirm',function(){
	$("#car_num").blur();
	isFocus = "0";
	if (globalCount != "0" && globalSysID == "") {
		mui.toast("请选择进厂车辆！")
		return;
	}
	if (globalVehicleID == "") { //如果该变量为空，说明没有从车牌信息列表中选中数据，则获取车牌文本框里的数据
		var car_num = $.trim($("#car_num").val());
		if (car_num == "") {
			mui.toast("请输入车牌号码！");
			return;
		}
		var province = $.trim($("#province_name").text());// 获取车牌信息的省份简称
		globalVehicleID = province + car_num.toUpperCase(); //车牌号
		console.log(globalVehicleID);
		var vehicel_id_length = globalVehicleID.length;	
			
		var vehicel_id_reg;
		if(vehicel_id_length == '8'){
			console.log("长度：" + vehicel_id_length);
			vehicel_id_reg = /^[京津冀黑辽吉蒙新藏青甘陕宁晋鲁豫川云渝贵湘鄂皖苏沪浙赣闽粤桂琼]{1}[A-Za-z]{1}[A-Z0-9a-z]{5}[A-Za-z0-9挂]{1}$/;
		}else{
			console.log("长度：" + vehicel_id_length);
			vehicel_id_reg = /^[京津冀黑辽吉蒙新藏青甘陕宁晋鲁豫川云渝贵湘鄂皖苏沪浙赣闽粤桂琼]{1}[A-Za-z]{1}[A-Z0-9a-z]{4}[A-Za-z0-9挂]{1}$/;
		}
		//var vehicel_id_reg = /^[京津冀黑辽吉蒙新藏青甘陕宁晋鲁豫川云渝贵湘鄂皖苏沪浙赣闽粤桂琼]{1}[A-Za-z]{1}[A-Z0-9a-z]{4}[A-Za-z0-9挂]{1}$/;
		if (!vehicel_id_reg.test(globalVehicleID)) { // 判断车牌是否匹配正则表达式
			mui.toast("车牌号码格式不正确！");
			globalVehicleID = "";
			return;
		}
	}
	//modify by Luo Yinghui at 2018-12-05
	var hand_type = $("#hand_type option:selected").val();
	console.log("装卸货类型value：" + hand_type);
	if (hand_type == "00" || hand_type == "" || hand_type == null) {
		mui.toast("请选择车辆装卸类型！");
		return;
	}
	$("#vehicle_list").html("");
	$("#count").html("0");
	//end by Luo Yinghui
	console.log(globalVehicleID + "||");
	var params = "{seg_no:'" + segNo + "',vehicle_id:'" + globalVehicleID + "',sys_id:'" + globalSysID + "',user_id:'" + user_id + "',hand_type:'" + hand_type + "'}";
	console.log(params);
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; 
	var method = "exeVehicleEnterFactoryOrAdd";
	params = encodeURIComponent(params);
	$("#car_num").val("");
	$.ajax({
		type: "post",
		url : outUri,
		data : {innerUri:innerUri,params:params,method:method},
		dataType : "json",
		timeout : 3000,
		async : true,
		cache : false,
		success : function (data) {
			if (data.returnResult == "1") {
				mui.toast("车辆进厂成功！");
				$("#vehicle_list").html("");
				globalVehicleID = "";
				globalCount = "0";
				$("#count").html(globalCount);
				/*$.each(data.resultData, function(i,item) {
					//voucherinfo.put(item.voucher_id,item);
					count = data.resultData.length;
					tempHtml = tempHtml +	'<li class="mui-table-view-cell">' +
												'<a class="mui-navigate-right">' +
													'<div>' +
														'<div class="row"><span id="voucher_num">' + item.vehicle_id + '</span></div>' +
														'<div class="row"><input type="hidden" value="' + item.sys_id + '"/></div>' +
														//'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
													'</div>' +
												'</a>' +
											'</li>';
				});*/
			} else if (data.resultStatus == "0"){
				globalVehicleID = "";
				mui.toast("车辆进厂失败:" + data.resultDesc);
			} else {
				mui.toast("车辆进厂失败，请检查数据或联系管理员！");
			}
		},
		error : function () {
			mui.toast("连接超时，请检查网络或联系管理员！");
		}
	});
});	


			
/**
 * 车牌省份简称按钮选择事件  add by Luo Yinghui at 2018-11-11
 * @param {Object} ths
 */
function chooseProvince(ths) {
	$(ths).addClass('active').siblings().removeClass('active');
	var province = $(ths).text();
	$("#province_name").html(province);
	queryVehicleList();
	if (fadeFlag == 1) {
		$("#province").fadeOut();
		fadeFlag = 0;
	} else {
		$("#province").fadeIn();
		fadeFlag = 1;
	}
}

/**
 * 车牌省份简称信息淡入淡出事件    add by Luo Yinghui at 2018-11-11
 * 
 */
var fadeFlag = 0; //标记
$("#province_name").click(function() {
	if (fadeFlag == 1) {
		$("#province").fadeOut();
		fadeFlag = 0;
	} else {
		$("#province").fadeIn();
		fadeFlag = 1;
	}
});

/*$("#confirm_province").click(function () {
	$("#province").fadeOut();
	fadeFlag = 0;
});*/

/**
 * DOM页面触摸事件
 */
$(document).bind('touchstart',function(e){
	var x = e.originalEvent.targetTouches[0].pageX; //获取触碰点的X坐标
	var y = e.originalEvent.targetTouches[0].pageY; //获取触碰点的Y坐标
	var div = document.getElementById("province_name");  
	var height = $(window).height(); //获取车牌省份简称DIV的高度
	//var width = $(window).width(); //获取车牌省份简称的DIV的宽度
	if (((height/8)*2) > y && fadeFlag == "1") { //如果触碰的位置不在省份信息div元素，则将div淡出隐藏
		$("#province").fadeOut();
		fadeFlag = 0;
	}
});