<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>库位推荐</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="alloc_storage_location.css" />
	</head>

	<body>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">库位变更</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;">
				<!-- 具体内容 -->
				<!-- 倒库捆包 -->
				<div class="mui-input-row mui-search>">
					<div class="fourtext">捆&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;包</div>
					<div id ="allocContext"><input id="pack_id" type="text"  style="width: 42%;">
					<input id="inner_diameter" type="text" placeholder="  卷内径" style="width: 26%;font-size: 16px;"> 
						<a href="#"><span class="mui-icon mui-icon-search" id="innerDiameterQueryBtn"></span></a>
					</div>
				</div>
				
				<div class="mui-input-row detail_row">
					<div class="fourtext" id ="allocLabel">分配库位</div>
					<div id ="allocContext"> <input id="fp_location" type="text" readonly="readonly"> 
						<input id="fp_location_s" type="text" readonly="readonly">
					</div>
				</div>

				<!-- 新库位 -->
				<div class="mui-input-row detail_row">
					<div class="fourtext" style="color: red;">实际库位</div>
					<div id ="confirmContext"> 
						<select id="up_down_flag" class="select" style="height: 40px;">
						  <option value="1">下层</option>
						  <option value="2">上层</option>
						</select>
						<input id="pack_location_new" type="text" style="width: 28%;">
						<input id="new_loc_view_id" type="text" style="width: 22%;"> 
						
					</div>
				</div>
				
				<!--相邻捆包 adjacent_pack_id-->
				<div class="detail_row">
					<div class="fourtext" style="color: red" >相邻捆包</div>
						<div>
							<input id="adjacent_pack_id" type="text" style="width:68%;" class="mui-input-clear" > 
						</div>
					</div>
				</div>
				
				<!-- 紧靠方向 -->
				<div class="detail_row">
					<div class="fourtext" style="color: blue;">紧靠方向</div>
					<div>
						<button id="left_location_btn" type="button" class="mui-btn">左</button>
						<button id="right_location_btn" type="button" class="mui-btn">右</button>
					</div>
				</div>

				<!-- 捆包信息 
				<div class="pack_list" overflow: auto;>
					<ul class="mui-table-view" id="storage_list" >
					</ul>
				</div>
				-->
				<!-- 实际坐标-->
				<div class="detail_row">
					<div class="fourtext" style="color: red;">实际坐标</div>
					<div id ="confirmContext"> 
						<input id="x_point_start" type="text"> 
						<input id="x_point_end" type="text">
					</div>
				</div>
				
				
			</div>
			<!-- 按钮 -->
				<div class="mui-input-row">
					<button id="storage_but" type="button" class="mui-btn mui-btn-primary">确&nbsp;&nbsp; &nbsp; &nbsp;认</button>
				</div>
					<!--
						<button id="storage_realloc" type="button" class="mui-btn mui-btn-primary">库位推荐</button>
					-->
				
		</div>
		<!--智慧仓库-内径选择列表-->
		<div id = "InnerDiameterDiv">
			<div class="title">请手工选择卷内径</div>  
			<div style="margin: 5px; height: 170px; overflow: auto; " id="innerDiameterInfo">
				<ul id="InnerDiameterList" class="mui-table-view mui-table-view-radio"/>
			</div>
			<div class="mui-input-row" style="margin-top: 1px;">
				<button id="innerDiameterConfirm" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">确认</button>
				<button id="cancel" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">取消</button>
			</div> 
		</div>

		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/pda/time.js" type="text/javascript" charset="utf-8"></script>
		<script src="../../js/util/public.js"></script>
		<script src="alloc_storage_location.js"></script>
	</body>

</html>