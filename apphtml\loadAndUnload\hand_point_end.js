/**
 * 初始化变量信息 
 */
var car_trace_no = "";
var product_id = "";
var hand_point_id = "";
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var factory_area_id = localStorage.getItem("factory_area_id");
var factory_area_name = localStorage.getItem("factory_area_name");
var next_target_value = ""; //next_target_value  20离厂   10代表下个装卸点
$(function() {
	mui.init({
		swipeBack: false //启用右滑关闭功能
	});
	var vehicle_id = localStorage.getItem("vehicle_id");
	$("#vehicle_id").html(vehicle_id);
	if ('' != vehicle_id) {
		localStorage.setItem("putin_vehicle_no", vehicle_id);
	}
	car_trace_no = localStorage.getItem("car_trace_no");
	hand_point_id = localStorage.getItem("hand_point_id");
	if (segNo == "00118") {
		$("#morebut").attr("style", "display:none;");
	}
	console.log("vehicle_id:" + vehicle_id + "  car_trace_no:" + car_trace_no);
	//佛宝个性化自动带出下个目标装卸点
	if (segNo == "00126") {
		queryzhuangxiedNos(10);
	}

});
window.onload = function onload() {
	mui.plusReady(function() {
		var putin_scan = plus.webview.getWebviewById("putin_scan");
		plus.webview.close(putin_scan, "none");
		/*var wvs=plus.webview.all();  
				for(var i=0;i<wvs.length;i++){  
				console.log("webview"+i+": "+wvs[i].getURL());  
				} */
	});
}

//装卸结束按钮
mui(document.body).on("tap", "#config_button", function() {
	if ($("#next_hand_point").val() == null || $("#next_hand_point").val() == "") {
		mui.alert("请选择下个目标", "提示", "确定", null, 'div');
		return;
	}
	var hand_point_name = $("#next_hand_point").val();
	if (hand_point_name == "离厂") {
		next_target_value = "20";
	} else {
		next_target_value = "10";
	}
	if (hand_point_name == "离厂" && (segNo == '00129' || segNo == '00145' || segNo == '00138')) {
		//天津宝钢和宝井离厂时判断是否有未装的捆包
		exeQueryUnPutoutPack();
	} else {
		exeConfigVeicleFinishHandPoint();
	}
});

/**
 * 查询是否还有未装车的捆包 add by yangzemin 20200707
 */
function exeQueryUnPutoutPack() {
	var vehicle_id = localStorage.getItem("vehicle_id");
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';

	var params = '{"seg_no":"' + segNo + '","car_trace_no":"' + car_trace_no +
		'","vehicle_no":"' + vehicle_id + '"}';
	var method = "exeQueryPackByVehicle";
	console.log(method + "参数：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		console.log(method + "返回：" + JSON.stringify(data));
		if (data != null) {
			if (data.resultList.length > 0) {
				var packs = '';
				$.each(data.resultList, function(i, item) {
					if (packs != '') {
						packs += ",";
					}
					packs += item.PACK_ID;
				});
				console.log("捆包拼接：" + packs);

				var btnArray = ['否', '是'];
				mui.confirm('此配车单下有捆包号' + packs + '未出库，是否继续？', '提示', btnArray, function(e) {
					if (e.index == 1) {
						//是
						checkFinishSignature();
					}
				});
			} else {
				checkFinishSignature();
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//检查结束装卸货的电子签名开关
function checkFinishSignature() {
	var signature = getSwitchValue(segNo, 'LEAVE_FACTORYO_SIGNATURE');
	console.log("电子签名开关：" + signature);
	if (signature == '1') {
		//判断装卸货类型，装货类型跳转签名
		//add by gll
		if(segNo == '00138'){
			qufenZhuangxie();
		}else{
			exeQueryTruckType();
		}
		//end by gll
	} else {
		exeConfigVeicleFinishHandPoint();
	}
}


/**
 * 查询当前车辆装卸类型 add by yangzemin 20200716
 */
function exeQueryTruckType() {
	var vehicle_id = localStorage.getItem("vehicle_id");
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';

	var params = '{"seg_no":"' + segNo + '","car_trace_no":"' + car_trace_no +
		'","vehicle_no":"' + vehicle_id + '"}';
	var method = "exeQueryVehicleTruckType";
	console.log(method + "参数：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		console.log(method + "返回：" + JSON.stringify(data));
		if (data != null) {
			if (data.resultList.length > 0) {
				var truckType = data.resultList[0].hand_big_type;
				var truckWeight = data.resultList[0].putout_weight;
				console.log("truckType：" + truckType);
				//通过出库量判断是否是处理
				// if (truckType == '10' || truckType == '30') {
				if (truckWeight > 0) {
					//车辆有装货，需司机电子签名
					var btnArray = ['不同意', '同意'];
					mui.confirm('出库量：' + truckWeight + '吨\n货物完好，数量无误', '请确认', btnArray, function(e) {
						if (e.index == 1) {
							//跳转签字签名页面
							mui.openWindow({
								url: "electric_signature.html",
								id: "electric_signature",
								extras: {
									vehicle_no: vehicle_id,
									car_trace_no: car_trace_no,
									openType: "signature"
								},
								createNew: true
							});
						}
					});
					

				}
			} else {
				//结束装卸货
				exeConfigVeicleFinishHandPoint();
			}
		} else { //连接失败
			mui.alert("连接服务器异常，请重试", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//结束装卸货
function exeConfigVeicleFinishHandPoint() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var sspointid = "";
	if ($("#next_hand_point_id").val() == "离厂") {
		sspointid = "";
	} else {
		sspointid = $("#next_hand_point_id").val();
	}
	var params = '{"seg_no":"' + segNo + '","user_name":"' + user_id + '","car_trace_no":"' + car_trace_no +
		'","hand_point_id":"' + sspointid + '","next_target":"' + next_target_value + '"}';
	var method = "exeVeicleFinishHandPoint";
	console.log(params);
	params = encodeURI(params, 'utf-8');
	//TODO 测试 
	console.log("exeConfigVeicleFinishHandPoint结束装卸货方法调用...");
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if (data != null) {
			console.log(JSON.stringify(data));
			if (data.resultStatus == "1") {
				var next_hand_point = $("#next_hand_point").val();
				var next_hand_point_id = $("#next_hand_point_id").val();
				if (next_hand_point != "离厂") {
					//屏蔽原因 工厂基本上一个作业点一个pda 
					//localStorage.setItem("hand_point_id",next_hand_point_id);
					//localStorage.setItem("hand_point_name",next_hand_point);
				} else {
					localStorage.removeItem('vehicle_id');
					localStorage.removeItem('car_trace_no');
				}
				mui.openWindow({
					url: 'vehicle_load_manage.html',
					id: 'vehicle_load_manage',
					createNew: true
				});

			} else {
				var errorinfo = data.codeList;
				mui.alert("失败！原因：" + errorinfo, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//花都宝井自动查询下个装卸点
function exeQueryNextHandPoint() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","car_trace_no":"' + car_trace_no + '"}';
	var method = "exeQueryNextHandPoint";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if (data != null) {
			if (data.resultStatus == "1") {
				$.each(data.resultDesc, function(i, item) {
					$("#next_hand_point_id").val(item.hand_point_id);
					$("#next_hand_point").val(item.hand_point_name);
				});
			} else {
				$("#next_hand_point").val("离厂");
				//							var errorinfo = data.resultDesc;
				//							 mui.alert("失败！原因："+errorinfo, "提示", "确定", function() {
				//							}, 'div');
				//							 return ;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//查询装卸点名称
//			function queryHandPointEndName() {
//				<!-- 查询前先关闭软键盘-->
//				document.activeElement.blur();
//				var outUri = domainName+"webService.jsp?callback=?";
//				var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
//				var	openType = 'hand_point_end';//openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
//				var params = '{"seg_no":"'+segNo+'","factory_area":"'+factory_area_id+'","car_trace_no":"'+car_trace_no+'","openType":"'+openType+'"}';
//				var method = "exeQueryHandPoint";
//				params=encodeURI(params,'utf-8');
//				$.getJSON(outUri, {innerUri: innerUri,params: params,method: method}, function(data) { //如返回对象有一个username属性 
//					if(data != null) {
//						console.log(data.resultList.length);
//						if(data.resultList.length == 0){
//							/* var chtml = "<li class='mui-table-view-cell' p_id='离厂' p_name='离厂'>离厂</li>";
//							$("#selectdata").html(chtml);*/
//							
//						}else{
//							 var chtml = "<li class='mui-table-view-cell' p_id='离厂' p_name='离厂'>离厂</li>";
//							$.each(data.resultList,function(i, item) {
//								chtml+="<li class='mui-table-view-cell' p_id='"+item.hand_point_id+"' p_name='"+item.hand_point_name+"' >"+item.hand_point_name+"</li>";
//							});
//							$("#selectdata").html(chtml);
//						}
//					} else { //连接失败
//						mui.alert("连接服务器异常", "提示", "确定", function() {
//						}, 'div');
//						return ;
//					}
//				}); 
//			}
/*
 * 继续装货
 */
mui(document.body).on('tap', '#next_start_load', function() {
	//高强钢采用以前的
	if (segNo == '00138' || segNo == '00181') {
		mui.openWindow({
			url: '../putout/putout_list_new.html',
			id: 'putout_list_new',
			extras: {
				openType: 'vehicle_load_manage',
			},
			createNew: true,

		});
	} else {
		mui.openWindow({
			url: 'hand_point_list.html',
			id: 'hand_point_list',
			createNew: true,
		});
	}

});
/*
 * 继续卸货
 */
mui(document.body).on('tap', '#next_start_unload', function() {
	mui.openWindow({
		url: '../putin/putin_scan.html',
		id: 'putin_scan',
		extras: {
			openType: 'vehicle_load_manage',
		},
		createNew: true,
	});
});

//下个目标
mui(document.body).on('tap', '#next_hand_point', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#hid").val("");
	hhtype = "10";
	queryzhuangxiedNo(hhtype);
});
//下个目标
mui(document.body).on('tap', '#morebut', function() {
	hhtype = "20"; //更多
	queryzhuangxiedNo(hhtype);
});

function queryzhuangxiedNo(hhtype) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var openType = 'hand_point_end'; //openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
	if (hhtype == "10") {
		openType = 'hand_point_end';
		if (segNo == "00118") {
			openType = '';
		}
	} else {
		openType = '';
	}
	/*if (segNo == "00138") {
		factory_area_id = "";
	}*/
	var lihtml = '<li class="mui-table-view-cell">' +
		'<a class="mui-navigate-right">' +
		'<div style="width: 48%; float: left; text-align: left;" >' +
		'<label><span class="vehicle_num">离厂</span>' +
		'<span class="point_name"  hidden="hidden">离厂</span>' +
		'</label>' +
		'</div>' +
		'</a>' +
		'</li>';
	var params = '{"seg_no":"' + segNo + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no +
		'","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	console.log("parms：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		console.log("data:" + JSON.stringify(data));
		if (data != null) {
			$.each(data.resultList, function(i, item) {
				if ("00126" == segNo) { //佛宝显示 装卸点状态
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;" >' +
						'<label><span class="vehicle_num" style="display:none;">' + item.hand_point_id + '</span>' +
						'<span class="point_name">' + item.hand_point_name + '</span>' +
						'<span class="work_status" style="padding-left:5px">' + item.work_status + '</span>' +
						'</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				} else {
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;" >' +
						'<label><span class="vehicle_num">' + item.hand_point_id + '</span>' +
						'<span class="point_name">' + item.hand_point_name + '</span>' +
						'</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				}
			});
			$("#carList").html("");
			$("#carList").append(lihtml);
		} else {
			$("#carList").html("");
			$("#carList").append(lihtml);
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});

}
//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	carnum = el_J.find(".vehicle_num").text();
	carname = el_J.find(".point_name").text();
	$("#hid").val(carnum);
	$("#hidname").val(carname);
});
//搜索
mui(document.body).on('tap', '#query_button', function() {
	hhtype = "10";
	queryzhuangxiedNo(hhtype);
});
//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	carnum = $("#hid").val();
	carname = $("#hidname").val();
	/*if(carnum=="" || carnum ==null || carnum =="undefine"){
		mui.alert("请选择装卸点","提示","确认",function() {}, "div");
		return false;
	}else{*/
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#next_hand_point").val(carname);
	$("#next_hand_point_id").val(carnum);
	//}
});

mui.back = function() {
	mui.alert("请选择继续装卸货或者装卸结束", "提示", "确定", function() {}, "div");
	return false;
	/*var btnArray = ['是', '否'];
	mui.confirm('是否退出应用', '工贸PDA', btnArray, function(e) {
		if(e.index == 0) {
			plus.runtime.quit();
		}
	})*/
};

function queryzhuangxiedNos(hhtype) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var openType = 'hand_point_end'; //openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
	var params = '{"seg_no":"' + segNo + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no +
		'","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	console.log("parms：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("data:" + JSON.stringify(data));
			console.log(JSON.stringify(data.resultList));
			console.log(JSON.stringify(data.resultList[0]));
			if (JSON.stringify(data.resultList[0]) != null) {
				console.log(JSON.stringify(data.resultList[0].hand_point_name) + JSON.stringify(data.resultList[0].hand_point_id));
				var HandPointId = data.resultList[0].hand_point_name;
				var HandPointName = data.resultList[0].hand_point_id;
				$.each(data.resultList, function(i, item) {
					if (item.work_status == "空闲") {
						$("#next_hand_point").val(item.hand_point_name);
						$("#next_hand_point_id").val(item.hand_point_id);
						return false;
					} else {
						$("#next_hand_point").val(HandPointId);
						$("#next_hand_point_id").val(HandPointName);
					}
				});
			} else {
				$("#next_hand_point").val("离厂");
				$("#next_hand_point_id").val("离厂");
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//add by gll IDBObjectStore
function qufenZhuangxie (){
	var vehicle_id = localStorage.getItem("vehicle_id");
	console.log("iijijji"+vehicle_id+"56"+ car_trace_no);
	
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAQueryService';
	var openType = 'hand_point_end'; //openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","car_trace_no":"' + car_trace_no + '"}';
	var method = "exeVehicleInfoType";
	console.log("parms：" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("parms：" + data.resultStatus);
			//var type = data.resultList[0].check_type;
			var type = data.resultStatus;
			console.log("type：" + type);
			if (type == "1") {
				console.log("type：484" );
				var btnArray = ['已告知司机', ''];
				mui.confirm("货物完好，数量无误；确认雨布已盖",'请确认', btnArray, function(e) {
						
							//跳转签字签名页面
							mui.openWindow({
								//跳转出库信息页面
								url: "putout_info.html",
								id: "putout_info",
								//跳转出库信息页面
								extras: {
									vehicle_no: vehicle_id,
									car_trace_no: car_trace_no,
									type: "10",  
									pack_id: "",
									openType: "signature"
								},
								createNew: true
							});
						
					});
				
			} else if(type == "2") {
				var type = data.resultStatus;
				var desc =data.resultList[0].quality_desc; 
				var pack_id =data.resultList[0].pack_id;
				//跳转签字签名页面
				mui.openWindow({
					//跳转出库信息页面
					url: "putout_info.html",
					id: "putout_info",
					//跳转出库信息页面
					//跳转出库信息页面
					extras: {
						vehicle_no: vehicle_id,
						car_trace_no: car_trace_no,
						type: "20",
						desc: desc,
						pack_id: pack_id,
						openType: "signature"
					},
					createNew: true
				});
				// 判断质量描述是不是正常
				
			}else if (type == "3"){
				var type = data.resultStatus;
				var desc =data.resultList[0].quality_desc; 
				var pack_id =data.resultList[0].pack_id;
				var btnArray = ['已告知司机', ''];
				mui.confirm("货物完好，数量无误；确认雨布已盖",'请确认', btnArray, function(e) {
						//if (e.index == 1) { 
				mui.openWindow({
					//跳转出库信息页面
					url: "putout_info.html",
					id: "putout_info",
					//跳转出库信息页面
					extras: {
						vehicle_no: vehicle_id,
						car_trace_no: car_trace_no,
						type: "30",
						desc: desc,
						pack_id: pack_id,
						openType: "signature"
					},
					createNew: true
				});
				//}
				});
				//跳转签字签名页面
				
			}else if (type == "5"){
				var type = data.resultStatus;
				var btnArray = ['已告知司机', ''];
				mui.confirm("货物完好，数量无误；确认雨布已盖",'请确认', btnArray, function(e) {
						//if (e.index == 1) { 
				mui.openWindow({
					//跳转出库信息页面
					url: "putout_info.html",
					id: "putout_info",
					//跳转出库信息页面
					extras: {
						vehicle_no: vehicle_id,
						car_trace_no: car_trace_no,
						type: "30",
						desc: "",
						pack_id: "",
						openType: "signature"
					},
					createNew: true
				});
				//}
				});
				//跳转签字签名页面
				
			}
			
			else{
				//结束装卸货
				exeConfigVeicleFinishHandPoint();
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
	
}
