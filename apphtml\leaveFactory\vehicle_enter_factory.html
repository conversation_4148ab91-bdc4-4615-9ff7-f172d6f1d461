<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>车辆进厂</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="vehicle_enter_factory.css" />			
	</head>

	<body>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">车辆进厂</h4>
		</div>
		<div class="mui-input-row detail_row">
			<div class="text"><span id="required">*</span>车&nbsp;&nbsp;&nbsp;牌&nbsp;&nbsp;&nbsp;号</div>
			<div>
				<button id="province_name">粤</button>
				<input id="car_num" type="text" oninput="queryVehicleList()" style="margin-left:3px;width:42%;float:left;" class="mui-input-clear"/>
			</div>
			
		</div>
		<div>
			<div class="text"><span id="required">*</span>装&nbsp;&nbsp;卸&nbsp;&nbsp;类&nbsp;&nbsp;型</div>
			<select id="hand_type">
				
			</select>
					
		</div>
		<div style="margin-left:10px;margin-top:5px;font-size: 16px;">共计&nbsp;<span id="count" style="color:red;font-size: 20px;font-weight: bolder;">0</span>&nbsp;条记录：</div>
		<div class="mui-content" style="margin-top: 4px;padding-top: 0px;">
			<div class="mui-input-row" style="border-radius: 3px;border: 1px solid #CCCCCC;height: 300px;overflow-y: auto;">
				<ul class="mui-table-view" id="vehicle_list">
					 
					<!--<li class="mui-table-view-cell">
						<a class="mui-navigate-right">
							<div>
								<div class="row"><span id="voucher_num">EL1610160002</span><span class="icon">量</span>163.276</div>
								<div class="row"><span class="icon">客</span>成都同泰汽车零部件有限公司</div>
								<div class="row"><span class="icon">收</span>成都同泰汽车零部件有限公司</div>
							</div>
						</a>
					</li>
					 -->
				</ul>
			</div>
			
			<!-- 确认按钮 -->
			<!--<div class="mui-input-row" style="margin-top: 5px;">
				<button id="confirm" type="button" class="mui-btn mui-btn-blue" style="width: 100%;font-size: 22px; line-height: 1.8;">确&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;定</button>
			</div>-->
			
		</div>
		
		<div id="confirmDiv">
			<button id="confirm" type="button" class="mui-btn mui-btn-blue">入&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;厂</button>
		</div>
		<div id="province">
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">粤</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">桂</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">琼</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">湘</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">赣</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">闽</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">浙</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">沪</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">川</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">贵</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">云</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">渝</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">鄂</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">皖</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">苏</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">鲁</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">豫</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">陕</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">甘</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">青</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">宁</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">晋</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">冀</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">京</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">津</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">藏</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">新</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">蒙</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">辽</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">吉</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">黑</button>
			<!--<button id="confirm_province" type="button" class="mui-btn mui-btn-primary">确认</button>-->
		</div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script src="vehicle_enter_factory.js"></script>
	</body>

</html>