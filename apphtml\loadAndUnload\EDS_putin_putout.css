.mui-control-content {
	background-color: white;
	min-height: 280px;
}

.mui-segmented-control {
	font-size: 22px;
}

.mui-table-view-cell {
	font-size: 22px;
	padding: 6px 0px 8px 18px;
	text-align: left;
}


/** 明细样式 */

.left {
	float: left;
	width: 50%;
}

.mui-navigate-right:after {
	content: '';
}

.select:after {
	content: '\e472';
	color: red;
	font-size: 50px;
	font-weight: 600;
	right: 10px;
}

#voucher_num {
	font-size: 22px;
	color: blue;
	margin-right: 10%;
}

.icon {
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}

.row {
	margin: 5px;
	font-size: 18px;
}

.my_input {
	width: 70%!important;
}

.mui-checkbox {
	font-size: 14px;
}

#zhuang,
#xiehuo,
#jieshu {
	width: 29%;
	font-size: 20px;
	line-height: 1.8;
	padding-left: 3px;
}


/** 弹出图层设置 */

#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	/*text-align: center;*/
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
	margin-left: 10px;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

.next_hand_point {
	color: blue;
	font-size: 22px;
}

#div {
	width: 0px;
	height: 0px;
	background: red;
	position: fixed;
	top: 70%;
	left: 50%;
}

/*移除底部或顶部三角,需要在删除此代码*/
.mui-popover .mui-popover-arrow:after {
	width: 0px;
}


/*更多按钮*/

.morebut_s {
	margin-left: 25%;
	margin-top: -6px;
	background-color: green;
	color: white;
}

.divnone {
	display: none;
}

.point_name {
	color: blue;
	margin-left: 10px;
}

.packbutton {
	width: 50px;
	font-size: 18px;
	padding-left: 5px;
	margin-left: 50px;
}


.packbutton2 {
	width: 50px;
	font-size: 18px;
	padding-left: 5px;
	margin-left: 20px;
}

.mui-checkbox li {
	height: 45px;
}

.firstspan {
	display: inline-block;
	/* width: 35%;*/
	font-size: 16px;
	margin-right: 0px;
	line-height: 35px;
	height: 30px;
	width: 110px;
}

.mui-checkbox button {
	width: 40px;
	font-size: 14px;
	padding-left: 5px;
}

.mui-left span:nth-child(5) {
	display: none;
}

.mui-left span:nth-child(4) {
	display: none;
}

.icon-search {
	position: absolute;
	left: 250px;
	z-index: 5;
	background-image: url(../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 5px;
}

.allvi {
	position: relative;
	margin-top: 0;
	margin-bottom: 0;
	padding-left: 0;
	list-style: none;
	background-color: #fff;
	width: 280px;
	margin-left: 50px;
}

.vehicleli {
	height: 40px;
	line-height: 40px;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}