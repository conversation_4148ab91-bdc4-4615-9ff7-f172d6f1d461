/**
 * 初始化变量信息 
 */
var scanPackList = new Array(); //入库捆包列表
var vehicle_id = ""; //车牌号
var putinScanPage = "";
var packListPage = "";

var putinScanTransferPage = "";

$(function() {
	mui.init({
		swipeBack: false //启用右滑关闭功能
	});

	(function($) {
		//修改、删除事件
		$('#pack_putin_list').on('tap', '.mui-btn.mui-btn-blue', function(event) {
			var elem = this;
			var li = elem.parentNode.parentNode; //向上找两个父节点正好对应li节点
			var li_J = getJQueryObject(li);
			mui.confirm('确认修改该条记录？', '提示', ['确认', '取消'], function(e) {
				if(e.index == 0) {
					var ws = plus.webview.currentWebview();
					plus.webview.close(ws);
					//返回捆包扫描页面编辑捆包
					editPackById(li_J.find("#pack_id").text());
				} else {
					setTimeout(function() {
						$.swipeoutClose(li);
					}, 0);
				}
			}, 'div');
		});

		$('#pack_putin_list').on('tap', '.mui-btn.mui-btn-red', function(event) {
			var elem = this;
			var li = elem.parentNode.parentNode;
			var li_J = getJQueryObject(li);
			mui.confirm('确认删除该条记录？', '警告', ['确认', '取消'], function(e) {
				if(e.index == 0) {
					deletePackById(li_J.find("#pack_id").text());
					//删除捆包
					li.parentNode.removeChild(li);
				} else {
					setTimeout(function() {
						$.swipeoutClose(li);
					}, 0);
				}
			}, 'div');
		});
	})(mui);
});

window.onload = function onload() {
	mui.plusReady(function() {
		scanPackList = plus.webview.currentWebview().scanPackList;
		vehicle_id = plus.webview.currentWebview().vehicle_id;
		putinScanPage = plus.webview.getWebviewById("changchun_synergy_scan");
		packListPage = plus.webview.getWebviewById("changchun_synergy_pack_list");
		//添加捆包信息到ul中
		showPackList(scanPackList);
	});
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	//否则返回普通入库捆包扫描页面	
	mui.fire(putinScanPage, 'back', {
		scanPackList: scanPackList,
		vehicle_id: vehicle_id
	});
	putinScanPage.show();
	packListPage.close();
}

function showPackList(packList) {
	$.each(packList, function(i, value) {
		$("#pack_putin_list").append('<li class="mui-table-view-cell"><div class="mui-slider-right mui-disabled"><a class="mui-btn mui-btn-blue mui-icon">修改</a><a class="mui-btn mui-btn-red mui-icon">删除</a></div><div class="mui-slider-handle"><div><div id="pack_id">' +
			value.pack_id +
			'</div><div><div id="product_process_id" class="left"><span>首</span><label>' +
			value.product_process_id +
			'</label></div><div id="spec"><span>规</span><label>' +
			value.spec +
			'</label></div></div></div><div><div style="clear: both;"></div><div class="left" id="pack_location"><span>库</span><label>' +
			value.location_desc +
			'</label></div><div id="weight"><span>重</span><label>' +
			value.weight * 1000 +
			'</label></div></div></div></li>');
	});
}

function deletePackById(pack_id) {
	var index = getIndexById(pack_id, scanPackList);
	console.log("index====>" + index);
	if(index == -1) {
		mui.alert("没有找到可以删除的捆包", "提示", "确定", function() {}, "div");
		return false;
	} else {
		scanPackList.splice(index, 1);
		console.log("已经调用删除方法");
		console.log(scanPackList.length);
	}
}

function editPackById(pack_id) {
	var index = getIndexById(pack_id, scanPackList);
	mui.fire(putinScanPage, 'edit', {
		scanPackList: scanPackList,
		edit_pack: scanPackList[index],
		vehicle_id: vehicle_id,
		type: "1"
	});
	putinScanPage.show();
	packListPage.close();
}