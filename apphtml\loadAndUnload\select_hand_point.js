/**
 * 变量定义
 */
var hand_point_id = "";
var hand_point_name = "";
var open_from_url = ""; //通过哪个页面打开该页面
var open_from_id = ""; //通过哪个页面打开该页面
var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");

var factory_area_list;
var factory_area_id = "";
var factory_area_name = "";
var openType = "";

mui.init({
	swipeBack: false //启用右滑关闭功能
});
window.onload = function onload() {
	mui.plusReady(function() {
		open_from_url = plus.webview.currentWebview().open_from_url;
		open_from_id = plus.webview.currentWebview().open_from_id;
		openType = plus.webview.currentWebview().openType;
		var ls_factory_area_id = localStorage.getItem("factory_area_id");
		var ls_factory_area_name = localStorage.getItem("factory_area_name");
		console.info("ls_factory_area_id::" + ls_factory_area_id + "ss:" + ls_factory_area_name);
		if(ls_factory_area_id != null) {
			factory_area_id = ls_factory_area_id;
			$('#factory_area_name').val(ls_factory_area_name);
			queryHandPointName()
		} else {
			queryFactoryAreaData();
		}

	});

}
$(function() {

	//调用webservices接口
	//queryHandPointName();

	//var areaPicker = new mui.PopPicker();//加载区域下拉框

});
document.querySelector('#factory_area_name').addEventListener("tap", function() {
	queryFactoryAreaData();
	//延迟加载
	setTimeout(function() {
		var roadPick = new mui.PopPicker(); //获取弹出列表组建，假如是二联则在括号里面加入{layer:2}
		console.info("bfactory_area_list:" + JSON.stringify(factory_area_list));
		roadPick.setData(factory_area_list);
		roadPick.show(function(item) { //弹出列表并在里面写业务代码
			var itemCallback = roadPick.getSelectedItems();
			factory_area_name = itemCallback[0].text;
			$('#factory_area_name').val(factory_area_name);
			factory_area_id = itemCallback[0].value;
			if(factory_area_id != "" || factory_area_id != null) {
				queryHandPointName();
			}
		});
	}.bind(this), 100);
});
//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	hand_point_id = el_J.find("#hand_point_id").text();
	hand_point_name = el_J.find("#hand_point_name").text();

});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	/*console.info(localStorage.getItem("factory_area_name"));
	console.info(localStorage.getItem("factory_area_id"));
	console.info(localStorage.getItem("hand_point_id"));
	console.info(localStorage.getItem("hand_point_name"));*/
	if(localStorage.getItem("hand_point_id") == null || localStorage.getItem("hand_point_id") == "") {
		//判断是否选择仓库
		if(null == hand_point_id || "" == hand_point_id) {
			mui.alert("请选择装卸点", " ", "确定", function() {}, 'div');
			return false;
		}
		localStorage.setItem("factory_area_id", factory_area_id);
		console.info("ccccccc::" + factory_area_name)
		localStorage.setItem("factory_area_name", factory_area_name);
		localStorage.setItem("hand_point_id", hand_point_id);
		localStorage.setItem("hand_point_name", hand_point_name);
	} else {
		if(localStorage.getItem("factory_area_id") == factory_area_id) {
			if(null != hand_point_id || "" != hand_point_id) {
				console.info("cccccccddddd::" + factory_area_name)
				localStorage.setItem("factory_area_id", factory_area_id);
				localStorage.setItem("factory_area_name", factory_area_name);
				localStorage.setItem("hand_point_id", hand_point_id);
				localStorage.setItem("hand_point_name", hand_point_name);
			}
		} else {
			console.info("aaaaaaaaaaaaa::" + hand_point_id)
			if(null == hand_point_id || "" == hand_point_id) {
				mui.alert("更换厂区后请选择装卸点", " ", "确定", function() {}, 'div');
				return false;
			}
			localStorage.setItem("factory_area_id", factory_area_id);
			localStorage.setItem("factory_area_name", factory_area_name);
			localStorage.setItem("hand_point_id", hand_point_id);
			localStorage.setItem("hand_point_name", hand_point_name);
			//						console.info("factory_area_name_afactory_area_name_a::"+factory_area_name_a);
			//						mui.alert("目前厂区 ["+factory_area_name_a+"] 与所选厂区 ["+factory_area_name+"] 不一致"," ","确定",function(){},'div');
			//						return false;
		}
	}
	//高强钢，厂区和仓库对应
	if(seg_no == "00138") {
		factoryWprovice();
	}
	localStorage.getItem("name");
	localStorage.getItem("segNo");
	var self = plus.webview.currentWebview();
	var popHandPoint = self.openType;
	if("popHandPoint" == popHandPoint) {
		mui.openWindow({
			url: '../public/index.html',
			id: 'index',
			createNew: true
		});
	} else if("handPoint" == popHandPoint) {
		mui.openWindow({
			url: 'load_unload_menus.html',
			id: 'load_unload_menus',
			createNew: true
		});
	} else if("vehicle_load_manage" == popHandPoint) {
		mui.openWindow({
			url: 'vehicle_load_manage.html',
			id: 'vehicle_load_manage',
			createNew: true
		});
	} else if("hand_point_end" == popHandPoint) {
		mui.openWindow({
			url: 'hand_point_end.html',
			id: 'hand_point_end',
			extras: {
				next_hand_point: hand_point_name,
				next_target: '下个装卸点',
			},
			createNew: true
		});
	} else {
		mui.openWindow({
			url: 'load_unload_menus.html',
			id: 'load_unload_menus',
			createNew: true
		});
	}
});
//查询装卸点名称
function queryHandPointName() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var car_trace_no = localStorage.getItem("car_trace_no");
	console.info("跳转了。。。。。" + car_trace_no);
	console.info("跳转了。。。。。" + openType);
	var params = '{"seg_no":"' + seg_no + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no + '","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			console.log(data.resultList.length);
			if(data.resultList.length == 0) {
				/*	 mui.alert("未查询到对应的装卸点信息", "提示", "确定", function() {
					 	$("#companyList").html("");
					}, 'div');
					return ;*/
				$("#companyList").html("");
			} else {
				var chtml = "";
				$.each(data.resultList, function(i, item) {
					//佛山宝钢装卸点信息不显示装卸点代码
					if('00126' == seg_no) {
						chtml = chtml + '<li class="mui-table-view-cell">' +
							'<a class="mui-navigate-right">' +
							'<div>' +
							'<label id="hand_point_id" style="display:none;color: blue;">' + item.hand_point_id + '</label>' +
							'</div>' +
							'<div>' +
							'<label id="hand_point_name" >' + item.hand_point_name + '</label>' //style="font-size: 24px;color: blue;"
							+
							'</div>' +
							'</a>' +
							'</li>';
					} else {
						//html+="<li class='mui-table-view-cell' id='show_hand_point_name' p_name='"+item.hand_point_name+"' p_id='"+item.hand_point_id+"'>"+item.hand_point_name+"</li>";
						chtml = chtml + '<li class="mui-table-view-cell">' +
							'<a class="mui-navigate-right">' +
							'<div>' +
							'<label id="hand_point_id">' + item.hand_point_id + '</label>' +
							'</div>' +
							'<div>' +
							'<label id="hand_point_name">' + item.hand_point_name + '</label>' +
							'</div>' +
							'</a>' +
							'</li>';
					}
				});
				$("#companyList").html(chtml);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//查询厂区
function queryFactoryAreaData() {
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	//var factory_area_name = $("#factory_area_name").val();
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","code_type":"FACTORY_AREA"}';
	var method = "exeQueryFactoryArea";
	console.info("params:" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			if(data.resultStatus == "1") {
				factory_area_list = data.codeList;
				if(factory_area_name == null || factory_area_name == "") {
					factory_area_name = data.codeList[0].text;
					console.info("aaaaaaaaaaaaa" + factory_area_name);
					$('#factory_area_name').val(data.codeList[0].text);
					factory_area_id = data.codeList[0].value;
					queryHandPointName();
					console.info("factory_area_list:" + JSON.stringify(factory_area_list));
				}
			} else {
				mui.alert("没有配置厂区数据，请联系管理员", "提示", "确定", function() {}, 'div');
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});

}

function factoryWprovice() {

	var factory_area_id = localStorage.getItem("factory_area_id");
	//change by yangzemin 20201020
	if(factory_area_id == "F1") {
		// localStorage.setItem("factory_wprovider", "1GQ01");
		localStorage.setItem("factory_wprovider", "037413005");
	} else if(factory_area_id == "F2") {
		// localStorage.setItem("factory_wprovider", "1GQ02");
		localStorage.setItem("factory_wprovider", "037413006");
	} else if(factory_area_id == "F3") {
		// localStorage.setItem("factory_wprovider", "1GQ03");
		localStorage.setItem("factory_wprovider", "037413004");
	}
	console.log(localStorage.getItem("factory_wprovider"));
	//该页面确定按钮被点击之后放入临时变量
	localStorage.setItem("depotHasChanged", "depotHasChanged");
}