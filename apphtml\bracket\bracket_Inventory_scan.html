<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>铁托架盘库扫描</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="bracket_Inventory_scan.css" />
	</head>

	<body>
		<div id="overlay" ></div>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">铁托架盘库扫描</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;">
				<!-- 具体内容 -->
				<!-- 铁托架号 -->
				<div class="mui-input-row detail_row">
					<div class="text">铁托架号&nbsp;&nbsp;&nbsp;</div>
					<div> <input id="steel_support_id" type="text" class="mui-input-clear"> </div>
				</div>
				
				<!-- 铁托架编号 -->
				<div class="mui-input-row detail_row">
					<div class="text">铁托架编号</div>
					<div> <input id="steel_support_num" type="text" class="mui-input-clear"> </div>
				</div>
			
				<!-- 托架名称 -->
				<div class="mui-input-row detail_row">
					<div class="text">托架名称&nbsp;&nbsp;&nbsp;</div>
					<div> <input id="steel_support_name" type="text" class="mui-input-clear"> </div>
				</div>
				
				<!-- 托架类型 -->
				<div class="mui-input-row detail_row">
					<div class="text">托架类型&nbsp;&nbsp;&nbsp;</div>
					<select id="steel_support_type" onchange="steel_support_type_change();">
						<option value="0">--请选择--</option>
						<option value="10">固定铁托架</option>
						<option value="20">组合铁托架</option>
						<option value="30">木托架</option>
					</select>
				</div>
				
				<!-- 合计框 -->
				<div class="mui-input-row sum">
					<div>
						<div>
							<div class="text" style="color: blue;">已下载</div>
							<div><label id="download">0</label></div>
						</div>
						<div>
							<div class="text" style="color: red;">待上传</div>
							<div><label id="un_download">0</label></div>
						</div>
					</div>
				</div>
				
				<!-- 按钮 -->
				<div class="mui-input-row">
					<button id="comfir_button" type="button" class="mui-btn mui-btn-primary" style="width: 68%;font-size: 20px; line-height: 1.8; margin-left: 3px;">上&nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp; 传</button>
					<button id="update_button" type="button" class="mui-btn mui-btn-primary" style="width: 30%;font-size: 20px; line-height: 1.8; ">修&nbsp;&nbsp;改</button>
				</div>
			</div>
			
		</div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/util/public.js"></script>
		<script src="bracket_Inventory_scan.js"></script>
	</body>
</html>