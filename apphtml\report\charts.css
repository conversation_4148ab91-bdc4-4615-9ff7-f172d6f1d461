.mui-content {
	height: 100%;
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	width: 100%;
	text-align: center;
}

* {
	padding: 0;
	margin: 0;
}

#ability {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 300px;
	z-index: 5;
}

#content {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
}

.simlefont {
	font-size: x-small;
	color: #000;
}

.bigfont {
	font-size: x-large;
	color: #000;
}

.lableleft {
	float: left;
	font-size: 22px;
	padding: 5px 5px;
	text-align: center;
	color: #007AFF;
}

.detail_row {
	height: 46px;
}

.detail_row div {
	float: left;
}

.detail_row .text-content {
	width: 70%;
}

.text {
	float: left;
	width: 30%;
	font-size: 18px;
	padding: 10px 0px;
	text-align: center;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

.mean {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	-webkit-transform: translateY(-50%);
	left: -5px;
	;
	width: 40px;
	height: 40px;
	z-index: 9;
	background-image: url(../images/menu3.png);
}

#query_button,
#pingzhong,
#kuling {
	line-height: 1.8;
	font-size: 22px;
}