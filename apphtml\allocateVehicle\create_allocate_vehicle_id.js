//定义全局变量
var webServiceUrl;
var segNo;
var status;
var user_id;
var isFocus = "-1";
var deliver_type = "0"; //交货方式
var peidan_type = "10"; //配单维度默认为按单据号
var customer_id = "";
var customer_name = "";

var driverInfo = new HashMap(); //驾驶员信息  Map<driver,driverNameInfo>
var vehicleInfo = new HashMap(); //车牌、承运商代码、名称信息  Map<vehicle_id,vehicl>

var car_num; //车牌数字
var vehicle_id; //车牌号
var vehicleSelectedFlag = "0"; //车牌号选择标记  0：未选中；1：已选中；
$(function() {
	mui.init({
		swipeBack: true, //启用右滑关闭功能
	});
});
(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);

/**
 * 初始化页面
 */
$(document).ready(function() {
	webServiceUrl = localStorage.getItem("webServiceUrl");
	segNo = localStorage.getItem("segNo");
	user_id = localStorage.getItem("account");
	getHandType(); //查询页面装卸类型下拉框数据
	//getSelectionParams(); //查询长云商代码、名称
	vehicle_arrive_time(); //车辆预计到达时间定义
	//getSelectionVehicleParams(); //车牌号下拉框加载
	//getSelectionDriverNameParams(); //驾驶员姓名下拉框
});

var driverInfo = new HashMap(); //驾驶员信息  Map<driver,driverNameInfo>
var vehicleInfo = new HashMap(); //车牌、承运商代码、名称信息  Map<vehicle_id,vehicl>

var car_num; //车牌数字
var vehicle_id; //车牌号
var vehicleSelectedFlag = "0"; //车牌号选择标记  0：未选中；1：已选中；
$(function() {
	mui.init({
		swipeBack: true, //启用右滑关闭功能
	});
});
(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);
/**
 * 查询装卸类型 
 */
function getHandType() {
	var params = "{seg_no:'" + segNo + "',code_type:'HAND_TYPE'}";
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var method = "exeQueryHandTypeForFSBG";
	params = encodeURIComponent(params);
	var handTypeHtml = "";
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			if (data != null) {
				$.each(data.list, function(i, item) {
					handTypeHtml = handTypeHtml + "<option value='" + item.VALUE + "'>" + item
						.LABEL + "</option>";
				});
				$("#hand_type").html(handTypeHtml);
			} else {
				mui.toast("网络超时，请联系管理员！")
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}
/*
 * 车辆到达时间
 */
function vehicle_arrive_time() {
	var myDate = new Date();
	var dtpicker = new mui.DtPicker({
		type: "datetime", //设置日历初始视图模式 
		beginDate: new Date(myDate.getFullYear(), myDate.getMonth(), myDate.getDate()), //设置开始日期 
		endDate: new Date(myDate.getFullYear(), myDate.getMonth() + 1, myDate.getDate()), //设置结束日期 
		labels: ['年', '月', '日', '时', '分', '秒'], //设置默认标签区域提示语 
		customData: {
			/* h: [
			     { value: 'AM', text: 'AM' },
			     { value: 'PM', text: 'PM' }
			 ] */
		} //时间/日期别名 
	});
	mui(document.body).on('tap', '#car_arrive_time', function() { //绑定事件
		dtpicker.show(function(items) {
			$("#car_arrive_time").val(items.text); //dtpicker.dispose();
			car_arrive_time = items.text;
		});
	});
}

/**
 * 初始化页面
 */
// $(document).ready(function() {
// 	webServiceUrl = localStorage.getItem("webServiceUrl");
// 	segNo = localStorage.getItem("segNo");
// 	user_id = localStorage.getItem("account");
// 	getHandType(); //查询页面装卸类型下拉框数据
// 	//getSelectionParams(); //查询长云商代码、名称
// 	vehicle_arrive_time(); //车辆预计到达时间定义
// 	//getSelectionVehicleParams(); //车牌号下拉框加载
// 	//getSelectionDriverNameParams(); //驾驶员姓名下拉框
// });
/*
 * 根据交货方式判断身份证号、手机号是否为只读
 */
// function deliver_type_change() {
// 	deliver_type = $("#deliver_type option:selected").val();
// 	//console.log(deliver_type);
// 	//自提
// 	if (deliver_type == "10") {
// 		//去除只读
// 		$("#phone_number").removeAttr("readOnly", "false");
// 		$("#id_card").removeAttr("readOnly", "false");
// 		//去除灰色背景
// 		$("#phone_number").css("background-color", "#FAFAFA");
// 		$("#id_card").css("background-color", "#FAFAFA");
// 	} else {
// 		//添加只读 
// 		$("#phone_number").attr("readOnly", "true");
// 		$("#id_card").attr("readOnly", "true");
// 		//增加灰色背景
// 		$("#phone_number").css("background-color", "#CCCCCC");
// 		$("#id_card").css("background-color", "#CCCCCC");
// 	}
// }

/*
 * 承运商名称以及代码
 */
function getSelectionParams() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = JSON.stringify({
		seg_no: segNo
	});
	var method = "exeQueryProviderInfo";
	var selectHtml = "<option value='0' selected='true'>--请选择--</option>";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			//console.log(outUri + "&" + innerUri + "---" + params);
			$.each(data.returnList, function(i, item) {
				selectHtml = selectHtml + "<option value='" + item.PROVIDER_ID + "'>" + item
					.PROVIDER_NAME + "</option>";
			});
		}
		$("#customer_name").html(selectHtml);
	});
}

/*
 * 根据交货方式判断身份证号、手机号是否为只读
 */
function deliver_type_change() {
	deliver_type = $("#deliver_type option:selected").val();
	//console.log(deliver_type);
	//自提
	if (deliver_type == "10") {
		//去除只读
		$("#phone_number").removeAttr("readOnly", "false");
		$("#id_card").removeAttr("readOnly", "false");
		//去除灰色背景
		$("#phone_number").css("background-color", "#FAFAFA");
		$("#id_card").css("background-color", "#FAFAFA");
	} else {
		//添加只读 
		$("#phone_number").attr("readOnly", "true");
		$("#id_card").attr("readOnly", "true");
		//增加灰色背景
		$("#phone_number").css("background-color", "#CCCCCC");
		$("#id_card").css("background-color", "#CCCCCC");
	}
}
/*
 * 获取承运商名称、代码
 */
function customer_name_change() {
	var customer = $("#customer_name option:selected");
	customer_id = customer.val();
	customer_name = customer.text();
	$("#customer_id").val(customer_id);
	//console.log("代码" + customer_id + "名称" + customer_name);
}

/*
 *新增子项按钮
 */
mui(document.body).on('click', '#btn_new_sub', function() {
	var hand_type = $("#hand_type option:selected").val(); //装卸类型
	var car_arrive_time = $("#car_arrive_time").val(); //车辆到达时间
	var id_card = $("#id_card").val(); //身份证号
	var phone_number = $("#phone_number").val(); //手机号
	var driver_name = $("#driver_name").val();

	var car_num_reg = /^[京津冀黑辽吉蒙新藏青甘陕宁晋鲁豫川云渝贵湘鄂皖苏沪浙赣闽粤桂琼]{1}[A-Za-z]{1}[A-Z0-9a-z]{4}[A-Za-z0-9挂]{1}$/;
	var id_card_reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
	/*
	 * 判断配车单输入控制
	 */
	if ("" == hand_type) {
		mui.alert("请选择装卸类型");
		return false;
	} else if ("" == vehicle_id || "0" == vehicle_id) {
		mui.alert("请选择车牌号");
		return false;
	} else if ("" == car_arrive_time) {
		mui.alert("请选择车辆到达时间");
		return false;
	} else if ("" == deliver_type || "0" == deliver_type) {
		mui.alert("请选择交货方式");
		return false;
	} else if ("" == id_card) {
		mui.alert("请输入身份证号");
		return false;
	} else if ("" == phone_number) {
		mui.alert("请输入手机号");
		return false;
	} else {
		console.log(deliver_type);
	}

	if (!car_num_reg.test(vehicle_id)) { // 判断车牌是否匹配正则表达式
		mui.toast("车牌号码格式不正确！");
		flag = false;
		return;
	}
	if (!id_card_reg.test(id_card)) { // 判断车牌是否匹配正则表达式
		mui.toast("身份证格式不正确！");
		flag = false;
		return;
	}
	
	// //按捆包号
	if (peidan_type == "20") {
		//跳转到配车单子项页面
		mui.openWindow({
			url: 'create_allocate_vehicle_sub_kb.html',
			id: 'create_allocate_vehicle_sub_kb',
			createNew: true,
			extras: {
				hand_type: hand_type,
				vehicle_id: vehicle_id,
				customer_id: customer_id,
				customer_name: customer_name,
				car_arrive_time: car_arrive_time,
				deliver_type: deliver_type,
				driver_name: driver_name,
				id_card: id_card,
				phone_number: phone_number
			}
		});
		return;
	}
	
	mui.openWindow({
		url: 'create_allocate_vehicle_sub.html',
		id: 'create_allocate_vehicle_sub',
		createNew: true,
		extras: {
			hand_type: hand_type,
			vehicle_id: vehicle_id,
			customer_id: customer_id,
			customer_name: customer_name,
			car_arrive_time: car_arrive_time,
			deliver_type: deliver_type,
			driver_name: driver_name,
			id_card: id_card,
			phone_number: phone_number
		}
	});
});

//配单维度选择框
function peidan_type_change() {
	peidan_type = $("#peidan_type option:selected").val();
	// const hand_type = $("#hand_type option:selected").val(); //装卸类型
	// //按捆包号
	// if (peidan_type == "20") {
	// 	//跳转到配车单子项页面
	// 	mui.openWindow({
	// 		url: 'create_allocate_vehicle_sub_kb.html',
	// 		id: 'create_allocate_vehicle_sub_kb',
	// 		createNew: true,
	// 		extras: {
	// 			hand_type: hand_type,
	// 			vehicle_id: vehicle_id,
	// 			customer_id: customer_id,
	// 			customer_name: customer_name,
	// 			car_arrive_time: car_arrive_time,
	// 			deliver_type: deliver_type,
	// 			driver_name: driver_name,
	// 			id_card: id_card,
	// 			phone_number: phone_number
	// 		}
	// 	});
	// 	$("#peidan_type option:selected").val('10');
	// }

}

//车牌号弹窗
mui(document.body).on('tap', '#vehicle_id', function() {
	console.log("车牌号点击....");
	document.activeElement.blur();
	$("#InnerVehicleDiv").toggleClass('show');
	//$("#pop_car").toggleClass('show');
});

//车牌号弹窗 取消按钮点击事件
mui(document.body).on('tap', '#innerVehicleCancel', function() {
	$("#InnerVehicleDiv").toggleClass('show');
	$("#car_num").focus();

});

//车牌号弹窗 确认按钮点击事件
mui(document.body).on('tap', '#innerVehicleConfirm', function() {
	if (vehicleSelectedFlag == "0") {
		mui.alert("请选择车牌号", "提示", "确认", function() {}, "div");
		return false;
	}
	$("#vehicle_id").val(vehicle_id);
	$("#customer_name").val(customer_id);
	$("#customer_id").val(customer_id);
	$("#InnerVehicleDiv").toggleClass('show');
});

/**
 * 车牌省份简称按钮选择事件  
 * @param {Object} ths
 */
function chooseProvince(ths) {
	$(ths).addClass('active').siblings().removeClass('active');
	var province = $(ths).text();
	$("#province_name").html(province);
	getSelectionVehicleParams();
	if (fadeFlag == 1) {
		$("#province").fadeOut();
		fadeFlag = 0;
	} else {
		$("#province").fadeIn();
		fadeFlag = 1;
	}
}

/**
 * 车牌省份简称信息淡入淡出事件
 * 
 */
var fadeFlag = 0; //标记
$("#province_name").click(function() {
	if (fadeFlag == 1) {
		$("#province").fadeOut();
		fadeFlag = 0;
	} else {
		$("#province").fadeIn();
		fadeFlag = 1;
	}
});

/**
 * DOM页面触摸事件
 */
$(document).bind('touchstart', function(e) {
	var x = e.originalEvent.targetTouches[0].pageX; //获取触碰点的X坐标
	var y = e.originalEvent.targetTouches[0].pageY; //获取触碰点的Y坐标
	var div = document.getElementById("province_name");
	var height = $(window).height(); //获取车牌省份简称DIV的高度
	//var width = $(window).width(); //获取车牌省份简称的DIV的宽度
	if (((height / 8) * 2) > y && fadeFlag == "1") { //如果触碰的位置不在省份信息div元素，则将div淡出隐藏
		$("#province").fadeOut();
		fadeFlag = 0;
	}
});

//车牌号下拉框
function getSelectionVehicleParams() {
	var car_num = $.trim($("#car_num").val());
	var province = $.trim($("#province_name").text()); // 获取车牌信息的省份简称
	var vehicle_no = province + car_num.toUpperCase(); //车牌号
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = JSON.stringify({
		seg_no: segNo,
		vehicle_id: vehicle_no
	});
	params = encodeURIComponent(params);
	var method = "exeSelectionVehicleParams";
	//var selectHtml = "<option value='0' selected='true'>--请选择--</option>";
	var selectHtml = "";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			console.log(JSON.stringify(data.returnList));
			$.each(data.returnList, function(i, item) {
				vehicleInfo.put(item.vehicle_id, item);
				selectHtml = selectHtml + '<li class= "mui-table-view-cell ">' +
					'<a class="mui-navigate-right">' +
					'<div style="float: left; text-align: left" id = "vehicle_flag" >' + item
					.vehicle_id + '</div>' +
					'</a>' +
					'</li>';
			});
		}
		$("#vehicle_list").html(selectHtml);
	});
}

/**
 * 失去焦点 
 */
$("#car_num").blur(function() {
	isFocus = "0";
});
/**
 * 获得焦点 
 */
$("#car_num").focus(function() {
	isFocus = "1";
});

//绑定列表选中事件
mui(document.body).on('selected', '#InnerVehicleInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	vehicle_id = el_J.find("#vehicle_flag").text();
	var v = vehicleInfo.get(vehicle_id); //获取map中对应车牌号信息
	customer_name = v.tprovider_name; //获取承运商名称
	customer_id = v.tprovider_id; //获取承运商代码
	vehicleSelectedFlag = "1";
	//console.log("车牌" + vehicle_id + "承运商名称" + customer_name + "承运商代码" + customer_id);
});

//=====================TODO=====================================
//点击图标搜索驾驶员信息
mui(document.body).on("tap", ".icon-search", function() {
	if ("" == deliver_type || "0" == deliver_type) {
		mui.alert("请选择交货方式", "提示", "确认", function() {}, "div");
		return false;
	}
	getSelectionDriverNameParams();
});

/*
 * 驾驶员信息
 */
function getSelectionDriverNameParams() {
	driver_name = $("#driver_name").val();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = JSON.stringify({
		seg_no: segNo,
		driver_name: driver_name,
		deliver_type: deliver_type
	});
	params = encodeURIComponent(params);
	var method = "exeSelectionDriverNameParams";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			console.log(JSON.stringify(data.returnList));
			if ("1" == data.returnStatus) {
				id_card = data.returnList[0].id_card;
				phone_number = data.returnList[0].mobile_phone;
				$("#id_card").val(id_card);
				$("#phone_number").val(phone_number);
			} else {
				$("#id_card").val("");
				$("#phone_number").val("");
				mui.toast("没有该驾驶员信息，请检查后重新输入！");
			}
		}
	});
}
