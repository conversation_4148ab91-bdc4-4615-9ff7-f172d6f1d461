		var segNo=localStorage.getItem("segNo");
		var webServiceUrl=localStorage.getItem("webServiceUrl");
		
		//单据号获得焦点
		$("#putout_voucher_num")[0].focus();
		
		 //查询按钮事件绑定			
	 $("#query_button").click(function(){
	     putoutVoucherDownLoad();
	   });
			
	/**
	 * 出库查询
	 * 
	 */
	function putoutVoucherDownLoad(){
		var putout_voucher_num=$("#putout_voucher_num").val();
		var outUri = domainName+"webService.jsp?callback=?";
		var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAQueryService';
		var params = '{"seg_no":"'+segNo+'","putout_voucher_num":"'+putout_voucher_num+'"}';
		console.log(params);
		var method = "queryPutOutPackInfo";
		$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){//如返回对象有一个username属性
			console.log(data);
			if(null != data){
				if(data.resultStatus=="1"){
				        var chtml="";
						$.each(data.packList,function(i, item) {
							chtml=chtml+'<li class="mui-table-view-cell" id="format">'+
							                '<div class="row"><span id="voucher_num" >'+putout_voucher_num+'</span>'+
											'<div class="row"><span class="kunbaohao">'+item.pack_id+'</span>'+'&nbsp;'+
											'<span class="ziyuanhao">'+item.product_id+'</span>'+'</div>'+
										'</li>';
						});
						$("#table").html(chtml);
				}else{
					mui.alert(data.resultList,"提示","确认",function() {}, "div");
					console.log(data.resultList);
				}
			}else{//连接失败
				mui.alert("连接服务器异常","提示","确认",function() {}, "div");
			}
		});
	}
