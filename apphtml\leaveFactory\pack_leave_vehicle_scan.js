var factory_area_id = localStorage.getItem("factory_area_id"); // 库位
var factory_area_name = localStorage.getItem("factory_area_name"); // 库位
var vehicle_id = localStorage.getItem("vehicle_id"); // 车牌号
//	var factory_area_name = "A库原料";
var hand_point_id = localStorage.getItem("hand_point_id"); //装卸点
var hand_point_name = localStorage.getItem("hand_point_name");
//	var hand_point_name = "1号门";
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");

var packInfoList = new Array(); //捆包信息
var vehicle_no = "";
var driver_name = ""; //司机姓名
var id_card = ""; //司机身份证号
var fact_pack_count = ""; //实物捆包个数
var own_pack_count = ""; //自带捆包个数
var pack_count = ""; //捆包总个数
var car_trace_no = "";
var vehicleInfo = new HashMap(); //信息  Map<vehicle_id,packInfoList>

var vehicleInfo = new HashMap(); //信息  Map<vehicle_id,packInfoList>
var reconfigPackInfoList = new Array();
//页面加载事件
window.onload = function onload() {
	mui.plusReady(function() {
		if(segNo == "00126" || segNo == "00181") {
			$("#zidai").css("display", "none");
			$("#bottom_div").css("display", "none");
			$("#driver").css("display", "none");
			//$("#shiwu").css("display","none");
			$("#papers").css("display", "none");
		} else {
			$("#provider").css("display", "none");
		}
		$("#vehicle_id")[0].focus();
		//dropDownVehicleID();
	});
	$("#factory_area_name").html("目前库区：" + factory_area_name);
	$("#hand_point_name").html("装卸点：" + hand_point_name);
	//$("#vehicle_id").val(vehicle_id);
	$("#driver_name").val(driver_name);
	$("#id_card").val(id_card);
	$("#fact_pack_count").val(fact_pack_count);
	$("#own_pack_count").val(own_pack_count);
	/*if(vehicle_id!=null){
		queryOwnGoodInfo();
	}*/
}

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

$(function() {
	//输入框获得焦点
	$("#vehicle_id")[0].focus();
});

//返回
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	//localStorage.setItem("wprovider_id", wprovider_id);
	//localStorage.setItem("wprovider_name", wprovider_name);
	mui.openWindow({
		id: "index",
		url: "../public/index.html",
		createNew: true
	});
}
//下拉框变更触发事件
function vehicle_id_change(vehicle_id_value) {
	vehicle_no = vehicle_id_value;
	car_trace_no = vehicleInfo.get(vehicle_no).car_trace_no;
	if(segNo == "00126" || segNo == "00181") {
		//$("#driver_name").val(vehicleInfo.get(vehicle_id).provider_name);
	} else {
		$("#driver_name").val(vehicleInfo.get(vehicle_id).driver_name);
		$("#id_card").val(vehicleInfo.get(vehicle_id).id_card);
		$("#fact_pack_count").val(vehicleInfo.get(vehicle_id).fact_pack_count);
		$("#own_pack_count").val(vehicleInfo.get(vehicle_id).own_pack_count);
	}
	$("#driver_name").val("");
	$("#id_card").val("");
	$("#fact_pack_count").val("");
	$("#own_pack_count").val("");
	if(segNo == "00126" || segNo == "00181") {
		queryOwnGoodInfofsbg();
	} else {
		queryOwnGoodInfo();
	}
}

// 按钮确定事件
mui(document.body).on('tap', '#confirm', function() {
	var options_value = $("#vehicle_id").val();
	if(options_value == 0) {
		alert("请选择车牌号");
		return false;
	}

	// 跳转页面
	mui.openWindow({
		url: 'pack_leave_scan.html',
		id: 'pack_leave_scan',
		createNew: true,
		extras: {
			packInfoList: reconfigPackInfoList,
			pack_count: pack_count,
			vehicle_id: vehicle_no,
			car_trace_no: car_trace_no
		}
	});
});

//取消按钮绑定事件
mui(document.body).on('tap', '#cancel', function() {
	$("#vehicle_id").val("");
	$("#driver_name").val("");
	$("#id_card").val("");
	$("#fact_pack_count").val("");
	$("#own_pack_count").val("");
	packInfoList.length = 0; //置空数组
	$("#vehicle_id")[0].focus();
});

//库位 : 增加keypress监听
$("#vehicle_id").keypress(function(e) {
	if(e.keyCode == 13) {
		vehicle_id = $("#vehicle_id").val();
		if(vehicle_id == null || vehicle_id == "") {
			alert("车牌号不能为空");
		}
		queryOwnGoodInfo();
		//$("#vehicle_id").val("");
		//捆包输入框获得焦点
		$("#vehicle_id")[0].focus();
	}
});

// 查询车辆及自带货信息
function queryOwnGoodInfo() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //pdaboard
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","factory_area_id":"' + factory_area_id + '","vehicle_id":"' + vehicle_no + '","car_trace_no":"' + car_trace_no + '"}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "exeQueryOwnGoodInfo";
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(null != data) {
				console.log(data);
				packInfoList = data.returnList;
				$.each(data.returnList, function(i, item) {
					if(item != null) {
						console.log(">>>>>>>>>>>>packInfoList:" + JSON.stringify(data));
						$("#driver_name").val(packInfoList[0].driver_name);
						$("#id_card").val(packInfoList[0].id_card);
						$("#fact_pack_count").val(packInfoList[0].fact_pack_count);
						$("#own_pack_count").val(packInfoList[0].own_pack_count);
						car_trace_no = packInfoList[0].car_trace_no;
						pack_count = packInfoList[0].fact_pack_count + packInfoList[0].own_pack_count;
						vehicle_id = packInfoList[0].vehicle_id;
						reconfigurationPackInfo(packInfoList);
					}
				});
			} else { //连接失败
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this; //调用本次ajax请求时传递的options参数
		}
	});
}

// 查询车辆及自带货信息-佛山宝钢
function queryOwnGoodInfofsbg() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //pdaboard
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_no + '","car_trace_no":"' + car_trace_no + '"}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "exeQueryOwnGoodInfofsbg";
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(null != data) {
				console.log(data);
				packInfoList = data.returnList;
				$.each(data.returnList, function(i, item) {
					if(item != null) {
						console.log(">>>>>>>>>>>>packInfoList:" + JSON.stringify(data));
						//$("#driver_name").val(packInfoList[0].driver_name);
						//$("#id_card").val(packInfoList[0].id_card);
						$("#provider_name").val(packInfoList[0].provider_name);
						$("#fact_pack_count").val(packInfoList[0].fact_pack_count);
						//$("#own_pack_count").val(packInfoList[0].own_pack_count);
						//car_trace_no = packInfoList[0].car_trace_no;
						pack_count = packInfoList[0].fact_pack_count;
						vehicle_id = packInfoList[0].vehicle_id;
						reconfigurationPackInfofsbg(packInfoList);
					}
				});
			} else { //连接失败
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this; //调用本次ajax请求时传递的options参数
		}
	});
}

//佛山宝钢
function reconfigurationPackInfofsbg(packInfoList) {
	//console.log("11111111111111111111" + JSON.stringify(packInfoList));
	//离厂捆包信息
	$.each(packInfoList, function(i, item) {
		$.each(item.leavePackList, function(j, jtem) {
			//console.log("2222222222222222222222222222"+jtem);
			reconfigPackInfoList.push(jtem);
		});
	});
}

function reconfigurationPackInfo(packInfoList) {
	console.log("11111111111111111111" + JSON.stringify(packInfoList));
	//离厂捆包信息
	$.each(packInfoList, function(i, item) {
		$.each(item.leavePackList, function(j, jtem) {
			console.log("2222222222222222222222222222" + jtem);
			reconfigPackInfoList.push(jtem);
		});
	});
	//自带货捆包信息
	$.each(packInfoList, function(a, atem) {
		$.each(atem.ownGoodPackList, function(s, stem) {
			console.log("2222222222222222222222222222" + JSON.stringify(stem));
			reconfigPackInfoList.push(stem);
		});
	});
}

window.addEventListener('refresh', function(e) {
	location.reload();
	$("#vehicle_id")[0].focus();
});

// 车牌下拉框加载
function dropDownVehicleID() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"' + segNo + '","factory_area_id":"' + factory_area_id + '"}';
	var chtml = '<option value=' + '"0"' + 'selected="true" >' + "--请选择车牌号--" + '</option>';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "exeQueryLeaveVehicle";
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(null != data) {
				$.each(data.returnList, function(i, item) {
					vehicleInfo.put(item.vehicle_no, item);
					console.log(">>>>>>>>>>>>packInfoList:" + JSON.stringify(vehicleInfo));
					chtml = chtml + '<option value = "' + item.vehicle_no + '">' + item.vehicle_no + '</option>';
				});
				$("#vehicle_id").html(chtml);
			} else { //连接失败
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this; //调用本次ajax请求时传递的options参数
		}
	});
	
	mui(document.body).on('click', '#vehicle_id', function() {
				dropDownVehicleID();
			});
}

mui(document.body).on('click', '#vehicle_id', function() {
	dropDownVehicleID();
});