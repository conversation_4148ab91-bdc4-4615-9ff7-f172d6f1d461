/**
 * 初始化变量信息 
 */
var scanSteelSupportList= new Array();//选中之后要綁定的列表

$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
});

mui.ready(function() {
	$("#pack_id").focus();
/*	$("#pack_id").val("P141204012");
	$("#steel_support_id").val("TJ150600310");*/
});

//捆包号
$("#pack_id").keypress(function(e) {
	if(e.keyCode == 13) {
		var pack_id = $("#pack_id").val();
		if(pack_id == null || pack_id == "") {
			mui.alert("请扫描或者输入捆包号", "提示", "确定", null, 'div');
			$("#pack_id").focus();
			return;
		}else{
			$("#steel_support_id").focus();
		}
	}
});


//按钮确定事件
mui(document.body).on('tap','#comfir_button',function(){
	
	//查询铁托架 与捆包信息
	queryBindInfo();
	
});

//按钮绑定事件
mui(document.body).on('tap','#bind_button',function(){
	if(scanSteelSupportList.length == 0){
		mui.alert("已扫描列表无记录,请检查", "提示", "确定",function(){},'div');
		return;
	}else{
		mui("#bind_button").button('loading');
		$("#overlay").addClass("overlay"); //遮罩层
		
		/**
		 * 调用绑定接口方法
		 */
		exeSteelSupportBind();
	}
});

//返回按钮事件
mui(document.body).on('tap','#back',function(){
	mui.back();
});     
mui.back = function () {
	if(scanSteelSupportList.length == 0){
		 mui.openWindow({
			url:'bracket_menus.html',
			id:'bracket_menus',
			createNew:false
		  });
		}else{
			var btnArray = ['退出', '取消'];
			mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			   if(e.index==0){
			     setTimeout(function() {
				  var ws=plus.webview.currentWebview();
				  plus.webview.close(ws)
				}, 0);
			 }
		},'div');
	}
};

/*方法*/

//捆包扫描方法
function queryQualityBlockPack(pack_id){
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '"}';
	//console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "queryPackSupportInfo";
	
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 2000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data != null) {
				
				if(data.resultStatus == "1"){
					//光标到铁托架
					$("#steel_support_id").focus();
				}else{
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					$("#pack_id").val("");
					$("#pack_id").focus();
					return;
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if(textStatus != "timeout") {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
}


//查询铁托架与捆包状态
function queryBindInfo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var pack_id = $("#pack_id").val();
	var steel_support_id = $("#steel_support_id").val();
	var params = '{"seg_no":"'+segNo+'","pack_id":"'+pack_id+'","steel_support_id":"'+steel_support_id+'","steel_support_status":"10"}';
	//console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "queryBindInfo";
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:10000,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			console.log(JSON.stringify(data))
			if(data != null) {
				if(data.resultStatus == "2"){
					var list = data.resultList;
					//将当前扫描的记录增加到集合中
					var lihtml = "";
					if(getIndexByPackId(list.pack_id,scanSteelSupportList) == -1){
						//将查询的铁托架信息放置于当前对象中
						var obj = {
									pack_id: list.pack_id,
									steel_support_id: list.steel_support_id,
									steel_support_name: list.steel_support_name,
									steel_support_status:'10'
						};
						scanSteelSupportList.push(obj);
						//加载表格信息
						loadingContentList();
					}else{
						mui.alert("该捆包已在绑定记录中,请检查", "提示", "确定",function(){},'div');
						return;
					}
					
				}else if(data.resultStatus == "0"){ //捆包提示
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					$("#pack_id").val("");
					$("#pack_id").focus();
					return;
				}
				else if(data.resultStatus == "1"){ //铁托架提示
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					$("#steel_support_id").val("");
					$("#steel_support_id").focus();
					return;
				}
			} else {//连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(xhr,type,errorThrown){
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
		  	 if(type != "timeout"){
		  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			 }else{
			 	$("#pack_id").focus();
			 }
		}
	});
}



//执行铁托架绑定
function exeSteelSupportBind() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","steel_support_list":' + JSON.stringify(scanSteelSupportList) + '}';
	var method = "exeBindSteelSupport";
	console.log("exeBindSteelSupport参数："+JSON.stringify(params));

	$.ajax({
		type:"post",
		async:true,
		timeout:10000,
		url:outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data != null) {
				console.log("exeBindSteelSupport返回："+JSON.stringify(data));
				//移除页面遮罩层效果
				mui("#bind_button").button('reset');
				$("#overlay").removeClass("overlay");
				
				if(data.resultStatus == "1") {
					mui.alert("绑定成功!", "提示", "确定", function() {
						scanSteelSupportList = [];
						$("#companyList").html("");
						$("#pack_id").val("");
						$("#steel_support_id").val("");
						$("#pack_id").focus();
					}, 'div');
					return;
				} else {
					var errorinfo = data.resultDesc;
					mui.alert("绑定失败！原因：" + errorinfo, "提示", "确定", function() {}, 'div');
					return;
				}
			} else { //连接失败
				mui("#bind_button").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return;
			}
		},
		error: function(xhr, type, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传"); 
				} else if(type == "timeout") {
					xhr.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				}else{
					mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
				}
				mui("#bind_button").button('reset');
				$("#overlay").removeClass("overlay");
				});
			}
	});
}


function loadingContentList(){
	var lihtml = "";
	$.each(scanSteelSupportList, function(i, item) {
	 lihtml = lihtml +'<li class="mui-table-view-cell">'+
					'<div class="mui-slider-right mui-disabled">'+
						'<a class="mui-btn mui-btn-red mui-icon"  onclick='+'deleteLi(this,"'+item.pack_id+'")'+'>删除</a>'+
					'</div>'+
					'<div class="mui-slider-handle">'+
						'<div>'+
							'<div id="steel_support_id_1" data="'+item.pack_id+'">'+item.pack_id+'</div>'+
								'<div>'+
									'<div id="spec" class="left"><span>号</span><label>'+item.steel_support_id+'</label></div>'+
									'<div id="steel_support_name"><span>名</span><label>'+item.steel_support_name+'</label></div>'+
							'	</div>'+
						'</div>'+
					'</div>'+
				'</li>';
		});
		$("#companyList").append(lihtml);
}

//删除
function deleteLi(ele, pack_id) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '警告', btnArray, function(e) {
		if(e.index == 0) {
			console.log("pack_id>>>>>>>>>>>>>>>>>>>>>>>>>" + pack_id);
			var indexd = getIndexByPackId(pack_id, scanSteelSupportList);
			console.log("删除：" + pack_id + "，index:" + indexd);
			if(indexd > -1) {
				scanSteelSupportList.splice(indexd, 1);
			}
				//删除捆包
			li.parentNode.removeChild(li);
		} else {
			setTimeout(function() {
				$.swipeoutClose(li);
			}, 0);
		}
	});
}

//获取当前铁托架的下标
function getIndexByPackId(pack_id,scanSteelSupportList){
	var index = -1;
	$.each(scanSteelSupportList, function(i,value) {
		if(value.pack_id == pack_id){
			index = i;
			return index;
		}
	});
	return index;
}

mui(document.body).on('tap','#back',function(){
	mui.back();
});
    
mui.back = function () {   
	if(scanSteelSupportList.length > 0){
		var btnArray = ['退出', '取消'];
		mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			if(e.index==0){
				setTimeout(function() {
					var ws=plus.webview.currentWebview();
					plus.webview.close(ws)
				}, 0);
			}
		},'div');
	}else{
		setTimeout(function() {
			var ws=plus.webview.currentWebview();
			plus.webview.close(ws)
		}, 0);
	}
};