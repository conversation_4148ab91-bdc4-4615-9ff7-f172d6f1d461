var checkId = "";
var segNo;
var webServiceUrl;
var userName;
var packCount = 0;
 
 $(function(){
	mui.init({
		swipeBack:true//启用右滑关闭功能
	});
});

$(document).ready(function(){
	initParms();
	//swipeBack(true);
	initData();
	//$(window).resize();
});

function plusReady(){
	
	readerScanedPackTxt();
}
if(window.plus){
	plusReady();
}else{
	document.addEventListener('plusready', plusReady, false);
}

/*$(window).resize(function () {
	console.log("before resize>>>>>>>>>>>>");
	$('#waitDialog').css({
		position: 'absolute',
		left: ($(window).width() -($('#waitDialog').outerWidth())) / 2,
		top: ($(window).height() - $('#waitDialog').outerHeight()) / 2
	});
	$('#waitDialog').show();
	console.log("after resize>>>>>>>>>>>>"); 
});*/
	  
/**
 * 采用localStorage存储数据
 */
function initParms(){
	segNo = localStorage.getItem("segNo");
	webServiceUrl = localStorage.getItem("webServiceUrl");
	userName = localStorage.getItem("account");
}

function initData(){
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDACheckStockService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+userName+'"}';
	var method = "exeCheckStockVoucherDownload";
	$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){
		if(null != data){//连接成功
			if(data.resultStatus == '1'){//下载成功
				$("#checkListUL li").remove();
				var checkListUL = $("#checkListUL");
				for(var i=0;i<data.resultList.length;i++){
					var li=$('<li class="mui-table-view-cell">'
								+'<a class="mui-navigate-right" onclick='+'transf("'+data.resultList[i].check_id+'")>'
									+'<div><label>'+data.resultList[i].check_id+'</label></div>'
									+'<div><label>'+data.resultList[i].wprovider_desc+'</label><label>'+data.resultList[i].check_date+'</label></div>'
								+'</a>'
							+'</li>');
					checkListUL.append(li);
				}
			}else{
				mui.toast("未下载到盘库单");
			}
		}else{//连接失败
			mui.toast("服务器连接异常");
		}
	});
}
function transf(checkId){
		localStorage.setItem("checkType","1");
		localStorage.setItem("checkId",checkId);
		//location.href="pack_load.html";
		mui.openWindow({
			url:'pack_download.html',
			id:'pack_download',
			createNew:true
		});
}
function noBillTransf(){
		localStorage.setItem("checkType","4");
		localStorage.setItem("checkId","");
		//location.href="pack_load.html";
		mui.openWindow({
			url:'pack_download.html',
			id:'pack_download',
			createNew:true
		});
}

/**
 * 已扫描捆包存储到本地；
 */
var localFile = "upload.txt"
var loacalDir = "PDA";

// 读取本地已盘捆包文件
function readerScanedPackTxt() {
		plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) { 
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(entry) {
				entry.getFile(localFile, {
					create: false
				}, function(fentry) {
					fentry.file(function(file) {
						var btnArray = ['上传', '继续'];   
						mui.confirm('存在未上传捆包数据，是否上传后继续本次盘库?', '系统提醒', btnArray, function(e) {
							var waitingDialog = plus.nativeUI.showWaiting("数据处理中...");
							$("#overlay").addClass("overlay");
								if (e.index == 0) {
								 	uploding = true;
								 	var reader = new plus.io.FileReader();
									reader.readAsText(file, 'utf-8');
									reader.onloadend = function(e) { 
									var data = null;
									try {
										data = e.target.result;
										if(data != "[]" || data!= ""){
											if(data.indexOf("[") < 0 && data.indexOf("]") < 0) {
												data = "[" + data.substring(0, data.length - 1) + "]";
											}
											try {
												chkPackUpload(data, fentry); 
												//$("#overlay").removeClass("overlay");
												//$("#waitDialog").hide();
												waitingDialog.close();
												$("#overlay").removeClass("overlay");
											} catch(e) {
												waitingDialog.close();
												file.close();  
												$("#overlay").removeClass("overlay");
												mui.alert("捆包上传失败,请联系开发人员", "系统提示", "确定", function() {}, 'div');
												return; 
											}
											file.close();
										}
									} catch(e) {
										waitingDialog.close();
										file.close();
										$("#overlay").removeClass("overlay");
										mui.alert("读取已盘捆包文件失败,请联系开发人员", "系统提示", "确定", function() {}, 'div');
										return; 
									} 
								  }
							  }else if(e.index == 1){
								 copyScanedPackTxt(fentry);//删除本地捆包文件
								 $("#overlay").removeClass("overlay");
								 waitingDialog.close();
							  }else{
							  	waitingDialog.close();
							  	$("#overlay").removeClass("overlay");
							  	mui.alert("存在未上传的捆包数据,请选择上传或者继续盘库", "系统提示", "确定", function() {}, 'div');
							  	return ;
							  }	
						 
					  });
					}, function(e) {
						return;
					});
				}, function(e) {
					return;
				});
			}, function(e) {
				return;
			});
		}, function(e) {
			return;
		});
}

//删除本地已扫描捆包文件
function deleteScanedPackTxt(){
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(entry) {
				entry.getFile(localFile, {
					create: false
				}, function(fentry) {
					fentry.remove(function(entry) {
						clearUploadSum();
					}, function(e) {
						mui.alert("文件删除失败" + e.message, "系统提示", "确定", function() {}, 'div');
					});
				}, function(e) {
					 return;
				});
		});
	});
}

function clearUploadSum(){
	localStorage.setItem("recentPackId", "");
	localStorage.setItem("scanPackList", "");
	localStorage.setItem("uploadedCount", "");
	localStorage.setItem("uploadingCount", "");
}

//上传调用服务端接口
function chkPackUpload(orignPackList,fentry){
		var orignPackArray = {};
	 	orignPackArray = eval(orignPackList);
	    packCount = orignPackArray.length;
		//alert("总捆包个数>>>>>>>>>>>>>>>>"+packCount);  
		var outUri = domainName + "webService_test.jsp"; 
		var innerUri = "http://" + webServiceUrl + "/sm/ws/PDACheckStockService";
		var method = "exeCheckStockPackUpload";
		//按2500记录数为单位分批上传
		var pageSize = 2500;
		var loopCount  = Math.floor(packCount/pageSize); 
		var curUploadSeq = 0 ; 
		
		while(curUploadSeq <= loopCount){
			var currentPackArray = orignPackArray.splice(0,pageSize);
			var curPackCount = currentPackArray.length;
			console.log("第"+(curUploadSeq+1)+"次上传捆包个数"+curPackCount);
			var curCheckId = localStorage.getItem("checkId")==null?"":localStorage.getItem("checkId");
			var curCheckType = localStorage.getItem("checkType")==null?"":localStorage.getItem("checkType");
			//console.log(segNO+userName + localStorage.getItem("checkId")+ localStorage.getItem("checkType"));
			var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","check_id":"' + curCheckId + '","operate_type":"' + curCheckType + '","pack_list":' + JSON.stringify(currentPackArray) + '}';
			$.ajax({
			        type:"post",
					url:outUri, 
					dataType:"json",
					async:false,//外面有循环，异步需谨慎
					data: {
					    innerUri:innerUri,
					    params:params,
					    method:method
					},
				    success: function(result) {
						 if(null != result){
						 	if(curUploadSeq == loopCount){
						 		mui.toast("本次成功上传捆包"+packCount+"个");
								var logTxt = segNo+"-"+userName+"时间"+getCurrentTime()+"自动上传捆包"+packCount+"个";
								writeUploadLogTxt(logTxt);//记录上传日志
								copyScanedPackTxt(fentry);//文件备份后移除
								clearUploadSum();//清楚缓存中的统计信息
						 	}
							curUploadSeq++;
						 }else{//连接失败
						 	mui.alert("服务器处理异常","系统提示","确定",function() {},'div');
						}
				  },
				  error: function(XMLHttpRequest, textStatus, errorThrown) {
						mui.alert("服务器连接异常，请稍后再试","系统提示","确定",function() {}, "div");
					} 
			 });
		}
}

//已扫描数据文件备份
function copyScanedPackTxt(fentry){
	var newName = "upload" + getCurrentTime() + ".txt";
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(fileEntry) {
			fentry.moveTo(fileEntry, newName, function(entry) {
				var logTxt = segNo+"-"+userName+"时间"+getCurrentTime()+"第二次登陆选择删除已盘文件"+packCount+"个";
				writeUploadLogTxt(logTxt);//记录上传日志
			}, function(e) {
				mui.alert("文件备份失败" + e.message, "系统提示", "确定", function() {}, 'div');
			});
		});
	});
}

//记录当前上传日志
function writeUploadLogTxt(logTxt){
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
	// fs.root是根目录操作对象DirectoryEntry
	fs.root.getFile('PDA/pklog.txt', {
		create: true
	}, function(fileEntry) {
		fileEntry.createWriter(function(writer) {
			writer.onerror = function() {
				mui.alert("保存上传日志失败", "系统提示", "确定", function() {}, 'div');
			}
			writer.seek(writer.length); //从文件默认追加新的内容
			//var data = segNo+"-"+userName+"时间"+getCurrentTime()+"手动上传捆包"+scanPackList.length+"个";
			if(writer.length == 0) {
				writer.write(logTxt);
			} else {
				writer.write("\n" + logTxt);
			}
		 }, function(e) {
				mui.alert("加载上传日志文件对象失败", "系统提示", "确定", function() {}, 'div');
		    });
		});
	});
}

mui(document.body).on('tap','#back',function(){
	mui.back();
});

mui.back = function () {  
	mui.doAction('backs');//退回上一个页面
}