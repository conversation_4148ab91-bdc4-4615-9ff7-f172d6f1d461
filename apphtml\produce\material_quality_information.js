var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var scanPackList = new Array(); //已扫描捆包列表
var ownPackInfoList = new Array(); //接收捆包查询结果
var checkstatus = "0"; // 判断扫描捆包是否为扫描捆包列表信息中

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//并包按钮
mui(document.body).on('tap', '#confirm', function() {
	exeOwnGoodVehicleOut();
});


//新增捆包
mui(document.body).on('tap', '#add', function() {
	alert(123)
});

//提交
mui(document.body).on('tap', '#save', function() {
	alert(321)
});

// 显示去重
function deWeight(arr) {
	for(var i = 0; i < arr.length - 1; i++) {
		for(var j = i + 1; j < arr.length; j++) {
			if(arr[i].pack_id == arr[j].pack_id) {
				arr.splice(j, 1);
				j--;
			}
		}
	}
	return arr;
}

//捆包扫描输入按下监听
$("#to_scan_pack").keypress(function(e) {
	var pack_id = $("#to_scan_pack").val();
	console.log(pack_id);
	console.log(segNo);
	$.each(scanPackList, function(i, item) {
		console.log(item.pack_id)
		if(item.pack_id == pack_id) {
			mui.alert(item.pack_id + "捆包已扫描过，请勿重复操作！", "提示", "确定", function() {}, "div");
		}
	});
	// 调用后台捆包查询接口， 根据返回的数据判断捆包是否存在，如果存在就添加到scanPackList，不存在就弹窗
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '"}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "queryByPackId";
	$.ajax({
		type: "post",
		//contentType:"application/json",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			console.log(result)
			ownPackInfoList = result.saleOutList;
			console.log(JSON.stringify(result));
			if(ownPackInfoList.length != 0) {
				$.each(ownPackInfoList, function(i, item) {
					console.log(item.PACK_ID)
					var scanPackInfo = {};
					scanPackInfo.pack_id = item.PACK_ID;
					scanPackInfo.seg_no = segNo;
					scanPackInfo.user_id = user_id;
					scanPackInfo.remark = item.REMARK;
					scanPackInfo.product_id = item.PRODUCT_ID;
					scanPackInfo.product_plan_id = item.PRODUCT_PLAN_ID;
					scanPackList.push(scanPackInfo);
				});
				showMateSuccessPackInfo();
				$("#to_scan_pack").val("");
			} else {
				mui.alert("该扫描捆包不为在库捆包，请重试！", "提示", "确定", function() {}, "div");
				$("#to_scan_pack").val("");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(getnowtime());
			mui("#confirm").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
			this;
		}
	});

	/*var scanPackInfo = {};
	scanPackInfo.pack_id='1';
	scanPackInfo.status='2';
	scanPackList.push(scanPackInfo);*/
	/*console.log(JSON.stringify(scanPackList))
	$.each(scanPackList, function(i, item) {
		console.log(item.pack_id)
		if(item.pack_id == $("#to_scan_pack").val()){
			mui.alert(item.pack_id + "捆包已扫描过，请勿重复操作！", "提示", "确定", function() {}, "div");
		}else{
			// 调用后台捆包查询接口， 根据返回的数据判断捆包是否存在，如果存在就添加到scanPackList，不存在就弹窗
			console.log(123)
			var scanPackInfo = {};
			scanPackInfo.pack_id='3';
			scanPackInfo.status='2';
			scanPackList.push(scanPackInfo);
			delKb(1);
		}
	});	*/
});

// 删除方法 捆包扫描列表支持删除功能，将已扫描捆包删除
function delKb(pack_id) {
	for(var i = 0; i < scanPackList.length; i++) {
		if(scanPackList[i].pack_id == pack_id) {
			scanPackList.splice(i, 1);
			mui.alert(pack_id + "删除成功！", "提示", "确定", function() {}, "div");
			showMateSuccessPackInfo();
		}
	}
}

// 显示已扫描的捆包信息
function showMateSuccessPackInfo() {
	scanPackList = deWeight(scanPackList);
	console.log("scanPackList:" + JSON.stringify(scanPackList));
	//绘制已扫捆包信息
	var phtml = "";
	var prot = "";
	$.each(scanPackList, function(i, item) {
		console.log(item.pack_id)
		/*phtml = phtml + '<li class="mui-table-view-cell">' +
			'<div class="mui-slider-handle">' +
			'<div id="pack_id" onclick=""  data="' + item.pack_id + '">' + item.pack_id + '</div>' +
			'<div>' +
			'<div id="status" class="left"><span>捆包状态</span><label>11111111</label></div>' +
			'<div id="prot" ><span>资源号</span><label>6666666666' + prot + '</label><button type="button" class="mui-btn mui-btn-blue" onclick="delKb(\'' + item.pack_id + '\')">删除</button></div>' +
			'</div>' +
			'</a>' +
			'</li>';*/
		phtml = phtml + '<li class="mui-table-view-cell">' +
		//'<div class="mui-slider-handle">' +
		'<div id="pack_id" onclick=""  data="' + item.pack_id + '">' + item.pack_id +
		'<button type="button" id="left" class="mui-btn mui-btn-blue" onclick="delKb(\'' + item.pack_id + '\')">删除</button></div>' +
		'</li>';
	});
	$("#phtml").html(phtml);
}


function tz(){
	alert(1)
}


// 并包方法
function exeOwnGoodVehicleOut() {
	//var a = '666';
	//mui.alert("并包成功  ! 并包号" + a, "提示", "确定", function() {}, "div");
	//console.log("scanPackList:" + JSON.stringify(scanPackList));
	if(scanPackList.length == 0) {
		mui.alert("请扫描要并包的捆包!", "提示", "确定", function() {}, "div");
	} else if(scanPackList.length == 1){
		mui.alert("选择为1个捆包，不能并包!", "提示", "确定", function() {}, "div");
	}else {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var outUri = domainName + "webService_test.jsp";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		var params = JSON.stringify(scanPackList);
		params = encodeURI(params, 'utf-8');
		var method = "exeUnitePack";
		$.ajax({
			type: "post",
			//contentType:"application/json",
			async: true,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(result) {
				console.log(JSON.stringify(result))
				if(result.out_result != 0){
					mui.alert("并包成功  ! 并包号" + result.out_result_desc, "提示", "确定", function() {}, "div");
				}else{
					mui.alert(result.out_result_desc, "提示", "确定", function() {}, "div");
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				//console.log(getnowtime());
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
				this;
			}
		});
	}
}