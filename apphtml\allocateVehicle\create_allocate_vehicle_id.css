/*字段显示*/

#allocate_from,
#vehicle_id,
#customer_id,
#customer_name,
#car_arrive_time,
#driver_name,
#id_card,
#phone_number {
	width: 70%;
	font-size: 20px;
	float: right;
}


/*输入框样式*/

.text {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}


/*装卸下拉框*/

#hand_type {
	width: 50%;
	margin-top: 10px;
	text-align: center;
	text-align-last: center;
	font-size: 22px;
	color: red;
}


/*交货方式*/

#deliver_type {
	width: 50%;
	text-align: center;
	text-align-last: center;
	font-size: 22px;
}

.detail_row {
	height: 45px;
}

#peidan_type {
	width: 70%;
	border: 1px solid rgba(0, 0, 0, .2) !important;
	font-size: 20px;
	padding-left: 20px;
	/* -webkit-appearance: auto; */
	background: url(../../resource/icon_bottom.png) no-repeat right center;

}


/*按钮*/

#btn_new_sub {
	width: 98%;
	font-size: 16px;
	line-height: 1.8;
	float: left;
	margin: 5px;
}


/*-----------------TODO---------------------------------------*/


/** 弹出图层设置 */

#InnerVehicleDiv.show {
	visibility: visible;
	opacity: 1;
}

#InnerVehicleDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 35%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#InnerVehicleDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

.mui-input-row .mui-icon-search {
	font-size: 24px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 42px;
	/*height: 30px;*/
	/*text-align: left;*/
	color: #999;
}

.mui-input-clear {
	width: 45%;
}

.select {
	width: 18% !important;
	font-size: 20px !important;
	background-color: white;
	border: 1px solid rgba(0, 0, 0, .2) !important;
}

.active {
	background-color: red;
	color: white;
}


/*弹出框end*/

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

#province_name {
	width: 50px;
	height: 30px;
	background-color: #007aff;
	float: left;
	color: white;
	margin-top: 2px;
	border-radius: 3px;
	font-size: 24px;
	font-weight: bolder;
	margin-top: 5px;
	margin-left: 20px;
}

#car_num {
	/*width: 61%;*/
	height: 30px;
	padding: 0px 5px;
	font-size: 18px;
	margin-top: 5px;
	border-radius: 3px;
}

#button_province {
	width: 23.5%;
	margin-top: 4px;
	font-size: 18px;
}

#province {
	margin-left: 3px;
	display: none;
	position: absolute;
	bottom: 5px;
	line-height: 1.8;
	background-color: #efeff4;
	z-index: 0;
}


/*搜索图标*/

.icon-search {
	position: absolute;
	z-index: 5;
	background-image: url(../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 5px;
}

#allocate_from,
#vehicle_id,
#customer_id,
#customer_name,
#car_arrive_time,
#driver_name,
#id_card,
#phone_number {
	width: 70%;
	font-size: 20px;
	float: right;
}

/*输入框样式*/

.text {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

/*装卸下拉框*/

#hand_type {
	width: 50%;
	margin-top: 10px;
	text-align: center;
	text-align-last: center;
	font-size: 22px;
	color: red;
}

/*交货方式*/

#deliver_type {
	width: 50%;
	text-align: center;
	text-align-last: center;
	font-size: 22px;
}

.detail_row {
	height: 45px;
}

/*按钮*/

#btn_new_sub {
	width: 98%;
	font-size: 16px;
	line-height: 1.8;
	float: left;
	margin: 5px;
}

/*-----------------TODO---------------------------------------*/
/** 弹出图层设置 */

#InnerVehicleDiv.show {
	visibility: visible;
	opacity: 1;
}

#InnerVehicleDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 35%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#InnerVehicleDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

.mui-input-row .mui-icon-search {
	font-size: 24px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 42px;
	/*height: 30px;*/
	/*text-align: left;*/
	color: #999;
}

.mui-input-clear {
	width: 45%;
}

.select {
	width: 18% !important;
	font-size: 20px !important;
	background-color: white;
	border: 1px solid rgba(0, 0, 0, .2) !important;
}

.active {
	background-color: red;
	color: white;
}

/*弹出框end*/

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

#province_name {
	width: 50px;
	height: 30px;
	background-color: #007aff;
	float: left;
	color: white;
	margin-top: 2px;
	border-radius: 3px;
	font-size: 24px;
	font-weight: bolder;
	margin-top: 5px;
	margin-left: 20px;
}

#car_num {
	/*width: 61%;*/
	height: 30px;
	padding: 0px 5px;
	font-size: 18px;
	margin-top: 5px;
	border-radius: 3px;
}

#button_province {
	width: 23.5%;
	margin-top: 4px;
	font-size: 18px;
}

#province {
	margin-left: 3px;
	display: none;
	position: absolute;
	bottom: 5px;
	line-height: 1.8;
	background-color: #efeff4;
	z-index: 0;
}

/*搜索图标*/

.icon-search {
	position: absolute;
	z-index: 5;
	background-image: url(../../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 5px;
}
