var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var user_name = localStorage.getItem("account"); //采用localStorage存储数据
var factory_area_id = localStorage.getItem("factory_area_id"); //采用localStorage存储数据
var vehicle_numer = "";
var switchhtml = ""; //页面上控制的开关
var switchorac = ""; //数据库根据状态显示的开关
var status = "";
var hand_point_id = "";
var old_vehicle_numer = "";
var close_reason = "";
var reason_list = new Array();
/*
 * 变量定义
 */
mui.init({
	swipeBack: true //启用右滑关闭功能

});

$(function() {
	queryCloseReasonData();
	//调用webservices接口
	//autoHandPintManage();
	//预计恢复时间
	var myDate = new Date();
	var dtpicker = new mui.DtPicker({
		type: "datetime", //设置日历初始视图模式 
		beginDate: new Date(myDate.getFullYear(), myDate.getMonth(), myDate.getDate()), //设置开始日期 
		endDate: new Date(myDate.getFullYear(), myDate.getMonth() + 1, myDate.getDate()), //设置结束日期 
		labels: ['年', '月', '日', '时', '分'], //设置默认标签区域提示语 
		customData: {
			/* h: [
			     { value: 'AM', text: 'AM' },
			     { value: 'PM', text: 'PM' }
			 ] */
		} //时间/日期别名 
	});
	mui(document.body).on('tap', '#time_huifu', function() { //绑定事件
		dtpicker.show(function(items) {
			$("#time_huifu").val(items.text); //dtpicker.dispose();
		});
	});
});
//结束预计恢复时间

function getCloseList() {

}

//进入装卸点选择界面
mui(document.body).on("tap", ".icon-setting", function() {
	mui.openWindow({
		url: 'select_hand_point.html',
		id: 'select_hand_point',
		extras: {
			openType: 'handPoint', //handPoint代表厂内装卸点查询 hand_point_end代表选择装卸点查询
		},
		createNew: true
	});
});
//下拉框变更触发事件
function reason_change(colse_value) {
	close_reason = colse_value;
	console.log(close_reason);
}
//勾选装卸点信息
mui(document.body).on('selected', '.mui-table-view-cell', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	hand_point_id = $(this).attr("p_id"); //el_J.find("#hand_point_id").text();
	vehicle_numer = $(this).attr("p_vehicle_numer");
	var aa = document.getElementById(hand_point_id);
	switchorac = aa.classList.contains("mui-active");
	if (switchorac) {
		status = '30';
	} else {
		status = '99';
	}
});
//监听开关事件
this.addEventListener('toggle', function(event) {
	switchhtml = event.detail.isActive;
	console.log(switchhtml);
});
/*//获取numbox点击事件
function getNumbox(getNum){
	var el = getNum.parentNode;
	var el_J = $(el);
	vehicle_numer =el_J.find("#vehicle_numer").val();
}*/
//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	console.log(switchhtml + "," + switchorac + "," + status);
	var tingyong = false;
	if (switchhtml) {
		status = '30';
		tingyong = true;
		// console.log("1:"+switchhtml+","+switchorac+","+status+","+tingyong);
	} else if (switchhtml) {
		status = '99';
		tingyong = false;
		// console.log("2:"+switchhtml+","+switchorac+","+status+","+tingyong);
	} else {
		if (switchorac) {
			status = '30';
			tingyong = true;
			//console.log("3:"+switchhtml+","+switchorac+","+status+","+tingyong);
		} else {
			status = '99';
			tingyong = false;
			//console.log("4:"+switchhtml+","+switchorac+","+status+","+tingyong);
		}
	}

	if (hand_point_id == '' || hand_point_id == null) {
		mui.alert("请勾选装卸点记录", "提示", "确定", null, 'div');
		return;
	}
	console.log("5:" + switchhtml + "," + switchorac + "," + status + "," + tingyong);
	if (!tingyong) {
		var times = $("#time_huifu").val();
		if (times == "" || times == null) {
			mui.alert("请选择预计恢复时间", "提示", "确定", null, 'div');
			return;
		}

		if (close_reason == "00" || close_reason == "") {
			mui.alert("请选择停用原因", "提示", "确定", null, 'div');
			return;
		}
	} else {
		close_reason = "";
	}
	//console.log("----status:"+status+",close_reason:"+close_reason);
	confirmHandPoint();
});

//自动查询
function autoHandPintManage() {
	$("#time_huifu").val("");
	console.log(reason_list.length);
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var car_trace_no = "";
	var openType = "";
	var params = '{"seg_no":"' + seg_no + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no +
		'","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
		if (null != data) { //连接成功
			//	console.log(data.resultList.length);
			if (data.resultList.length > 0) {
				console.log(JSON.stringify(data.resultList));
				var chtml = "";
				$.each(data.resultList, function(i, item) {
					/*var reson="";
					if(item.close_reason=="" || item.close_reason==null){
						reson=" ";
					}else{
						reson=item.close_reason;
					}*/
					chtml = chtml + '<li class="mui-table-view-cell" p_id="' + item.hand_point_id + '" p_vehicle_numer="' + item.vehicle_numer +
						'">' +
						'<a class="mui-navigate-right" >' +
						'<div id="hand_point_id"style="display: none;">' + item.hand_point_id + '</div>' +
						'<div id="hand_point_name"style="width: 110px;float: left;">' + item.hand_point_name + '</div>'
						/*    +'<div style="width: 130px;float: left;" >'
								+'<div class="mui-numbox" id="vehicle_numer_numbor"   >'
									+'<button onclick='+'getNumbox(this)'+' class="mui-btn mui-numbox-btn-minus" type="button">-</button>'
									+'<input  id="vehicle_numer" value="'+item.vehicle_numer+'" class="mui-numbox-input" type="number" />'
									+'<button  onclick='+'getNumbox(this)'+' class="mui-btn mui-numbox-btn-plus" type="button">+</button>'
						   		+'</div>'
							+'</div>'*/
						+
						'<div calss="aa" style="width: 10px;float: left; margin-left: 10px;">' +
						'<div id="' + item.hand_point_id + '"  class="mui-switch ' + item.status + '">' +
						'<div class="mui-switch-handle"></div>' +
						'</div>' +
						'</div>' +
						'<div  style="width: 80px;float: left;">' +
						'<select id="colse_xialakuang" name="" style="margin-left: 70px;" onchange="reason_change(this.options[this.options.selectedIndex].value)">';
					var optionss = "";
					if (reason_list.length > 0) {
						var cchh = '<option value="00">&nbsp;&nbsp;&nbsp;</option>';
						$.each(reason_list, function(j, reitem) {
							if (item.close_reason == reitem.value) {
								close_reason = reitem.value;
								cchh = cchh + '<option value="' + reitem.value + '" selected="selected">' + reitem.text + '</option>';
							} else {
								cchh = cchh + '<option value="' + reitem.value + '" >' + reitem.text + '</option>';
							}
						});
					}
					chtml = chtml + cchh + '</select></div>' +
						'</a>' +
						'</li>';
				});
				$("#hand_point_List").html(chtml);
				mui('.mui-numbox').numbox();
				mui('.mui-switch')['switch']();
			}
		} else { //连接失败
			alert("服务器连接异常");
		}
	});
}

function confirmHandPoint() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + seg_no + '","hand_point_id":"' + hand_point_id + '","status":"' + status +
		'","vehicle_numer":"' + vehicle_numer + '","user_name":"' + user_name + '","factory_area":"' + factory_area_id +
		'","close_reason":"' + close_reason + '","expected_recovery_time":"' + $("#time_huifu").val() + '"}';
	var method = "exeConfirmHandPoint";
	console.info("aaa" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
		if (null != data) { //连接成功
			//console.log(data.resultList.length);
			if (data.resultStatus == "1") {
				mui.alert("确认成功！", "提示", "确定", function() {
					autoHandPintManage();
				}, 'div');
				return;
			} else {
				alert("确认失败！");
			}
		} else { //连接失败
			alert("服务器连接异常");
		}
	});
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});
mui.back = function() {
	mui.openWindow({
		id: "load_unload_menus",
		url: "load_unload_menus.html",
		createNew: true
	});
}

function queryCloseReasonData() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + seg_no + '","code_type":"STOP_CAUSE"}';
	var method = "exeQueryFactoryArea";
	console.info("exeQueryFactoryArea params:" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.info("exeQueryFactoryArea data:" + JSON.stringify(data));
		if (data != null) {
			console.log(JSON.stringify(data));
			if (data.resultStatus == "1") {
				reason_list = data.codeList;
				console.log(JSON.stringify(reason_list));
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
		autoHandPintManage();
	});
}
