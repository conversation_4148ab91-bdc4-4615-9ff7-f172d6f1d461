/**
 * 变量定义
 */
var listCarNum = "";
var confirmCarNum = "";
var carArray = {};
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var open = "";
mui.init({
	swipeBack: true //启用右滑关闭功能  
});

mui.plusReady(function() {
	//检测是否有网络连接
	var curNetConnetType = plus.networkinfo.getCurrentType();
	if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
		curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
		plus.nativeUI.toast("未找到可用的网络，请检查", {
			'verticalAlign': 'center'
		});
	} else {
		open = plus.webview.currentWebview().open;
		queryEnterFacoryVehicleNo("");
	}
});

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	listCarNum = e.detail.el.innerText == null ? "" : e.detail.el.innerText.trim();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	var inputCarNum = $("#car_no").val() == null ? "" : $("#car_no").val().trim();
	if (carArray.length > 0) {
		if (listCarNum == "" && inputCarNum == "") {
			mui.alert("请选择或输入车牌号", "提示", "确定", null, 'div');
			return;
		} else if (listCarNum != "") {
			confirmCarNum = listCarNum;
		} else if (inputCarNum != "") {
			confirmCarNum = inputCarNum;
		}
	} else {
		if (inputCarNum == "") {
			mui.alert("请手工输入车牌号", "提示", "确定", null, 'div');
			return;
		} else {
			confirmCarNum = inputCarNum;
		}
	}
	//确定的车牌号，放入全局缓存 中
	localStorage.setItem("putin_vehicle_no", confirmCarNum);
	console.log(">>>>>>>>>>>>>>>>>>>>>inputCarNum:" + confirmCarNum);
	insertVehicleNo(confirmCarNum);
	mui("#confirm").button("loading");
	$("#overlay").addClass("overlay");

	//by tangli add ERP_51191  start 
	//关闭当前页面(入库捆包清单页面)
	/*var ws=plus.webview.currentWebview();
	console.log(">>>>>>>>>>>>>>"+ws);
	plus.webview.close(ws);*/
	//by tangli add ERP_51191  end 

	//跳转到入库捆包扫描界面
	//modify by lal 长春收货
	console.log(">>>>>>>>>>>>>>>>>" + segNo + open);
	if (segNo == "00112" && open == "synergy") {
		mui.openWindow({
			url: '../changChunSynergy/changchun_synergy_scan.html',
			id: 'changchun_synergy_scan',
			createNew: true
		});
	} else if (segNo == "00137" && open == "sgm_gf_putin") {
		mui.openWindow({
			url: '../sgmGFPutin/sgm_cch.html',
			id: 'sgm_cch',
			createNew: true
		});
	} else {
		mui.openWindow({
			url: '../putin/putin_scan.html',
			id: 'putin_scan',
			createNew: true
		});
	}

});

//搜索
mui(document.body).on('tap', '#query_button',
	function() {
		var car_no = $("#car_no").val();
		queryEnterFacoryVehicleNo(car_no);
	});

//车辆入厂车牌信息查询方法
function queryEnterFacoryVehicleNo(car_no) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据

	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"' + segNo + '","vehicle_no":"' + car_no + '"}';
	console.log("queryVehicleLicense接口param：" + params);
	params = encodeURI(params, 'utf-8');
	var method = "queryVehicleLicense";

	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("queryVehicleLicense接口返回：" + JSON.stringify(data));
		if (data != null) {
			//先清空当前列表中的牌号信息
			var lihtml = "";
			$("#companyList").html(lihtml);
			//填充最新的牌号信息
			if (data.returnList.length > 0) {
				carArray = data.returnList;
				$.each(data.returnList, function(i, item) {
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;" >' +
						'<label>' + item.vehicle_no + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#companyList").html(lihtml);
			} else {
				mui.alert("未查询到车牌信息，请人工指定", "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//车辆入厂车牌新增方法
function insertVehicleNo(car_no) {
	console.log(">>>>>>>>>>>>>>>>1");
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","vehicle_no":"' + car_no + '","user_id":"' + user_id + '"}';
	console.log(">>>>>>>>>>>>>>" + JSON.stringify(params));
	params = encodeURI(params, 'utf-8');
	var method = "exeCheckVehicleNo";

	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(">>>>>>>>>>>>>>" + JSON.stringify(data));
		if (data != null && data != "") {
			console.log(">>>>>>>>>>>>>>" + JSON.stringify(data));
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
