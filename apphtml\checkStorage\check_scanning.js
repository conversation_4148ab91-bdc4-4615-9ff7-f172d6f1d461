var webServiceUrl;
var checkId;
var checkType;
var segNo;
var userName;
var scanPackList = new Array();
var packList; //存入下载的捆包列表
var downLoadCount; //已下载
var uploadedCount; //已上传
var uploadingCount; //待上传
var recentPackId;
var profitFlag;
var location_id = "";
var if_judge_pack_location = "0"; //库位开关
　var packData = localStorage.getItem("packData");
//add by penglei ** add by tangli 20191203 湛江现货流程 自动获取用户对应库位
var pack_location_zj = "";

$(function() {
	mui.init({
		swipeBack: false //启用右滑关闭功能
	});

});

//页面加载事件
/*window.onload = function onload() {
	mui.plusReady(function() {
		//queryConfigPDAVoucherCountMax();
	});
}*/

function plusReady() {}
if (window.plus) {
	plusReady();
} else {
	document.addEventListener('plusready', plusReady, false);
}

$(document).ready(function() {
	initParams();
	queryConfigPDAVoucherCountMax();
	// add by penglei 湛江盘库库位查询账号对应库区 **add by tangli 20191126 ERP_58339 湛江物流入库查询账号对应外库库区
	if ("00166" == segNo) {
		getPacklocation_zj();
	}
	//swipeBack(false);
	//postBack();
	//add by wangshengbo 20180307 开关控制已盘捆包是否需要人工确认信息
	profitFlag = getSwitchValue(segNo, 'PDA_PROFIT_PACK_CONFIRM');

	addListener();
});


function initParams() {
	$("#packLocation")[0].focus(); //库位输入框获得焦点
	localStorage.setItem("qty", qty); //用于累加合计
	localStorage.setItem("weight", weight);
	packList = localStorage.getItem("packList");
	if (null != packList && packList.length > 0) {
		packList = JSON.parse(packList);
	} else {
		packList = new Array()
	}
	webServiceUrl = localStorage.getItem("webServiceUrl");
	checkId = localStorage.getItem("checkId");
	checkType = localStorage.getItem("checkType");
	segNo = localStorage.getItem("segNo");
	userName = localStorage.getItem("account");
	downLoadCount = parseInt(localStorage.getItem("downLoadCount"));
	uploadedCount = parseInt(localStorage.getItem("uploadedCount"));
	uploadingCount = parseInt(localStorage.getItem("uploadingCount"));
	recentPackId = localStorage.getItem("recentPackId");
	$('#downLoadCount').html("已下载:" + downLoadCount);
	$('#uploadedCount').html("已上传:" + uploadedCount);
	$('#uploadingCount').html("待上传:" + uploadingCount);
	$('#recentPackId').html(recentPackId);
	f1();
}

function f1(){
　　setTimeout(function () {
　　　　// f1的任务代码
var packDataStatus = localStorage.getItem("packDataStatus");
	console.log("packData = "+JSON.stringify(packData)); 
	console.log("packDataStatus = "+packDataStatus); 
　　}, 1000);
}

function addListener() {
	$("#packLocation").keypress(function(e) {
		if (e.keyCode == 13) {
			if (if_judge_pack_location == "1") { //库位开关打开
				judgeLocationExist();
			} else {
				$("#packId")[0].focus(); //捆包输入框获得焦点
			}

		}
	});
	$("#packId").keypress(function(e) { //捆包号
		console.log('触发');
		if (null == scanPackList || scanPackList == "") { //读取本地存储的已扫描的捆包
			scanPackList = new Array();
		} else if (localStorage.getItem("scanPackList") == "") {
			scanPackList = new Array();
		} else {
			scanPackList = JSON.parse(localStorage.getItem("scanPackList"));
		}
		if (e.keyCode == 13) {
			//add by xuhuaijun 20161229 格式化捆包号
			var packId = $("#packId").val();
			//20211201 yangzemin 增加空校验，不校验空值会匹配出捆包
			if(packId == "" || packId.trim() == ""){
				mui.toast("请扫描捆包号");
				return;
			}
			packId = formatPackId(packId);
			//add by xuhuaijun 20180925 武钢拼焊
			packId = trimPackId(packId);
			$("#packId").val(packId);
			if (segNo != '00112') {
				if (getCheckPackIsExist($('#packId').val(), scanPackList)) { //校验捆包号是否重复
					mui.toast("捆包号不能重复扫描");
					$("#packId").val("");
					$("#packId")[0].focus();
					return false;
				}
			}
			//调用ajax获取捆包内容
			var result = getItem($("#packId").val());
			if (!result) {
				if (segNo == "00166" || segNo == "00138") {
					result = {};
					result.spec = '';
					result.check_weight = '';
					result.product_id = '';
					result.pack_location = location_id; //$('#packLocation').val();
					result.check_qty = '';
					result.label_id = $('#packId').val();
					result.pack_id = $('#packId').val();
					result.quality_info = '';
					save2ScanList(result);
				} else {
					if ((profitFlag != 1 && segNo != '00112') || (segNo == '00112' && packId != '')) {
						mui.toast("未匹配到捆包。请确认捆包信息后，点修改按钮");
					} else {
						result = {};
						result.spec = '';
						result.check_weight = '';
						result.product_id = '';
						result.pack_location = location_id; //$('#packLocation').val();
						result.check_qty = '';
						result.label_id = $('#packId').val();
						result.pack_id = $('#packId').val();
						result.quality_info = '';
						save2ScanList(result);
					}
				}

			} else {
				save2ScanList(result);
			}

			/*if(result == 'no') {
				mui.toast("请补全信息");
			} else {
				save2ScanList(result);
			}*/
		}
	});
	//修改按钮绑定事件
	mui(document.body).on('tap', '#edit', function() {
		var packId = $("#packId").val();
		if (getCheckPackIsExist(packId, scanPackList)) {
			var index = getCheckPackIdIndex(packId, scanPackList);
			scanPackList[index].new_pack_location = $("#packLocation").val();
			scanPackList[index].spec = $("#spec").val();
			scanPackList[index].check_weight = $("#weight").val();
			scanPackList[index].check_qty = $("#qty").val();
			scanPackList[index].quality_info = $("#qualityInfo").val();
			scanPackList[index].quality_flg = $('#qualityFlg').html() == '无异议' ? '0' : '1';
			localStorage.setItem("scanPackList", JSON.stringify(scanPackList));
			localStorage.setItem("recentPackId", scanPackList[index].pack_id);
			$('#recentPackId').html(scanPackList[index].pack_id);
			mui.toast("修改成功");
			clearText();
		} else if ("" == $("#packId").val()) {
			mui.toast("请输入捆包号");
			/*}else if("" == $("#spec").val()){
				mui.toast("请输入规格");
			}else if("" == $("#weight").val()){
				mui.toast("请输入重量");
			}else if("" == $("#qty").val()){
				mui.toast("请输入数量");*/
		} else if ("" == $("#packLocation").val()) {
			mui.toast("请输入库位");
		} else {
			var result = {};
			//result.这里创建新数据，把数据初始化好后添加到已扫描的数据中
			result.pack_id = $("#packId").val();
			result.product_id = "";
			result.spec = $("#spec").val();
			result.pack_location = "";
			result.label_id = "";
			result.check_weight = $("#weight").val();
			result.check_qty = $("#qty").val();
			save2ScanList(result);
			localStorage.setItem("recentPackId", result.pack_id);
			$('#recentPackId').html(result.pack_id);
			mui.toast("保存成功");
			clearText();
		}
	});
	//点击手机回退按键时。如果存在已扫描未上传数据则提示
	mui.back = function() {
		if (scanPackList != null && scanPackList.length > 0) {
			var btnArray = ['退出', '继续'];
			mui.confirm('存在已扫描未上传数据。确定退出本次盘库?', '系统提醒', btnArray, function(e) {
				if (e.index == 0) {
					//先清理缓存数据
					localStorage.setItem("scanPackList", "");
					//删除本地捆包文件
					deleteScanedPackTxt();
					var logTxt = segNo + "-" + userName + "时间" + getCurrentTime() + "强制退回放弃捆包" + scanPackList.length + "个";
					writeUploadLogTxt(logTxt);
					mui.doAction('backs'); //退回上一个页面
				}
			});
		} else {
			mui.doAction('backs'); //退回上一个页面
		}
	}

	//绑定盘库清单信息修改事件
	window.addEventListener('edit', function(event) {
		var productId = event.detail.productId;
		var packId = event.detail.packId;
		refreshPackInfo(productId, packId);
	});

	//绑定盘库清单信息回写事件
	window.addEventListener('preback',
		function(event) {
			$("#packLocation").val(event.detail.prePackLocation);
			scanPackList = event.detail.newScanedPackList;
			downLoadCount = parseInt(localStorage.getItem("downLoadCount"));
			uploadedCount = parseInt(localStorage.getItem("uploadedCount"));
			uploadingCount = parseInt(localStorage.getItem("uploadingCount"));
			recentPackId = localStorage.getItem("recentPackId");
			$('#downLoadCount').html("已下载:" + downLoadCount);
			$('#uploadedCount').html("已上传:" + uploadedCount);
			$('#uploadingCount').html("待上传:" + uploadingCount);
			$('#recentPackId').html(recentPackId);
		});

	//详情
	mui(document.body).on('tap', '#detail', function() {
		var prePackLocation = $("#packLocation").val();
		mui.openWindow({
			url: 'check_list.html',
			id: 'check_list',
			createNew: true,
			extras: {
				prePackLocation: prePackLocation,
				preScanPackList: scanPackList
			}
		});
	});

	//上传
	mui(document.body).on('tap', '#upload', function() {

		mui.plusReady(function() {
			var curNetConnetType = plus.networkinfo.getCurrentType();
			if (curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW &&
				curNetConnetType != plus.networkinfo.CONNECTION_NONE) {
				var btnArray = ['确定', '取消'];
				mui.confirm('确定上传本次盘库数据?', '提示', btnArray, function(e) {
					if (e.index == 0) {
						setTimeout(function() {
							//点击上传按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
							uploadButtonClickable(false);
							//先读本地文件内容，然后调用上传接口
							readerScanedPackTxt();
						}, 0);
					}
				}, 'div');
			} else {
				plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
			}
		});

	});
}

function uploadButtonClickable(isLoading) {
	if (isLoading) {
		mui("#upload").button('reset');
		$("#overlay").removeClass("overlay");
	} else {
		mui("#upload").button('loading');
		$("#overlay").addClass("overlay");
	}
}

//获取当前捆包在数据中的下标
function getCheckPackIdIndex(packId, packList) {
	var index = -1;
	$.each(packList, function(i, value) {
		if (value.pack_id == packId) {
			index = i;
			return false;
		}
	});
	return index;
}

//当前扫描捆包是否在下载捆包信息中找到，如果在返回true.不在返回false
function getCheckPackIsExist(id, list) {
	if (list != null) {
		for (var i = 0; i < list.length; i++) {
			if (id == list[i].pack_id || id == list[i].label_id || id == list[i].outPackId) {
				return true;
			}
		}
	}
	return false;
}

function save2ScanList(result) {
	result.new_pack_location = $("#packLocation").val();
	result.quality_info = $("#qualityInfo").val();
	scanPackList.push(initDataModel(result)); //向已扫描的捆包List中添加Json对象
	localStorage.setItem("recentPackId", result.pack_id);
	$('#recentPackId').html(result.pack_id);
	uploadingCount++; //这里对未上传的加1
	fillSum();
	localStorage.setItem("scanPackList", JSON.stringify(scanPackList));
	createScanedPackTxt(JSON.stringify(result));
	clearText();
}

function clearText() {
	$("#packId").val(""); //清空信息焦点定位到捆包
	$("#spec").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#qualityFlg").html("无异议");
	$("#qualityFlg").removeClass('active').addClass('active2');
	$("#qualityInfo").val("");
	$("#packId")[0].focus();
}

function changeDissent() {
	var ss = $("#qualityFlg").html();
	if (ss == "无异议") {
		$("#qualityFlg").html("有异议");
		$("#qualityFlg").removeClass('active2').addClass('active');
	} else if (ss == "有异议") {
		$("#qualityFlg").html("无异议");
		$("#qualityFlg").removeClass('active').addClass('active2');
	}
}

function getItem(packId) {
	var result = null; //modify by wangshengbo 170726即使没有扫描到的捆包不应单返回no
	for (var i = 0; i < packList.length; i++) {
		if (packList[i].packId == packId || packList[i].outPackId == packId) {
			result = getResultPack(packList[i]);
			break;
		}
	}

	if (!result) {
		for (var i = 0; i < packList.length; i++) {
			if (packList[i].labelId == packId || packList[i].outPackId == packId) {
				result = getResultPack(packList[i]);
				break;
			}
		}
	}
	return result;
}

function getResultPack(data) {
	var result = {};
	result.spec = data.spec;
	result.check_weight = data.checkWeight;
	result.product_id = data.productId;
	result.pack_location = data.packLocation;
	result.check_qty = data.checkQty;
	result.label_id = data.labelId;
	result.pack_id = data.packId;
	result.quality_info = data.qualityInfo;
	return result;
}

function initDataModel(result) {
	if (null == result.scan_time) {
		result.scan_time = getCurrentTime();
	}
	if (null == result.manage_no) {
		result.manage_no = getUUID();
	}
	if (null == result.quality_flg) {
		result.quality_flg = $('#qualityFlg').html() == '无异议' ? '0' : '1';
	}
	return result;
}

function fillSum() {
	localStorage.setItem("uploadingCount", uploadingCount);
	$('#uploadingCount').html("待上传:" + uploadingCount);
	localStorage.setItem("uploadedCount", uploadedCount);
	$('#uploadedCount').html("已上传:" + uploadedCount);
}

/**
 * 已扫描捆包存储到本地；
 */
var dir = null;
var localFile = "upload.txt"
var loacalDir = "PDA";
//document.addEventListener("plusready", createScanedPackTxt, false);


function createScanedPackTxt(data) {
	//alert("createScanedPackTxt");
	if (data != "[object Event]") {
		// 打开doc根目录
		plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
			fs.root.getDirectory(loacalDir, {
				create: true
			}, function(entry) {
				dir = entry;
				dir.getFile(localFile, {
					create: true
				}, function(fentry) {
					fentry.createWriter(function(writer) {
						writer.onerror = function() {
							mui.alert("捆包保存失败", "系统提示", "确定", function() {}, 'div');
						}
						//alert("writer>>>>>>>>>>>>>>>>");
						writer.seek(writer.length); //从文件默认追加新的内容
						if (writer.length == 0) {
							writer.write(data + ",");
						} else {
							if (data != null) {
								writer.write("\n" + data + ",");
							}
						}
					}, function(e) {
						mui.alert("加载已盘捆包文件失败", "系统提示", "确定", function() {}, 'div');
					});
				}, function(e) {
					mui.alert("已盘捆包本地文件不存在", "系统提示", "确定", function() {}, 'div');
				});
			}, function(e) {
				mui.alert("已盘捆包本地文件路径不存在", "系统提示", "确定", function() {}, 'div');
			});
		}, function(e) {
			mui.alert("系统异常", "系统提示", "确定", function() {}, 'div');
		});
	}
}

// 读取本地已盘捆包文件
function readerScanedPackTxt() {
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(entry) {
			entry.getFile(localFile, {
				create: false
			}, function(fentry) {
				console.log("获取文件路径fentry>>>>>>>>>>>");
				var waitingDialog = plus.nativeUI.showWaiting("数据上传中...");
				fentry.file(function(file) {
					var reader = new plus.io.FileReader();
					reader.readAsText(file, 'utf-8');
					reader.onloadend = function(e) {
						var data = null;
						try {
							data = e.target.result;
							console.log("上传的捆包列表：" + JSON.stringify(data))
							if (data == "[]" || data == "") {
								waitingDialog.close();
								mui.alert("没有待上传的捆包数据", "系统提示", "确定", function() {}, 'div');
								uploadButtonClickable(true);
								return;
							} else {
									data = "[" + data.substring(0, data.length - 1) + "]";
								// if (data.indexOf("[") < 0 && data.indexOf("]") < 0) {
								// 	data = "[" + data.substring(0, data.length - 1) + "]";
								// }
							}
						} catch (e) {
							file.close();
							waitingDialog.close();
							mui.alert("读取已盘捆包文件失败,请联系开发人员", "系统提示", "确定", function() {}, 'div');
							uploadButtonClickable(true);
							return;
						}

						try {
							chkPackUpload(data, fentry);
						} catch (e) {
							file.close();
							waitingDialog.close();
							mui.alert("捆包上传失败,请联系开发人员", "系统提示", "确定", function() {}, 'div');
							uploadButtonClickable(true);
							return;
						}
						file.close();
						waitingDialog.close();
					}
				}, function(e) {
					waitingDialog.close();
					mui.alert("加载已盘捆包文件失败", "系统提示", "确定", function() {}, 'div');
					uploadButtonClickable(true);
				});
			}, function(e) {
				mui.alert("没有待上传的捆包数据", "系统提示", "确定", function() {}, 'div');
				uploadButtonClickable(true);
			});
		}, function(e) {
			mui.alert("没有待上传的捆包数据", "系统提示", "确定", function() {}, 'div');
			uploadButtonClickable(true);
		});
	}, function(e) {
		mui.alert("系统异常", "系统提示", "确定", function() {}, 'div');
		uploadButtonClickable(true);
	});
}

//上传调用服务端接口
function chkPackUpload(orignPackList, fentry) {
	var orignPackArray = {};
	orignPackArray = eval(orignPackList);
	var packCount = orignPackArray.length;
	//alert("总捆包个数>>>>>>>>>>>>>>>>"+packCount);  
	var outUri = domainName + "webService_test.jsp";
	var innerUri = "http://" + webServiceUrl + "/sm/ws/PDACheckStockService";
	var method = "exeCheckStockPackUpload";
	//按2500记录数为单位分批上传
	var pageSize = 2500;
	var loopCount = Math.floor(packCount / pageSize);
	var curUploadSeq = 0;

	//开关，捆包和上传的捆包数量是否一致
	var code_value = getSwitchValue(segNo, 'PDA_AMOUNT_VERIFY');

	//while (curUploadSeq <= loopCount) {
		//console.log(curUploadSeq);
		var currentPackArray = orignPackArray.splice(0, pageSize);
		var curPackCount = currentPackArray.length;
		//alert("第"+(curUploadSeq+1)+"次上传捆包个数"+curPackCount);	
		var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","check_id":"' + checkId  + '","operate_type":"' +
				checkType + '","pack_list":' + JSON.stringify(currentPackArray) + '}';				
		if(code_value == '1'){
			params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","check_id":"' + checkId  + '","packCount":"' + uploadingCount + '","operate_type":"' +
				checkType + '","pack_list":' + JSON.stringify(currentPackArray) + '}';
		}
	
		$.ajax({
			type: "post",
			url: outUri,
			dataType: "json",
			async: false, //只能同步，异步有问题的
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(result) {
				console.log(JSON.stringify(result))
				if (result.resultStatus != "0" && result != "") {
					if (curUploadSeq == loopCount) {
						mui.toast("本次成功上传捆包" + packCount + "个");
						var logTxt = segNo + "-" + userName + "时间" + getCurrentTime() + "正常上传捆包" + packCount + "个";
						writeUploadLogTxt(logTxt); //记录上传日志
						if(code_value == '1'){
							chkPackUpload2(orignPackList, fentry);//文件上传到服务器
						}						
						copyScanedPackTxt(fentry); //文件备份后移除
						uploadingCount = 0;
						uploadedCount = uploadedCount + packCount;
						scanPackList = [];
						recentPackId = "";
						localStorage.setItem("recentPackId", "");
						$("#recentPackId").html("");
						localStorage.setItem("scanPackList", "");
						fillSum();
						uploadButtonClickable(true);
					}
					curUploadSeq++;
				} else { //连接失败
					mui.alert(result.resultDesc, "系统提示", "确定", function() {}, 'div');
					uploadButtonClickable(true);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				mui.alert("服务器连接异常，请稍后再试", "系统提示", "确定", function() {}, "div");
				uploadButtonClickable(true);
			}
		});
	//}
}

function createUpload() {
	var task = plus.uploader.createUpload("http://" + webServiceUrl + "/sm/PDAChk", {
			method: "POST"
		},
		function(t, status) {
			// 上传完成
			if (status == 200) {
				alert("Upload success: " + t.url);
				alert("responseText: " + t.responseText);
			} else {
				alert("Upload failed: " + status);
			}
		});
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(entry) {
			entry.getFile(localFile, {
				create: false
			}, function(fentry) {
				task.addFile(fentry, {
					key: "testdoc"
				});
				alert("1111111111111111111")
				task.start();
				alert("2222222222222222222")
			}, function(e) {
				mui.alert("没有待上传的捆包数据", "系统提示", "确定", function() {}, 'div');
			});
		}, function(e) {
			mui.alert("没有待上传的捆包数据", "系统提示", "确定", function() {}, 'div');
		});
	}, function(e) {
		mui.alert("系统异常", "系统提示", "确定", function() {}, 'div');
	});
}

//已扫描数据文件备份
function copyScanedPackTxt(fentry) {
	var newName = "upload" + getCurrentTime() + ".txt";
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(fileEntry) {
			fentry.moveTo(fileEntry, newName, function(entry) {
				dir = null; //文件移除备份后，将文件目录对象置空
			}, function(e) {
				mui.alert("文件备份失败" + e.message, "系统提示", "确定", function() {}, 'div');
				mui("#upload").button('reset');
				$("#overlay").removeClass("overlay");
			});
		});
	});
}

//记录当前上传日志
function writeUploadLogTxt(logTxt) {
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		// fs.root是根目录操作对象DirectoryEntry
		fs.root.getFile('PDA/pklog.txt', {
			create: true
		}, function(fileEntry) {
			fileEntry.createWriter(function(writer) {
				writer.onerror = function() {
					mui.alert("保存上传日志文件失败", "系统提示", "确定", function() {}, 'div');
				}
				writer.seek(writer.length); //从文件默认追加新的内容
				//var data = segNo+"-"+userName+"时间"+getCurrentTime()+"手动上传捆包"+scanPackList.length+"个";
				if (writer.length == 0) {
					writer.write(logTxt);
				} else {
					writer.write("\n" + logTxt);
				}
			}, function(e) {
				mui.alert("加载上传日志文件对象失败", "系统提示", "确定", function() {}, 'div');
			});
		});
	});
}

//文件上传服务器
function chkPackUpload2(orignPackList, fentry) {
	var orignPackArray = {};
	orignPackArray = eval(orignPackList);
	var packCount = orignPackArray.length;
	var outUri = domainName + "webService_test.jsp";
	var innerUri = "http://" + webServiceUrl + "/sm/ws/PDACheckStockService";
	var method = "uploadFileCommonHdbj2";
	//按2500记录数为单位分批上传
	var pageSize = 2500;
	var loopCount = Math.floor(packCount / pageSize);
	var curUploadSeq = 0;
	var newName = "upload" + getCurrentTime() + ".txt";
	console.log(JSON.stringify(orignPackArray));
	var currentPackArray = orignPackArray.splice(0, pageSize);
	var curPackCount = currentPackArray.length;
	var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","currentTime":"' + getCurrentTime() + '","file_name":"' +
		newName + '","datastr":' + JSON.stringify(currentPackArray) + '}';
	console.log(params);
	$.ajax({
		type: "post",
		url: outUri,
		dataType: "json",
		async: false, //只能同步，异步有问题的
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			console.log(JSON.stringify(result))
			if (result.resultStatus != "0" && result != "") {
				
			} else { //连接失败
				mui.alert(result.resultDesc, "系统提示", "确定", function() {}, 'div');
				uploadButtonClickable(true);
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.alert("服务器连接异常，请稍后再试", "系统提示", "确定", function() {}, "div");
			uploadButtonClickable(true);
		}
	});
}


//删除本地已扫描捆包文件
function deleteScanedPackTxt() {
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
		fs.root.getDirectory(loacalDir, {
			create: false
		}, function(entry) {
			entry.getFile(localFile, {
				create: true
			}, function(fentry) {
				fentry.remove(function(entry) {
					dir = null; //文件移除备份后，将文件目录对象置空
					//重新计算待上传捆包个数
					uploadingCount = uploadingCount - scanPackList.length;
					localStorage.setItem("uploadingCount", uploadingCount);
				}, function(e) {
					mui.alert("文件移除失败" + e.message, "系统提示", "确定", function() {}, 'div');
				});
			}, function(e) {
				mui.alert("已盘捆包本地文件不存在", "系统提示", "确定", function() {}, 'div');
			});
		});
	});
}

//从详情页面返回到当前页面后刷新捆包信息
function refreshPackInfo(productId, packId) {
	scanPackList = JSON.parse(localStorage.getItem("scanPackList"));
	for (var i = 0; i < scanPackList.length; i++) {
		if (scanPackList[i].product_id == productId && scanPackList[i].pack_id == packId) {
			$("#packLocation").val(scanPackList[i].new_pack_location);
			$("#packId").val(packId);
			$("#spec").val(scanPackList[i].spec);
			$("#productId").val(productId);
			$("#weight").val(scanPackList[i].check_weight);
			$("#qty").val(scanPackList[i].check_qty);
			$("#qualityInfo").val(scanPackList[i].quality_info);
			if (scanPackList[i].quality_flg == '0') {
				$("#qualityFlg").html("无异议");
				$("#qualityFlg").removeClass('active').addClass('active2');
			} else {
				$("#qualityFlg").html("有异议");
				$("#qualityFlg").removeClass('active2').addClass('active');
			}
		} else if (scanPackList[i].pack_id == packId) {
			$("#packLocation").val(scanPackList[i].new_pack_location);
			$("#packId").val(packId);
			$("#spec").val(scanPackList[i].spec);
			$("#productId").val(productId);
			$("#weight").val(scanPackList[i].check_weight);
			$("#qty").val(scanPackList[i].check_qty);
			$("#qualityInfo").val(scanPackList[i].quality_info);
			if (scanPackList[i].quality_flg == '0') {
				$("#qualityFlg").html("无异议");
				$("#qualityFlg").removeClass('active').addClass('active2');
			} else {
				$("#qualityFlg").html("有异议");
				$("#qualityFlg").removeClass('active2').addClass('active');
			}
		}
	}
}

/* 
function postBack() {
	mui.plusReady(function() {
		mui.currentWebview.show();
		scanPackList = JSON.parse(localStorage.getItem("scanPackList"));
		var productId = plus.webview.currentWebview().productId;
		var packId = plus.webview.currentWebview().packId;
		//if(productId != null && productId != "") {
			qty = plus.webview.currentWebview().qty;
			weight = plus.webview.currentWebview().weight;
			localStorage.setItem("qty", qty);
			localStorage.setItem("weight", weight); 
			for(var i = 0; i < scanPackList.length; i++) {
				if(scanPackList[i].product_id == productId && scanPackList[i].pack_id == packId) {
					$("#packLocation").val(scanPackList[i].new_pack_location);
					$("#packId").val(packId);
					$("#spec").val(scanPackList[i].spec);
					$("#productId").val(productId);
					$("#weight").val(scanPackList[i].check_weight);
					$("#qty").val(scanPackList[i].check_qty);
					$("#qualityInfo").val(scanPackList[i].quality_info);
					if(scanPackList[i].quality_flg == '0') {
						$("#qualityFlg").html("无异议");
						$("#qualityFlg").removeClass('active').addClass('active2');
					} else {
						$("#qualityFlg").html("有异议");
						$("#qualityFlg").removeClass('active2').addClass('active');
					}
				}else if(scanPackList[i].pack_id == packId){
					$("#packLocation").val(scanPackList[i].new_pack_location);
					$("#packId").val(packId);
					$("#spec").val(scanPackList[i].spec);
					$("#productId").val(productId);
					$("#weight").val(scanPackList[i].check_weight);
					$("#qty").val(scanPackList[i].check_qty);
					$("#qualityInfo").val(scanPackList[i].quality_info);
					if(scanPackList[i].quality_flg == '0') {
						$("#qualityFlg").html("无异议");
						$("#qualityFlg").removeClass('active').addClass('active2');
					} else {
						$("#qualityFlg").html("有异议");
						$("#qualityFlg").removeClass('active2').addClass('active');
					}
				}
			}
		//}
	});
}*/

/*
function upload() {
	scanPackList = localStorage.getItem("scanPackList");
	if(null == scanPackList || scanPackList.length == 0) {
		mui.toast("没有需要上传的数据");
	} else {
		scanPackList = JSON.parse(localStorage.getItem("scanPackList"));
		console.log(JSON.stringify(scanPackList));
		var uploadList = new Array();
		for(var i = 0; i < scanPackList.length; i++) {
			uploadList[i] = '{"pack_id":"' + scanPackList[i].packId + '",' +
				'"product_id":"' + scanPackList[i].productId + '",' +
				'"spec":"' + scanPackList[i].spec + '",' +
				'"manage_no":"' + scanPackList[i].manageNo + '",' +
				'"pack_location":"' + scanPackList[i].packLocation + '",' +
				'"new_pack_location":"' + scanPackList[i].newPackLocation + '",' +
				'"scan_time":"' + scanPackList[i].scanTime + '",' +
				'"quality_flg":"' + scanPackList[i].qualityFlg + '",' +
				'"quality_info":"' + scanPackList[i].qualityInfo + '",' +
				'"check_weight":"' + scanPackList[i].checkWeight + '",' +
				'"check_qty":"' + scanPackList[i].checkQty + '"}';
		}
		if(uploadList.length < 1) {
			mui.toast("没有需要上传的数据");
			return;
		}
		var outUri = domainName + "webService.jsp?callback=?";
		var innerUri = "http://" + webServiceUrl + "/sm/ws/PDACheckStockService";
		var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '","check_id":"' + checkId + '","operate_type":"' + checkType + '","pack_list":[' + uploadList + ']}';
		var method = "exeCheckStockPackUpload";
		//alert(outUri+"\n"+innerUri+"\n"+params);
		$.getJSON(outUri, {
			innerUri: innerUri,
			params: params,
			method: method
		}, function(data) { //如返回对象有一个username属性   
			if(null != data) {
				mui.toast("上传成功");
				uploadingCount = 0;
				uploadedCount = uploadedCount + scanPackList.length;
				localStorage.setItem("scanPackList", "");
				fillSum();
			} else {
				mui.toast("上传失败");
			}
			clearText();
		});
	}
}*/

//页面加载时判断库位开关
function queryConfigPDAVoucherCountMax() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","switch_type":"IF_JUDGE_PACK_LOCATION"}';
	var method = "exeConfigPDAvoucherMaxCount";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			console.log("switch_con:" + data.switch_con);
			if_judge_pack_location = data.switch_con;
		}
	})
};
var location_id = ""; //库位
//判断库位是否存在
function judgeLocationExist() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_location":"' + $('#packLocation').val() + '"}';
	var method = "exeQueryLocation";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			console.log("resultStatus:" + JSON.stringify(data));
			if (data.resultStatus == "1") {
				if (data.locationList.length > 0) {
					$.each(data.locationList, function(i, item) {
						location_id = item.location_id;
						$("#packId")[0].focus(); //捆包输入框获得焦点
					});
				}
			} else {
				mui.alert("库位不存在", "提示", "确认", null, "div");
				$("#packLocation")[0].focus(); //库位输入框获得焦点
				return;
			};
		}
	})
};

// add by penglei 湛江盘库库位查询账号对应库区 **add by tangli 20191126 ERP_58339 湛江物流入库查询账号对应外库库区
function getPacklocation_zj() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + userName + '"}';
	var method = "exeQueryPackLoction_zj";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 2000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if (data != null) {
				if (data.resultStatus == '1') {
					if (data.resultMap != "") {
						pack_location_zj = data.resultMap
						$("#packLocation").val(data.resultMap);
						$("#packId").focus();
						$('#packLocation').attr("disabled", true);
						$("#packLocation").css("background-color", "#CCCCCC");
					}
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			if (textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
};
//end by penglei 湛江盘库库位查询账号对应库区 **end by tangli 20191126 ERP_58339 湛江物流入库查询账号对应外库库区
