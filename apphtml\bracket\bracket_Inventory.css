/** 明细样式 */

.detail_row {
	height: 49px;
	margin-top: 10px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.mui-table-view-cell{
	font-size: 22px;
	padding: 8px 0px 8px 18px;
	text-align: left;
}
/*去除li右边的>*/
.mui-navigate-right:after{
	content: '';
}

#check_id{
	font-size: 22px;
	margin-bottom: 4px;
	padding: 6px 0px 0px 0px;
	color: blue;
}

#check_id span{
	background-color: darkred;
	color: white;
	margin-left: 20px;
	font-size: 16px;
}
#check_id label{
	color: #000000;
	margin-left: 6px;
	font-size: 16px;
}

/*选择盘库单的时候*/
.select:after{
	content: '\e472';
	color: red;
	font-size: 50px;
	font-weight: 600;
	right: 10px;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}

#companyList label {
	font-size: 14px;
}

.icon-search {
	position: absolute;
	left: 250px;
	z-index: 5;
	background-image: url(../../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 5px;
}