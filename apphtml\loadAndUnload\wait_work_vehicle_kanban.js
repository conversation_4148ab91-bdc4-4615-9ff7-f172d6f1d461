var totalWeight = "0";
//页面加载时查询车辆指引
window.onload = function onload() {
	mui.plusReady(function() {
		queryVehicleNoWork();
		queryVehicleWaitWork();
	});
}

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//排队看板查询
function queryVehicleNoWork() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService'; //PDABoardVehicleService
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryVehicleNoWork";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			//先清空当前列表中的牌号信息
			var lihtml = "";
			$("#vehicleSortList").html(lihtml);
			if(data.resultDesc.length > 0) {
				$.each(data.resultDesc, function(i, item) {
					totalWeight = "0";
					if(null != item.total_weight) {
						totalWeight = item.total_weight
					}
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<div style="width: 270px;float: left;text-align: start;font-size:20px;color: red;">' +
						item.vehicle_no + '/' + item.check_type + '/' + totalWeight +
						'吨</div>' +
						'<div style="width: 270px;float: left;text-align: start;font-size:18px;color: red;">' +
						'所有装卸点：' + item.all_hand_point_name +
						'</div>' +
						'</li>';
				});
				$("#vehicleSortList").html(lihtml);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//排队看板查询
function queryVehicleWaitWork() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService'; //PDABoardVehicleService
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryVehicleWaitWork";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			//先清空当前列表中的牌号信息
			var lihtml = "";
			$("#vehicleWaitList").html(lihtml);
			if(data.resultDesc.length > 0) {
				$.each(data.resultDesc, function(i, item) {
					totalWeight = "0";
					if(null != item.total_weight) {
						totalWeight = item.total_weight
					}
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<div style="width: 270px;float: left;text-align: start;font-size:20px;color:#FFA500;">' +
						item.vehicle_no + '/' + item.check_type + '/' + totalWeight +
						'吨</div>' +
						'<div style="width: 270px;float: left;text-align: start;font-size:18px;color:#FFA500;">' +
						'待装卸点：' + item.all_hand_point_name +
						'</div>' +
						'</li>';
				});
				$("#vehicleWaitList").html(lihtml);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}