/**
 * 初始化变量信息 
 */
var putinPackList = new Array();//入库捆包列表
var vehicle_id = "";//车牌号
var putinScanPage = "";
var packListPage ="";

var putinScanTransferPage = "";
var transfer_flag = "";   //转库标记

$(function(){
	mui.init({
		swipeBack:false //启用右滑关闭功能
	});
	
	(function($) {
		//修改、删除事件
		$('#pack_putin_list').on('tap', '.mui-btn.mui-btn-blue', function(event) {
			var elem = this;
			var li = elem.parentNode.parentNode;//向上找两个父节点正好对应li节点
			var li_J = getJQueryObject(li);
			mui.confirm('确认修改该条记录？', '提示', ['确认','取消'], function(e) {
				if (e.index == 0) {
					
					//by tangli add ERP_51191  start 
					//关闭当前页面(入库捆包清单页面)
					var ws=plus.webview.currentWebview();
					plus.webview.close(ws);
					//by tangli add ERP_51191  end 
					
					//返回捆包扫描页面编辑捆包
					editPackById(li_J.find("#pack_id").text());
				} else {
					setTimeout(function() {
						$.swipeoutClose(li);
					}, 0);
				}
			},'div');
		});
		
		$('#pack_putin_list').on('tap', '.mui-btn.mui-btn-red', function(event) {
			var elem = this;
			var li = elem.parentNode.parentNode;
			var li_J = getJQueryObject(li);
			mui.confirm('确认删除该条记录？', '警告', ['确认','取消'], function(e) {
				if (e.index == 0) {
					deletePackById(li_J.find("#pack_id").text());
					//删除捆包
					li.parentNode.removeChild(li);
				} else {
					setTimeout(function() {
						$.swipeoutClose(li);
					}, 0);
				}
			},'div');
		});
	})(mui);
});

window.onload = function onload(){
	mui.plusReady(function(){
		putinPackList = plus.webview.currentWebview().putinPackList;
		vehicle_id = plus.webview.currentWebview().vehicle_id;
		transfer_flag = plus.webview.currentWebview().transfer_flag;
		console.log("transfer_flag:" + transfer_flag);
		if (transfer_flag == '1') {
			$(".mui-title").html("转库捆包清单");
		}
		putinScanPage = plus.webview.getWebviewById("putin_scan");
		packListPage = plus.webview.getWebviewById("putin_pack_list");
		putinScanTransferPage = plus.webview.getWebviewById("putin_scan_transfer");
		//添加捆包信息到ul中
		showPackList(putinPackList);
	});
}

mui(document.body).on('tap','#back',function(){
	mui.back();
});

mui.back = function(){
	console.log("transfer_flag:" + transfer_flag);
	if (transfer_flag == "1") { //判断转库标记 如果标记为1，返回转库捆包扫描页面
		mui.fire(putinScanTransferPage,'back',{
			putinPackList: putinPackList,
			vehicle_id : vehicle_id
		});
		//putinScanTransferPage.show();
	} else {  //否则返回普通入库捆包扫描页面
		mui.fire(putinScanPage,'back',{
			putinPackList: putinPackList,
			vehicle_id : vehicle_id
		});
		putinScanPage.show();
	}
	//var packListPage = plus.webview.getWebviewById("putin_pack_list");
	packListPage.close();
}

function showPackList(packList){
	//console.log(JSON.stringify(packList));
	$.each(packList, function(i,value) {
		$("#pack_putin_list").append('<li class="mui-table-view-cell"><div class="mui-slider-right mui-disabled"><a class="mui-btn mui-btn-blue mui-icon">修改</a><a class="mui-btn mui-btn-red mui-icon">删除</a></div><div class="mui-slider-handle"><div><div id="pack_id">'
		+ value.pack_id +
		'</div><div><div id="factory_product_id" class="left"><span>钢</span><label>'
		+ value.factory_product_id +
		'</label></div><div id="spec"><span>规</span><label>'
		+ value.spec +
		'</label></div></div></div><div><div style="clear: both;"></div><div class="left" id="pack_location"><span>库</span><label>'
		+ value.location_desc +
		'</label></div><div id="weight"><span>重</span><label>'
		+ value.putin_weight +
		'</label></div></div></div></li>');
	});
}

function deletePackById(pack_id){			 
	var index =getIndexById(pack_id,putinPackList);
	console.log("index====>" + index);
	if(index == -1){
		mui.alert("没有找到可以删除的捆包","提示","确定",function() {}, "div");
		return false;
	}else{
		putinPackList.splice(index,1);
		console.log("已经调用删除方法");
		console.log(putinPackList.length);
	}
}

function editPackById(pack_id){
	var index =getIndexById(pack_id,putinPackList);							 
	//var page = plus.webview.getWebviewById('putin_scan');
	/*mui.openWindow({
		id:'putin_scan'
	});*/
	//var putinScanPage = plus.webview.getWebviewById("putin_scan");
	
	console.log("转库扫描标记：" + transfer_flag);
	//var putinScanTransferPage1 = plus.webview.getWebviewById("putin_scan_transfer");
	if (transfer_flag == "1") {
		console.log("开始");
		mui.fire(putinScanTransferPage,'edit',{
			putinPackList: putinPackList,
			edit_pack: putinPackList[index],
			edit_flag : 1,
			type:"1"
		});
		putinScanTransferPage.show();
		//putinScanTransferPage1.show();
		//console.log(putinScanTransferPage1.isVisible());
		//.loadURL("putin_scan_transfer.html");
		console.log("结束");
	} else {
		mui.fire(putinScanPage,'edit',{
			putinPackList: putinPackList,
			edit_pack: putinPackList[index],
			vehicle_id : vehicle_id,
			type:"1"
		});
		putinScanPage.show();
		packListPage.close();
	}
	//page.show();
	//var packListPage = plus.webview.getWebviewById("putin_pack_list");
	
}