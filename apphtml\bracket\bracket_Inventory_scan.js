/**
 * 初始化变量信息 
 */
var querySteelSupportCheckDetailList= new Array();//单据号列表详情
var downLoadList= new Array();//已下载列表

var check_id = "";//盘库单号
var download_count = 0; //已下载托架数量	
var unDownload_count = 0; //待上传托架数量
var steel_support_type = 0; //托架类型
var flag = false; //校验铁托架号是否存在系统中


mui.ready(function(){
	$("#steel_support_id").focus();
});

$(function(){
	mui.init({
		swipeBack:true//启用右滑关闭功能
	});
});

//返回按钮事件
mui(document.body).on('tap','#back',function(){
	mui.back();
});     
mui.back = function () {
	if(downLoadList.length > 0){
			var btnArray = ['退出', '取消'];
			mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			   if(e.index==0){
			     setTimeout(function() {
				  var ws=plus.webview.currentWebview();
				  plus.webview.close(ws)
				}, 0);
			 }
		},'div');
	}else{
		setTimeout(function() {
				  var ws=plus.webview.currentWebview();
				  plus.webview.close(ws)
		}, 0);
	}
};


window.onload = function onload(){
	mui.plusReady(function(){
		open_from_url = plus.webview.currentWebview().open_from_url;
		open_from_id = plus.webview.currentWebview().open_from_id;
		querySteelSupportCheckDetailList = plus.webview.currentWebview().querySteelSupportCheckDetailList;
		check_id = plus.webview.currentWebview().check_id;
		
		//设置已下载数量 与 待上传数量
		download_count = querySteelSupportCheckDetailList.length;
		unDownload_count = querySteelSupportCheckDetailList.length;
		$("#download").html(download_count);
		$("#un_download").html(download_count);
	});
}

//铁托架号
$("#steel_support_id").keypress(function(e){
	if(e.keyCode == 13){
		var steel_support_id = $("#steel_support_id").val();
		 if(steel_support_id==null || steel_support_id==""){
		 	mui.alert("请扫描或者输入铁托架号","提示","确定",null,'div');
		 	$("#steel_support_id").focus();
		 	return;
		 }else{
		 /**
		  * 匹配该单据下的铁托架号
		  */
		 	mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
					&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
					 queryList(steel_support_id);
				  }else{
					plus.nativeUI.toast("无网络连接，请检查网络后再次上传");
				}
		  });
		 }
	}
});


//铁托架编号
$("#steel_support_num").keypress(function(e){
	if(e.keyCode == 13){
		var steel_support_num = $("#steel_support_num").val();
		 if(steel_support_num==null || steel_support_num==""){
		 	mui.alert("请扫描或者输入铁托架编号","提示","确定",null,'div');
		 	$("#steel_support_num").focus();
		 	return;
		 }else{
		 //匹配该单据下的铁托架号
		 	mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
					&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
					 queryList2(steel_support_num);
				  }else{
					plus.nativeUI.toast("无网络连接，请检查网络后再次上传");
				}
		  });
		 }
	}
});



//按钮 修改 事件
mui(document.body).on('tap','#update_button',function(){

	var steel_support_id = $("#steel_support_id").val();
	if(steel_support_id==null || steel_support_id==""){
	 	mui.alert("请扫描或者输入铁托架号","提示","确定",null,'div');
	 	$("#steel_support_id").focus();
	 	return;
	}
	
	var steel_support_num = $("#steel_support_num").val();
	if(steel_support_num==null || steel_support_num==""){
	 	mui.alert("请扫描或者输入铁托架编号","提示","确定",null,'div');
	 	$("#steel_support_num").focus();
	 	return;
	}
	var steel_support_name = $("#steel_support_name").val();
	if(steel_support_name==null || steel_support_name==""){
	 	mui.alert("请扫描或者输入铁托架名称","提示","确定",null,'div');
	 	$("#steel_support_name").focus();
	 	return;
	}
	var steel_support_type = $("#steel_support_type").val();
	if(steel_support_type==null || steel_support_type=="0"){
	 	mui.alert("请扫描或者输入铁托架类型","提示","确定",null,'div');
	 	$("#steel_support_type").focus();
	 	return;
	}
	
	//查询铁托架号是否存在
	querySteelSupportList(steel_support_id);
});

//按钮 保存 事件
mui(document.body).on('tap','#comfir_button',function(){
	
	if(download_count == 0){
		mui.alert("下载铁托架列表为空，不能保存","提示","确定",null,'div');
		$("#steel_support_id").focus();
		return;
	}
	
	//已下载数量与盘库单数量比较
	if(download_count < querySteelSupportCheckDetailList.length){
	 	mui.confirm("待上传列表还存在未扫描铁托架,确认保存？","提示",['取消','确定'],function(e){
	 		if(e.index != 1){
	 			$("#steel_support_id").focus();
				return;
				
	 		}else{
	 			mui("#comfir_button").button('loading');
				$("#overlay").addClass("overlay");
				
				/**
				 * 调用 保存 接口方法
				 */
				comfirSteelSupportList();
	 		}
	 		
	 	},'div');
	}else{
		mui("#comfir_button").button('loading');
		$("#overlay").addClass("overlay");
		
		/**
		 * 调用 保存 接口方法
		 */
		comfirSteelSupportList();
	}
});


/*方法*/
//匹配扫描铁托架号
function queryList(steel_support_id) {
	var flag = 0;
	
	$.each(querySteelSupportCheckDetailList, function(i, item) {
		if(item.steel_support_id == steel_support_id){
			flag = 1;
			//检查是否重复扫描
			if(getIndexBySteelSupportId(steel_support_id,downLoadList) == -1){
				//将铁托架信息放置于捆包中
				
					var object = {
						check_id:item.check_id,
						steel_support_id:item.steel_support_id,
						steel_support_num:	item.steel_support_num,
						steel_support_name:	item.steel_support_name,
						steel_support_type:	item.steel_support_type
					}
					downLoadList.push(object);
					
					//更新页面数量
					updateDownloadNumber();
					
					//清空铁托架信息
					$("#steel_support_id").val("");
					$("#steel_support_id").focus();
			}else{
				mui.alert("该铁托架已扫描，不能重复扫描！","提示","确定",null,'div');
				return;
			}
		}
	});
	
	if(flag == 0){
		mui.alert("该铁托架不存在,请手动填写信息并修改！","提示","确定",null,'div');
		return;
	}
}


//匹配扫描铁托架编号
function queryList2(steel_support_num) {
	var flag = 0;
	
	$.each(querySteelSupportCheckDetailList, function(i, item) {
		if(item.steel_support_num == steel_support_num){
			flag = 1;
			//检查是否重复扫描
			if(getIndexBySteelSupportNum(steel_support_num,downLoadList) == -1){
				//将铁托架信息放置于捆包中
					var object = {
						check_id:item.check_id,
						steel_support_id:item.steel_support_id,
						steel_support_num:	item.steel_support_num,
						steel_support_name:	item.steel_support_name,
						steel_support_type:	item.steel_support_type
					}
					downLoadList.push(object);
					
					//更新页面数量
					updateDownloadNumber();
					
					//清空铁托架信息
					$("#steel_support_num").val("");
					$("#steel_support_num").focus();
			}else{
				mui.alert("该铁托架已扫描，不能重复扫描！","提示","确定",null,'div');
				return;
			}
		}
	});
	
	if(flag == 0){
		mui.alert("该铁托架不存在,请手动填写信息并修改！","提示","确定",null,'div');
		return;
	}
}


/**
 * 保存 方法
 */
function comfirSteelSupportList() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+account+'","check_id":"'+check_id+'","downLoad_list":'+JSON.stringify(downLoadList)+'}';
	console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "comfirSteelSupportList";
	
	var outtime = 30000;
	if(querySteelSupportCheckDetailList.length > 50 && querySteelSupportCheckDetailList.length <=100){
		outtime = 60000;
	}else if(querySteelSupportCheckDetailList.length > 100){
		outtime = 120000;
	}
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:outtime,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
					//去除页面遮罩层
					mui("#comfir_button").button('reset');
					$("#overlay").removeClass("overlay");
				if(data.resultStatus == "1"){
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					
					//成功之后
/*					downLoadList.clear;
					$("#un_download").html("0");
					$("#download").html("0");
					download_count = 0; //已下载托架数量
					unDownload_count = 0; //待上传托架数量*/
				}else{
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
				
			}else { //连接失败
				//去除页面遮罩层
				mui("#comfir_button").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
 			//去除页面遮罩层
			mui("#comfir_button").button('reset');
			$("#overlay").removeClass("overlay");
	  	 }
		}
	});
}

//查询铁托架号 是否存在
function querySteelSupportList(steel_support_id) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var steel_support_status ="";
	var params = '{"seg_no":"' + segNo + '","steel_support_id":"' + steel_support_id + '","steel_support_status":"'+steel_support_status+'"}';
	var method = "querySteelSupportInfo";
	params = encodeURI(params,'utf-8');
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:2000,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
				if(data.resultStatus == "1"){
					//铁托架号存在
					
					//将铁托架信息放置于捆包中
					if(getIndexBySteelSupportId(steel_support_id,downLoadList) == -1){
						var object = {
							check_id:check_id,
							steel_support_id:steel_support_id,
							steel_support_num:	$("#steel_support_num").val(),
							steel_support_name:	$("#steel_support_name").val(),
							steel_support_type:	$("#steel_support_type").val()
						}
						downLoadList.push(object);
						
						//更新页面数量
						//updateDownloadNumber();
					}else{
						mui.alert("该铁托架已扫描，不能重复扫描！","提示","确定",null,'div');
						return;
					}
					//清空铁托架信息
					$("#steel_support_id").val("");
					$("#steel_support_num").val("");
					$("#steel_support_name").val("");
					$("#steel_support_type").val("0");
					$("#steel_support_id").focus();
				}else{
					mui.alert("铁托架号不存在系统中，请去铁托架管理页面维护铁托架!","提示","确定",null,'div');
				 	return;
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定",null,'div');
				return;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
	  	 }
		}
	});
}


function steel_support_type_change() {
	steel_support_type = $("#steel_support_type option:selected").val();
}

function getIndexBySteelSupportId(steel_support_id,list){
	var index = -1;
	$.each(list, function(i,value) {
		if(value.steel_support_id == steel_support_id){
			index = i;
			return false;
		}
	});
	return index;
}

function getIndexBySteelSupportNum(steel_support_num,list){
	var index = -1;
	$.each(list, function(i,value) {
		if(value.steel_support_num == steel_support_num){
			index = i;
			return false;
		}
	});
	return index;
}

function updateDownloadNumber(){ 
	
/*	//更新 已下载 数量
	download_count += 1;
	$("#download").html(download_count);*/
	
	//待上传数量校验
	if(unDownload_count > 0){
		unDownload_count -= 1;
		//更新 待上传 数量
		$("#un_download").html(unDownload_count);
	}
	
}
