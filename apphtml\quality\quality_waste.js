/**
 * 初始化变量信息 
 */
var product_id = "";
$(function(){
	mui.init({
		swipeBack:true //启用右滑关闭功能
	});
});

mui.plusReady(function(){
	$("#pack_id").focus();
});
	
//捆包号
$("#pack_id").keypress(function(e){
	if(e.keyCode == 13){
		var pack_id = $("#pack_id").val();
		//add by xuhuaijun 20161229 格式化捆包号
		pack_id = formatPackId(pack_id);
		$("#pack_id").val(pack_id);
		 //return false;
		 if(pack_id==null || pack_id==""){
		 	mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
		 	$("#pack_id").focus();
		 	return;
		 }
		 queryQualityRejudgePack(pack_id);
	}
});

//按钮绑定事件
mui(document.body).on('tap','#block_button',function(){
	var pack_id = $("#pack_id").val();
	if(pack_id==null || pack_id==""){
		mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
		$("#pack_id").focus();
		return;
	}

	mui("#block_button").button('loading');
	$("#overlay").addClass("overlay");
	exePackQualityRejudgeUpload();
});


//捆包扫描方法
function queryQualityRejudgePack(pack_id) {
	<!-- 查询前先关闭软键盘-->
	//console.log("packQualityBlock1111>>>>>>>>>>>>>>>>");
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '"}';
	var method = "exeQueryRejudgePackInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.packList.length == 0){
				 mui.alert("未查询到对应的封闭捆包信息", "提示", "确定", function() {
				 	$("#pack_id").val("");
				 	$("#pack_block_id").val("");
				 	$("#pack_id").focus();
				}, 'div');
				return ;
			}else{
				product_id = data.packList[0].product_id;
				var pack_block_id =  data.packList[0].pack_block_id;
				var shopsign = data.packList[0].shopsign;
				$("#pack_block_id").val(pack_block_id);
				$("#new_shopsign").val(shopsign);
				$("#quality_desc").focus();
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {
			}, 'div');
			return ;
		}
	});
}

//执行捆包封闭
function exePackQualityRejudgeUpload() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var pack_id = $("#pack_id").val();
//	var quality_desc = $("#quality_desc").val();
	$("#quality_desc").val($("#quality_desc").val().replace(/[\r\n]/g,""));//去掉回车换行
	var quality_desc = $("#quality_desc").val();
	var quality_grade = $("#new_quality_grade").val();
	var shopsign = $("#new_shopsign").val();
	var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","pack_id":"'+pack_id+'","remark":"'+quality_desc+'","new_quality_grade":"6","new_shopsign":"'+shopsign+'"}';
	params= encodeURI(params,'utf-8');
	var method = "exeRejudgePack";
	console.info("params:"+params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			mui("#block_button").button('reset');
			$("#overlay").removeClass("overlay");
			if(data.resultStatus == "1") {
				  mui.alert("判废成功", "提示", "确定", function() {
					  $("#pack_id").val("");
					  $("#new_shopsign").val("");
					  $("#pack_block_id").val("");
					  $("#quality_desc").val("");
					  product_id = "";
				      $("#pack_id").focus();
				}, 'div');
				return ;
			} else {
				var errorinfo = "";
				if(data.resultStatus != "0"){
					errorinfo = data.resultStatus;
				}else{
					errorinfo = data.resultDesc;
				}
				 mui.alert("判废失败！原因："+errorinfo, "提示", "确定", function() {
					  $("#pack_id").val("");
					  $("#quality_desc").val("");
					  $("#pack_block_id").val("");
					  product_id = "";
				      $("#pack_id").focus();
				}, 'div');
				 return ;
			}
		} else { //连接失败
			mui("#block_button").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("连接服务器异常", "提示", "确定", function() {
			}, 'div');
			return ;
		}
	});
}