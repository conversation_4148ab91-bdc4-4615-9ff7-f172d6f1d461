/** 按钮样式 */

#edit {
	width: 41%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 4px 0px 0px;
}

#putout,
#opeNext {
	width: 100%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 0px 0px 2px;
}


/** 明细样式 */

.detail_row {
	height: 43px;
}

.mui-input-row span {
	top: 10px!important;
}

.text {
	float: left;
	width: 30%;
	font-size: 15px;
	padding: 10px 0px;
	text-align: center;
}

#steel_support_id,
#steel_support_num,
#steel_support_name,
#steel_support_type{
	width: 70%;
	padding: 0px 5px;
	font-size: 20px;
}


/** 合计框样式 */

.sum {
/*	border: solid 1px #CBCBCB;
	border-radius: 5px;*/
	font-size: 22px;
	margin-top: 10px;
	margin-bottom: 10px;
}
.sum .text{
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	padding-left: 4px;
	text-align: left;
}

.sum label{
	float: left;
	width: 68%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: right;
}

.voucher-icon {
	float: left;
	padding: 4px;
	margin: 2px 6px 0px 6px;
	background-color: blue;
	color: white;
	width: 30px;
	height: 30px;
}

.icon {
	/*float: left;*/
	margin: 2px 6px;
	padding: 4px 4px;
	background-color: blue;
	color: white;
	width: 30px;
	height: 30px;
}

.icon_w {
	/*float: left;*/
	margin: 2px 6px;
	padding: 4px 4px;
	background-color: red;
	color: white;
	width: 30px;
	height: 30px;
}

.icon-detail {
	margin: 4px 0px;
	padding: 6px 0px 0px 0px;
	height: 30px;
}

#scan_weight,
#scan_qty {
	float: left;
	width: 70px;
	text-align: right;
	color: green;
}

#wscan_weight,
#wscan_qty {
	float: left;
	width: 70px;
	text-align: right;
	color: red;
}

#ready_weight,
#ready_qty {
	text-align: right;
	color: purple;
}

.seperator {
	margin-left: 20px;
	float: left;
	left: 50%;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}


/** 弹出图层设置 */

#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

#car_no {
	width: 100%;
}

.vehicle_no {
	color: blue;
	font-size: 22px;
}

.vehicle_status_desc {
	color: #000000;
	font-size: 16px;
	margin-left: 10px;
}

.carNo_bangcolor {
	background-color: #ACACB4;
}
