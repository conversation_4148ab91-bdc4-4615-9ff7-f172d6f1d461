/**
 * 变量定义
 */
var machineId = "";//机组代码
var machineName = "";//机组名称
var seg_no = localStorage.getItem("segNo");
var umcLoginName = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");

mui.init({
	swipeBack: true //启用右滑关闭功能
});

$(function() {
	//调用webservices接口
	toWebServices();
	
});

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	machineId = el_J.find("#machineId").text();
	machineName = el_J.find("#machineName").text();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	//判断是否选择仓库
	/*if(null == machineId || "" == machineId) {
		mui.alert("请选择机组", " ", "确定", function() {}, 'div');
		return false;
	}*/
	localStorage.setItem("machineId", machineId);
	localStorage.setItem("machineName", machineName);
	localStorage.getItem("name"); 
	localStorage.getItem("segNo");
	var self = plus.webview.currentWebview();
	
	console.log(self.openType)
	var a = 1 ;

	var popwrovider = self.openType;
	console.log(popwrovider);
	if("popwrovider" == popwrovider) {
		mui.openWindow({
			url: 'index.html',
			id: 'index',
			createNew: true
		});
	}else if("popHandPoint"==popwrovider){
		mui.openWindow({
			url:'../loadAndUnload/select_hand_point.html',
			id:'select_hand_point', 
			createNew:true
		});
	}else if(a == 1){
		mui.openWindow({
			url:'produce_menus.html',
			id:'produce_menus', 
			createNew:true
		});
	}else{
		//智慧仓库，入库前必须 选择车牌号
		console.log("startfrom>>>>>>>>>>>>"+startfrom);
		if(startfrom == 'menu'){
			console.log("putin_scan>>>>>>>>>>>>>>>>>>>>" + startfrom);
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，将进入入库选择页面
			if (putin_method_switch == "1" && seg_no == '00113') {
				mui.openWindow({
					url:'../putin/putin_method_menus.html',
					id:'putin_method_menus',
					createNew:true
		    	});
			} else if(auto_loc_flag == '1' && location_type > '0') {
				mui.openWindow({
					url: '../loadAndUnload/select_vehicle_no.html',
					id: 'select_vehicle_no',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					//extras:{
					//wprovider_id: wprovider_id,  //扩展参数
					//wprovider_name: wprovider_name,  //扩展参数
					//},
					createNew: true
				});
			}
		}else if(startfrom == 'synergy'){
			console.log("synergy>>>>>>>>>>>>>>>>>>>>" + startfrom);
				mui.openWindow({
					url:'select_vehicle_no.html',
					id:'select_vehicle_no',
					extras:{
						open:'synergy',  //扩展参数
					},
					createNew:true
				});
		}else{
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，进入转库扫描页面
			if (putin_method_switch == "1" && seg_no == '00113' && transfer_flag == 1) {
				mui.openWindow({
					url:'../putin/putin_scan_transfer.html',
					id:'putin_scan_transfer',
					createNew:true
		    	});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					createNew: true
				});
			}
		}
	}
	localStorage.getItem("name"); 
	localStorage.getItem("segNo");
	var self = plus.webview.currentWebview();

	var popwrovider = self.openType;
	console.log(popwrovider);
	if("popwrovider" == popwrovider) {
		mui.openWindow({
			url: 'index.html',
			id: 'index',
			createNew: true
		});
	}else if("popHandPoint"==popwrovider){
		mui.openWindow({
			url:'../loadAndUnload/select_hand_point.html',
			id:'select_hand_point', 
			createNew:true
		});
	}else{
		//智慧仓库，入库前必须 选择车牌号
		console.log("startfrom>>>>>>>>>>>>"+startfrom);
		if(startfrom == 'menu'){
			console.log("putin_scan>>>>>>>>>>>>>>>>>>>>" + startfrom);
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，将进入入库选择页面
			if (putin_method_switch == "1" && seg_no == '00113') {
				mui.openWindow({
					url:'../putin/putin_method_menus.html',
					id:'putin_method_menus',
					createNew:true
		    	});
			} else if(auto_loc_flag == '1' && location_type > '0') {
				mui.openWindow({
					url: '../loadAndUnload/select_vehicle_no.html',
					id: 'select_vehicle_no',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					//extras:{
					//wprovider_id: wprovider_id,  //扩展参数
					//wprovider_name: wprovider_name,  //扩展参数
					//},
					createNew: true
				});
			}
		}else if(startfrom == 'synergy'){
			console.log("synergy>>>>>>>>>>>>>>>>>>>>" + startfrom);
				mui.openWindow({
					url:'select_vehicle_no.html',
					id:'select_vehicle_no',
					extras:{
						open:'synergy',  //扩展参数
					},
					createNew:true
				});
		}else{
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，进入转库扫描页面
			if (putin_method_switch == "1" && seg_no == '00113' && transfer_flag == 1) {
				mui.openWindow({
					url:'../putin/putin_scan_transfer.html',
					id:'putin_scan_transfer',
					createNew:true
		    	});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					createNew: true
				});
			}
		}
	}
		
	mui.openWindow({
		url: 'produce_menus.html',
		id: 'produce_menus',
		createNew: true
	});	
});

function toWebServices() {
	    var segNo = seg_no;//localStorage.getItem("segNo"); //采用localStorage存储数据
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = { 
			segNo: segNo , 
			umcLoginName: umcLoginName 
		};
		params = JSON.stringify(params);
		var method = "queryMachineInfo";
		console.log("params" + params);
		$.ajax({  
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method, 
				targetnamespace: targetnamespace
			},
			dataType: "json", 
			success: function(data) { 				
				console.log(JSON.stringify(data));				
				if("1" == data.returnValue) { //查询成功 
						console.log(data.prbookSettingList.length);	
						if(data.prbookSettingList.length > 0) {
						var chtml = "";
						$.each(data.prbookSettingList, function(i, item) {
						chtml = chtml + '<li class="mui-table-view-cell">' +
								'<a class="mui-navigate-right">' +
								'<div>' +
								'<label id="machineId">'+item.machineId+'</label>' +
								'</div>' +
								'<div>' +
								'<label id="machineName">'+item.machineName+'</label>' +
								'</div>' +
								'</a>' +
								'</li>';
						});
						$("#companyList").html(chtml);
					} 
				} else{
					alert(data.returnDesc);
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
}