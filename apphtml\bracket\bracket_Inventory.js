/**
 * 初始化变量信息 
 */
var steelSupportList= new Array();//单据号列表

$(function(){
	mui.init({
		swipeBack:true //启用右滑关闭功能
	});
});

window.onload = function onload(){
	mui.plusReady(function(){
		var voucher_id = $("#voucher_id").val();
		if(voucher_id == null){
			voucher_id = "";
		}
		/**
		  * 调用查询该单据号是否存在的方法
		  */
		 	mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
					&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
					 queryVoucherInfo(voucher_id);
				  }else{
					plus.nativeUI.toast("无网络连接，请检查网络后再次上传");
				}
		  });
	});
}

mui.ready(function(){
	$("#voucher_id").focus();
});

//点击搜索图标
mui(document.body).on("tap", ".icon-search", function() {
	var voucher_id = $("#voucher_id").val();
	if(voucher_id == null){
		voucher_id = "";
	}
	/**
	  * 调用查询该单据号是否存在的方法
	  */
	 	mui.plusReady(function() {
			var curNetConnetType = plus.networkinfo.getCurrentType();
			if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
				&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
				 queryVoucherInfo(voucher_id);
			  }else{
					plus.nativeUI.toast("无网络连接，请检查网络后再次上传");
				}
	  });	
});

//按钮铁托架下载事件
mui(document.body).on("tap","#download_button",function(){
	//得到当前选中的盘库单单据号
	var voucher_id = $("#companyList .select").children().children().attr("data");
	
	if(voucher_id != null && voucher_id != ""){
		mui("#download_button").button("loading");
		$("#overlay").addClass("overlay");
		queryVoucherDetailInfo(voucher_id);
	}else{
		mui.alert("盘库单号不能为空", "提示", "确定", function() {}, 'div');
		return ;
	}
});

//点击单据号列事件
mui(document.body).on("tap","li",function(){

	var a = $(this).children('a');
	if(a.hasClass("select")==false){//选择
		
		//只能选择一个盘库单，将所有选中的盘库单全部取消
		var a_list = $("#companyList a");
		a_list.removeClass("select"); 
		
		a.addClass("select");
	}else if(a.hasClass("select")==true){//取消
		a.removeClass("select");
	}
});



/**
 * 查询单据号信息
 */
function queryVoucherInfo(voucherId) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+account+'","voucherId":"'+voucherId+'"}';
	console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "queryVoucherInfo";
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:2000,
		data: {
		    innerUri:innerUri, 
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
				console.log(data);
				if(data.resultStatus == "1"){
					steelSupportList = data.resultList;
					//显示盘库单号
					initData(steelSupportList);
					//默认勾选中第一条记录
					$("#companyList a").first().addClass("select");
					
				}else{
					mui.alert("未查询到对应的盘库单信息", "提示", "确定", function() {}, 'div');
				}
			} else {//连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
	  	 }
		}
	});
}

//下载铁托架信息
function queryVoucherDetailInfo(voucherId) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+account+'","voucherId":"'+voucherId+'"}';
	//console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "queryVoucherDetailInfo";
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:5000,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
				if(data.resultStatus == "1"){
					
					mui("#download_button").button("reset");
					$("#overlay").removeClass("overlay");


					//跳转到新页面
					mui.openWindow({
						url: 'bracket_Inventory_scan.html',
						id: 'bracket_Inventory_scan',
						createNew: true,
						extras: {
							querySteelSupportCheckDetailList: data.resultList,
							check_id:voucherId
						}
					});
					
					
					//清空列表信息
					$("#companyList li").remove();
					
				}else{
					mui.alert("未查询到对应的铁托架信息", "提示", "确定", function() {}, 'div');
					return;
				}
			} else {//连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
	  	 }
		}
	});
}


function initData(resultList){
	$("#companyList li").remove();
	var li = "";
	$.each(resultList, function(i, item) {
		console.log("initdata>>>>>>>>>>"+JSON.stringify(item));
		  	li = li +'<li class="mui-table-view-cell">'+
										'<a class="mui-navigate-right">'+
										'<div>'+
											'<div id="check_id"  data="'+item.check_id+'">'+item.check_id+'</div>'+
										'</div>'+
										'</a>'+
									'</li>';
	});
	$("#companyList").html(li);
}

function getIndexByCheckId(checkId,checkList){
	var index = -1;
	$.each(checkList, function(i,value) {
		if(value.check_id == checkId){
			index = i;
			return false;
		}
	});
	return index;
}
