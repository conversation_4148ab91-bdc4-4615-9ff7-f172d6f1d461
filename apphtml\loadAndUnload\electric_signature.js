var domainName = "http://ksh.baointl.com/pdaService/";
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var vehicle_id = localStorage.getItem("vehicle_id");
var car_trace_no = localStorage.getItem("car_trace_no");
var dataDriver = []; //司机签名
var dataTrans = []; //物流人签名

/*var unUploadPack=localStorage.setItem("unUploadPack",unUploadPack);
var uploadPack=localStorage.setItem("uploadPack",uploadPack);
var packInfo=localStorage.setItem("packInfo",packInfo);
var contractSubInfo=localStorage.setItem("contractSubInfo",contractSubInfo);
var last_html=localStorage.setItem("last_html","putout_scan");
var operatorType=localStorage.setItem("operatorType",operatorType);
var putout_confirm_flag=localStorage.setItem("putout_confirm_flag",putout_confirm_flag);*/

var unUploadPack = localStorage.getItem("unUploadPack");
// var uploadPack = new Map(); //已上传捆包信息  Map<voucher_id,packList>
// var uploadPack = {}; 
var uploadPack=localStorage.getItem("uploadPack");
var packInfo = localStorage.getItem("packInfo");
var contractSubInfo = localStorage.getItem("contractSubInfo");
var last_html = localStorage.getItem("last_html");
var operatorType = localStorage.getItem("operatorType");
var putout_confirm_flag = localStorage.getItem("putout_confirm_flag");
//var putoutVoucherList = localStorage.getItem("putoutVoucherList");
var putoutVoucherList = new Array(); //出库单据列表
var team_id = localStorage.getItem("team_id"); //班组代码
var work_shift = localStorage.getItem("class_id"); //班次代码
var signatureCount = "0";
// add by gll
var type ="";
var desc ="";
var pack_id ="";
// end by gll
window.onload = function onload() {
	mui.plusReady(function() {
		//					unUploadPack['map'] = plus.webview.currentWebview().unUploadPack['map'];
		//					uploadPack['map'] = plus.webview.currentWebview().uploadPack['map'];
		//					packInfo['map'] = plus.webview.currentWebview().packInfo['map'];
		//					contractSubInfo['map'] = plus.webview.currentWebview().contractSubInfo['map'];
		//					vehicle_id=plus.webview.currentWebview().vehicle_id;
		//					car_trace_no=plus.webview.currentWebview().car_trace_no;
		console.log("接收参数->" + plus.webview.currentWebview().uploadPack);
		putoutVoucherList = new Map(plus.webview.currentWebview().putoutVoucherList);
		uploadPack = new Map(plus.webview.currentWebview().uploadPack);
		var openType = plus.webview.currentWebview().openType;
		if (openType != null && openType == "signature") {
			last_html = openType;
			vehicle_id = plus.webview.currentWebview().vehicle_no;
			car_trace_no = plus.webview.currentWebview().car_trace_no;
		}
		//params =plus.webview.currentWebview().params;
		console.log(uploadPack['map']);
		// add by gll
		 type = plus.webview.currentWebview().type;
		 console.log("d7777ss"+type);
		 desc = plus.webview.currentWebview().desc;
		 pack_id = plus.webview.currentWebview().pack_id;
		 
		 if(segNo == "00138"){
		 	console.log("dss"+plus.webview.currentWebview().type);
		 	if (type == "10"){
		 		console.log("dss");
		 		var tishi ="高强钢提示您确认收货前做好以下工作<br>&nbsp;&nbsp;1、确认装载货物外观质量完好<br>&nbsp;&nbsp;2、做好质量防护，安全绑扎<br>&nbsp;&nbsp;3、盖好雨布，严防材料受潮。请签收确认";
		 		$("#tishi").html(tishi);
		 	}else if (type == "20"){
		 		var tishi ='您送的母卷号:" '+ pack_id +'", 存在:"'+
		 		desc +'"请您签字确认!';
		 		$("#tishi").html(tishi);
		 	}else if (type == "30"){
		 		var tishi ='高强钢提示您确认收货前做好以下工作'+ '<br>&nbsp;&nbsp;' +'1、确认装载货物外观质量完好'+
		 		'<br>&nbsp;&nbsp;' +'2、做好质量防护，安全绑扎'+
		 		'<br>&nbsp;&nbsp;'+'3、盖好雨布，严防材料受潮。请签收确认';
		 		 tishi =tishi + '<br>您送的母卷号:" '+ pack_id +'", 存在:"'+desc +'"请您签字确认!';
		 		$("#tishi").html(tishi);
		 	}
		 }
		
		//end by gll
	});
	
}

$(document).ready(function() {

	// This is the part where jSignature is initialized.
	var $sigdiv = $("#signature").jSignature({
			'UndoButton': true
		}), // All the code below is just code driving the demo.
		$su = $('#sure'),
		$del = $('#delete'),
		$tools = $('#tools'),
		$extraarea = $('#displayarea'),
		pubsubprefix = 'jSignature.demo.'
	var export_plugins = $sigdiv.jSignature('listPlugins', 'export'),
		chops = ['<span><b>Extract signature data as: </b></span><select>',
			'<option value="">(select export format)</option>'
		],
		name
	for (var i in export_plugins) {
		if (export_plugins.hasOwnProperty(i)) {
			name = export_plugins[i]
			chops.push('<option value="' + name + '">' + name + '</option>')
		}
	}
	chops.push('</select><span><b> or: </b></span>')

	$(chops.join('')).bind('change', function(e) {
		if (e.target.value !== '') {
			var data = $sigdiv.jSignature('getData', e.target.value)
			$.publish(pubsubprefix + 'formatchanged')
			if (typeof data === 'string') {
				$('textarea', $tools).val(data)
			} else if ($.isArray(data) && data.length === 2) {
				$('textarea', $tools).val(data.join(','))
				$.publish(pubsubprefix + data[0], data);
			} else {
				try {
					$('textarea', $tools).val(JSON.stringify(data))
				} catch (ex) {
					$('textarea', $tools).val('Not sure how to stringify this, likely binary, format.')
				}
			}
		}
	}).appendTo($tools)

	// sure--确认按钮
	$('<input type="button" value="确认">').bind('click', function(e) {
		console.log($sigdiv.jSignature('getData', 'image'));
		//被坑的地方 取数据
		var data = $sigdiv.jSignature('getData', 'image');
		var i = new Image()
		i.src = 'data:' + data[0] + ',' + data[1]
		dataDriver = $sigdiv.jSignature('getData', 'image');
		//dataTrans = $sigdiv.jSignature('getData','image');
		if (last_html == "signature") {
			//装卸结束页面签字签名跳转过来
			handPointEndSignatureUpload();
		} else if (last_html == "putout_scan") {
			console.log("第一次签名111111111111111111");
			putoutPackUpload();
			signatureCount = "1";
			//signatureUpload()
		} else {
			console.log("再次签名222222222222222222");
			signatureUpload()
		}
	}).appendTo($su)

	//rest--清除按钮
	$('<input type="button" value="清除">').bind('click', function(e) {
		$sigdiv.jSignature('reset')
	}).appendTo($del)

	$('<div><textarea style="width:100%;height:7em;"></textarea></div>').appendTo($tools)

	$.subscribe(pubsubprefix + 'formatchanged', function() {
		$extraarea.html('')
	})

	$.subscribe(pubsubprefix + 'image/svg+xml', function(data) {

		try {
			var i = new Image()
			i.src = 'data:' + data[0] + ';base64,' + btoa(data[1])
			$(i).appendTo($extraarea)
		} catch (ex) {

		}

		var message = [
			"If you don't see an image immediately above, it means your browser is unable to display in-line (data-url-formatted) SVG.",
			"This is NOT an issue with jSignature, as we can export proper SVG document regardless of browser's ability to display it.",
			"Try this page in a modern browser to see the SVG on the page, or export data as plain SVG, save to disk as text file and view in any SVG-capabale viewer."
		]
		$("<div>" + message.join("<br/>") + "</div>").appendTo($extraarea)
	});

	//				$.subscribe(pubsubprefix + 'image/svg+xml;base64', function(data) {
	//					var i = new Image()
	//					i.src = 'data:' + data[0] + ',' + data[1]
	//					$(i).appendTo($extraarea)
	//
	//					var message = [
	//						"If you don't see an image immediately above, it means your browser is unable to display in-line (data-url-formatted) SVG.", "This is NOT an issue with jSignature, as we can export proper SVG document regardless of browser's ability to display it.", "Try this page in a modern browser to see the SVG on the page, or export data as plain SVG, save to disk as text file and view in any SVG-capabale viewer."
	//					]
	//					$("<div>" + message.join("<br/>") + "</div>").appendTo($extraarea)
	//				});
	//
	//				$.subscribe(pubsubprefix + 'image/png;base64', function(data) {
	//					var i = new Image()
	//					i.src = 'data:' + data[0] + ',' + data[1]
	//					$('<span><b>As you can see, one of the problems of "image" extraction (besides not working on some old Androids, elsewhere) is that it extracts A LOT OF DATA and includes all the decoration that is not part of the signature.</b></span>').appendTo($extraarea)
	//					$(i).appendTo($extraarea)
	//				});
	//
	//				$.subscribe(pubsubprefix + 'image/jsignature;base30', function(data) {
	//					$('<span><b>This is a vector format not natively render-able by browsers. Format is a compressed "movement coordinates arrays" structure tuned for use server-side. The bonus of this format is its tiny storage footprint and ease of deriving rendering instructions in programmatic, iterative manner.</b></span>').appendTo($extraarea)
	//				});
	//
	//				if(Modernizr.touch) {
	//					$('#scrollgrabber').height($('#content').height())
	//				}

});

//传司机和物流人员的信息
function signatureInfo() {
	var params = {
		"seg_no": segNo,
		"user_id": user_id,
		"vehicle_id": vehicle_id,
		"car_trace_no": car_trace_no,
		"img_base64_d": 'data:' + dataDriver[0] + ',' + dataDriver[1]
		/*,
				"img_base64_t":'data:' + dataTrans[0] + ',' + dataTrans[1]*/
	};
	return params;
}

function signatureUpload() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	var params = signatureInfo();
	params = JSON.stringify(params);
	console.log(params);
	params = window.encodeURIComponent(params);
	console.log("ddd:" + params);
	//params=encodeURI(params,'utf-8');
	// 定义接口里的方法
	var method = "exeuploadSignature";
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				mui.alert("签名上传成功", "提示", "确定", function() {
					if (signatureCount == "1") {
						mui.openWindow({
							url: 'hand_point_end.html',
							id: 'hand_point_end',
							createNew: true
						});
					} else {
						mui.openWindow({
							url: 'vehicle_load_manage.html',
							id: 'vehicle_load_manage',
							createNew: true
						});
					}
				}, "div");
			} else {
				mui.alert("签名上传失败请重试", "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui.alert("签名上传失败请重试", "提示", "确定", function() {}, "div");
		}
	});

}

//出库捆包上传post方法
function putoutPackUpload() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	//console.log("name:"+mean_name);
	//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
	mui("#putout").button('loading');
	$("#overlay").addClass("overlay");
	//如果出现异常或者超时设置半分钟后可以再点一次
	setTimeout(function() {
		mui("#putout").button('reset');
		$("#overlay").removeClass("overlay");
	}.bind(this), 5000);

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	//var params = initPutoutPackUploadParams();
	var params = plus.webview.currentWebview().params;
	console.log(params);
	params = JSON.stringify(params);
	console.log(params);
	//params=encodeURI(params,'utf-8');
	var method = "exeNewPutoutPackUpload";

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log("电子签名出库返回===》"+JSON.stringify(result));
			if (result != null) {
				//console.log("data:"+JSON.stringify(data));
				/*mui("#putout").button('reset');
                $("#overlay").removeClass("overlay");*/
				if (result.returnStatus == "1") {
					mui("#putout").button('reset');
					$("#overlay").removeClass("overlay");
					mui.alert("" + result.returnDesc, "提示", "确定", function() {
						signatureUpload();
						/*mui.openWindow({
							url:'vehicle_load_manage.html',
							id:'vehicle_load_manage', 
							createNew:true
						});*/
					}, "div");
				} else {
					mui.alert("出库失败：" + result.returnDesc, "提示", "确定", function() {}, "div");
				}
			} else {
				mui("#putout").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui("#putout").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
		}
	});

}

//初始化上传出库捆包参数
function initPutoutPackUploadParams() {
	var params = {};
	params['seg_no'] = segNo;
	params['user_id'] = user_id;
	params['team_id'] = team_id;
	params['work_shift'] = work_shift;
	params['operatorType'] = operatorType;
	params['vechile_id'] = vehicle_id;
	//tangli  add
	params['putout_confirm_flag'] = putout_confirm_flag;
	var voucherList = new Array();
	console.log("签名->uploadPack->" + JSON.stringify(uploadPack));
	// var aaa = new Map(uploadPack);
	// for(ley key of aaa.keys()) {
	// 	var voucherIdList = aaa.get(key);
	// 	$.each(voucherIdList, function(i) {
	// 		if(uploadPack.get(voucherIdList[i]).length > 0) { //该单据下有捆包要出库
	// 			var voucher = {};
	// 			//console.log("weerrrr："+JSON.stringify(uploadPack));
	// 			//console.log("weerrrr："+JSON.stringify(uploadPack.get(1)));
	// 			//从出库单据列表中取单据验证码
	// 			//console.log("qqqqqqqqqqqqqqqqqqqqf："+uploadPack.get(voucherIdList[i]));
	// 			//console.log("qqqqqqqqqqqqqqqqqqq："+JSON.stringify(voucherIdList));
	// 			//var letobj=voucherIdList[i];
	// 			//console.log("qqqqqqqqqqqqqqqqletobj ："+letobj);
	// 			//var inmber=getIndexByVoucherId(voucherIdList[i],putoutVoucherList);
	// 			//console.log("qqqqqqqqqqqqqqqqinmber："+inmber);
	// 			//var identify_code = putoutVoucherList[inmber].identify_code;
	// 			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i], putoutVoucherList)]).identify_code;
	// 			console.info("identify_codeidentify_codeidentify_code::" + identify_code);
	// 			console.log("identify_code:" + identify_code);
	// 			voucher['identify_code'] = identify_code;
	// 			voucher['voucher_id'] = voucherIdList[i];
	// 			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
	// 			//往单据列表中添加信息
	// 			voucherList.push(voucher);
	// 		}
	// 	});
	// }
	/*var aaa = new HashMap(uploadPack);
	var voucherIdList = aaa.keySet();
	$.each(voucherIdList, function(i) {
		if(uploadPack.get(voucherIdList[i]).length > 0){//该单据下有捆包要出库
			var voucher = {};
			//console.log("weerrrr："+JSON.stringify(uploadPack));
			//console.log("weerrrr："+JSON.stringify(uploadPack.get(1)));
			//从出库单据列表中取单据验证码
			//console.log("qqqqqqqqqqqqqqqqqqqqf："+uploadPack.get(voucherIdList[i]));
			//console.log("qqqqqqqqqqqqqqqqqqq："+JSON.stringify(voucherIdList));
			//var letobj=voucherIdList[i];
			//console.log("qqqqqqqqqqqqqqqqletobj ："+letobj);
			//var inmber=getIndexByVoucherId(voucherIdList[i],putoutVoucherList);
			//console.log("qqqqqqqqqqqqqqqqinmber："+inmber);
			//var identify_code = putoutVoucherList[inmber].identify_code;
			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i],putoutVoucherList)]).identify_code;
			console.info("identify_codeidentify_codeidentify_code::"+identify_code);
			console.log("identify_code:" + identify_code);
			voucher['identify_code'] = identify_code;
			voucher['voucher_id'] = voucherIdList[i];
			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
			//往单据列表中添加信息
			voucherList.push(voucher);
		}
	});*/
	params['voucherList'] = voucherList;

	return params;
}


/**
 * 装卸货结束上传电子签名
 */
function handPointEndSignatureUpload() {
	plus.nativeUI.showWaiting();　
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	//拼接参数信息
	var params = {
		"seg_no": segNo,
		"user_id": user_id,
		"vehicle_id": vehicle_id,
		"car_trace_no": car_trace_no,
		"confirm_flag": "1", //确认接收标记
		"function_type": "WL_PCD_SIGN",
		"function_type_desc": "物流出厂配车单签收图片-PDA签收",
		"file_name": Date.now() + ".png",
		"entry_flag": "1",
		"if_sign": "1",
		"file_type": "0",
		"document_no": car_trace_no,
		"img_base64_d": 'data:' + dataDriver[0] + ',' + dataDriver[1]
	};
	params = JSON.stringify(params);
	console.log(  "exeQueryUploadEndSignature参数：" + params);
	params = window.encodeURIComponent(params);
	// 定义接口里的方法
	var method = "exeQueryUploadEndSignature";

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log(method + "参数：" + JSON.stringify(result));
			plus.nativeUI.closeWaiting();　//等待框关闭
			if (result != null && result.out_result == "1") {
				mui.alert("签名上传成功", "提示", "确定", function() {
					//TODO 返回上层页面，然后结束装卸点
					setTimeout(onSignatureUploadSuccess,100);
					// onSignatureUploadSuccess();
				}, "div");
			} else {
				mui.alert("签名上传失败：" + result.out_result_desc, "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui.alert("签名上传失败请重试", "提示", "确定", function() {}, "div");
		}
	});

}

function onSignatureUploadSuccess(){
	var parent = plus.webview.currentWebview().opener();
	parent.evalJS('exeConfigVeicleFinishHandPoint();'); //调用结束装卸货
	mui.back();
}
