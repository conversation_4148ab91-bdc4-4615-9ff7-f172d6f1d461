
/**
 * add by <PERSON><PERSON> 
 */



/** 明细样式 */
.detail_row{
	height: 41.5px;
}

.mui-input-row span{
	top: 10px!important;
	height:36px;
}

.text{
	float: left;
	width: 38%;
	font-size: 18px;
	padding: 5px 0px;
	text-align: center;
	margin-left:5px;
	margin-top:5px;
}

/* 半透明的遮罩层 */
.overlay {
    background-color: #777777;
     opacity: 0.5; /* 透明度 */
/*filter: alpha(opacity=50); /* IE的透明度 
    
    display: none;
   
    top: 0px;
    left: 0px;*/
position: absolute;
width: 100%;
height: 100%;
z-index: 99999; /* 此处的图层要大于页面 */
/*display:none;*/
}





/**
 * end 
 */


.mui-control-content {
	background-color: white;
	min-height: 280px;
}

.mui-segmented-control{
	font-size: 22px;
}

.mui-table-view-cell{
	font-size: 22px;
	padding: 6px 0px 8px 18px;
	text-align: left;
}

/** 明细样式 */
.left{
	float: left;
	width: 50%;
}

.mui-navigate-right:after{
	content: '';
}

.select:after{
	content: '\e472';
	color: red;
	font-size: 50px;
	font-weight: 600;
	right: 10px;
}

#voucher_num{
	font-size: 22px;
	color: blue;
	margin-right: 10%;
}

.icon{
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}

.row{
	margin: 5px;
	font-size: 18px;
}

#required{
	color:red;
}

.active {
	background-color: #2AC845;
	color: white;
}


#button_province {
	width:23.5%;
	margin-top:4px;
	font-size: 18px;
}

#province{
	margin-left:3px;
	display: none;
	position: absolute;
	bottom:5px;
	line-height: 1.8;
	background-color: #efeff4;
	z-index: 0;
}



#confirm_province {
	margin-left: 2px;
	width:98%;
	margin-top:4px;
	font-size: 20px;
}

#province_name {
	width:50px;
	height:30px;
	background-color:#007aff;
	float:left;
	color: white;
	margin-top:2px;
	border-radius:3px;
	font-size: 24px;
	font-weight: bolder;
	margin-top:5px;
	margin-left:-6px;
}

#car_num{
	/*width: 61%;*/
	height:30px;
	padding: 0px 5px;
	font-size: 18px;
	margin-top:5px;
	border-radius:3px;
}

/*#confirmDiv {
	width:100%;
	height:60px;
	bottom: 0px;
	position: absolute;
	margin-left:2px;
	margin-right:2px;
	
}
*/
#confirm {
	/*margin-left: 2px;
position: absolute;
bottom:5px;
width:95%;
font-size: 20px;*/
text-align: center;
/*position: absolute;*/
	width:98%;
	height:60px;
	margin-top:5px;
	margin-left:3px;
	font-size: 24px; 
	line-height: 1.8; 
}

#hand_type {
	width:50%;
	margin-top:10px;
	text-align: center;
	text-align-last: center;
	font-size: 22px;
	color:red;
}