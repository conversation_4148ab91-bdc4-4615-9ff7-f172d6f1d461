var params = {};

var segNo = localStorage.getItem("segNo");
var segName = localStorage.getItem("segName");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");

//入库扫描页面传过来的捆包信息 成都宝钢 -xsp - ******** 
var putinPackList = new Array(); //入库捆包列表
//卸货确认页面传过来的捆包信息 成都宝钢 -xsp - ******** 

$(function() {
	var flag = 0;

	mui.plusReady(function() {
		putinPackList = plus.webview.currentWebview().putinPackList; //询问一下师傅，如果这样的话，会不会重复
		XH_pack_id = plus.webview.currentWebview().pack_id;
		if (putinPackList != "" && putinPackList != null) {
			//查询开关值
			flag = getSwitchValue(segNo, "IF_SCAN_STORAGE_PRINTING");

			//判断当前账套是否存在扫描入库打印开关
			if (flag == 1) {
				//显示扫描入库打印开关
				//成都宝钢 -xsp - ******** 入库成功后提示是否跳转捆包打印页面，并把捆包信息传过去
				$.each(putinPackList, function(i, value) {
					$("#pack_id").val(value.pack_id);
				});

				//add by yangzemin 主动查询捆包
				document.activeElement.blur();
				mui("#query_pack").button("loading");
				$("#overlay").addClass("overlay");
				queryPackInfo();
				mui("#query_pack").button("reset");
				$("#overlay").removeClass("overlay");
			}
		}

		if (XH_pack_id != "" && XH_pack_id != null) {
			//查询开关值
			flag = getSwitchValue(segNo, "IF_DISCHARGE_CONFIRMATION_PRINT");

			//判断当前账套是否存在卸货确认打印开关
			if (flag == 1) {
				//显示卸货确认打印开关
				//成都宝钢 -xsp - ******** 卸货成功后提示是否跳转捆包打印页面，并把捆包信息传过去
				$("#pack_id").val(XH_pack_id);
			}
		}
	});
});

mui.init({
	swipeBack: true, //关闭右滑关闭功能
	gestureConfig: {
		longtap: true,
		doubletap: true
	}
});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui(document.body).on('tap', '#query_pack', function() {
	document.activeElement.blur();
	mui("#query_pack").button("loading");
	$("#overlay").addClass("overlay");
	queryPackInfo();
	mui("#query_pack").button("reset");
	$("#overlay").removeClass("overlay");

});

//查捆包信息
function queryPackInfo() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + $("#pack_id").val() + '"}';
	console.log(params);
	var method = "exeDownLoadPrintPackInfo";
	handLocationFlag = "0";
	forLocationPack = {};
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("exeDownLoadPrintPackInfo返回：" + JSON.stringify(data));
			if (data != null) {
				if (segNo != '00115' &&
					(data.material_no == "" || data.material_no == null)) {
					mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】", "提示", "确定", function() {}, "div");
					return false;
				} else {
					initParams(JSON.stringify(data));
					console.log(">>>>>>>>>>>>>" + JSON.stringify(data.material_no))
					$("#pack_id").val(data.pack_id);
					if (data.size.length > 18) {
						$("#spec").val(data.size.substr(0, 18));
					} else {
						$("#spec").val(data.size);
					}
					$("#factory_product_id").val(data.resource_no);
					$("#grade").val(data.grade);
					$("#weight").val(data.nw);
					$("#qty").val(data.num);
					$("#customer").val(data.customer);
				}
			} else { //连接失败
				mui.alert("未扫描到" + $("#pack_id").val() + "的捆包信息，请检查", "提示", "确定", null, 'div');
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if (textStatus == "timeout") {

			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
}

function initParams(data) {
	var jsons = JSON.parse(data);
	if (segNo == "00104") {
		jsons.segName = "天津宝钢北方贸易有限公司";
		jsons.engName = "Tianjin Baosteel North Trade Co., Ltd.";
	} else if (segNo == "00113") {
		jsons.segName = "青岛宝井钢材加工配送有限公司";
		jsons.engName = "Qingdao Bao-Mit Steel Distribution Co.,Ltd.";
	} else if (segNo == "00129") {
		jsons.segName = "天津宝钢钢材配送有限公司";
		jsons.engName = "Tianjin Baosteel Steel Distribution Co., Ltd.";
	} else if (segNo == "00130") {
		jsons.segName = "济南宝钢钢材加工配送有限公司";
		jsons.engName = "Jinan Baosteel Steel Process&Distribution Co.,Ltd";
	} else if (segNo == "00131") {
		jsons.segName = "烟台宝井钢材加工有限公司";
		jsons.engName = "Yantai Baojing Steel Processing Co., Ltd.";
	} else if (segNo == "00142") {
		jsons.segName = "北京宝钢北方贸易有限公司";
		jsons.engName = "Beijing Baosteel North Trade Co., Ltd.";
	} else if (segNo == "00145") {
		jsons.segName = "天津宝井钢材加工配送有限公司";
		jsons.engName = "TianjinBao-MitSteelProcess&DistributionCo.,Ltd.";
	} else if (segNo == "00148") {
		jsons.segName = "山东宝华耐磨钢有限公司";
		jsons.engName = "ShanDong Baohua Distribution Co., Ltd.";
	} else if (segNo == "00174") {
		jsons.segName = "天津武钢钢材加工有限公司";
		jsons.engName = "Tianjin WISCO Steel Processing Co., Ltd.";
	} else if (segNo == "00112") {
		jsons.segName = "成都宝钢加工配送有限公司";
		jsons.engName = "Chengdu Baosteel Process&DistributionCo.,Ltd.";
	} else if (segNo == "00143") {
		jsons.segName = "大连宝友金属制品有限公司";
		jsons.engName = "Dalian Bao-Summit Metal Products Co.,Ltd.";
	} else if (segNo == "00125") {
		jsons.segName = "福州宝井钢材有限公司";
		jsons.engName = "";
	} else if (segNo == "00118") {
		jsons.segName = "广州花都宝井汽车钢材部件有限公司";
		jsons.engName = "";
	} else if (segNo == "00152") {
		jsons.segName = "佛山三水宝钢钢材部件有限公司";
		jsons.engName = "";
	} else if (segNo == "00115") {
		jsons.segName = "郑州宝钢钢材加工配送有限公司";
		jsons.engName = "";
	} else if (segNo == "00119") {
		jsons.segName = "柳州宝钢汽车钢材部件有限公司";
		jsons.engName = "";
	} else if (segNo == "00165") {
		jsons.segName = "柳州宝钢汽车零部件有限公司";
		jsons.engName = "";
	} else if (segNo == "00170") {
		jsons.segName = "开封威仕科材料技术有限公司";
		jsons.engName = "";
	}else {
		jsons.segName = "";
		jsons.engName = "";
	}
	params = jsons;
	params.printCount = 1;
}

mui(document.body).on('tap', '#print', function() {
	mui("#print").button("loading");
	$("#overlay").addClass("overlay");
	console.log(params.resource_no);
	printLabel(params);
	localStorage.setItem("DaYin_putinPackList", "");
	localStorage.setItem("DaYin_pack_id", "");
});

function printLabel(params) {
	if (params.resource_no != $("#factory_product_id").val()) {
		mui.alert("未查到入库捆包信息！", "提示", "确定", function() {
			mui("#print").button("reset");
			$("#overlay").removeClass("overlay");
		}, "div");
		return false;
	} else {
		//打印次数弹窗的开关
		var code_value = getSwitchValue(segNo, "PRINT_THE_NUMBER");
		if (code_value == "1") {
			//打印次数弹窗
			document.activeElement.blur();
			$("#pop_car").toggleClass('show');
			$("#InnerVehicleDiv").toggleClass('show');
		} else {
			plus.bluetoothprint.printFunction(params);
			printDa();
		}
	}
}
//打印次数弹窗 取消按钮点击事件
mui(document.body).on('tap', '#innerVehicleCancel', function() {
	$("#pop_car").toggleClass('show');
	$("#InnerVehicleDiv").toggleClass('show');
});
//打印次数弹窗 确认按钮点击事件
mui(document.body).on('tap', '#innerVehicleConfirm', function() {
	$("#pop_car").toggleClass('show');
	$("#InnerVehicleDiv").toggleClass('show');
	var count = mui(document.body).numbox().getValue();
	params.printCount = count;
	plus.bluetoothprint.printFunction(params);
	printDa();
});

function printDa() {
	$("#pack_id").val("");
	$("#spec").val("");
	$("#factory_product_id").val("");
	$("#grade").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#customer").val("");
	mui.alert("打印中，请稍作等待", "提示", "确定", function() {
		mui("#print").button("reset");
		$("#overlay").removeClass("overlay");
	}, "div");
}
