/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var pack_id = ""; //定义捆包号
var product_id = ""; //定义资源号
var putin_id = ""; //定义临时入库申请单号(流水号)
var imageNameList = []; //图片名称
var putin_scan_transfer_page = ""; 
var upload_image_page = "";
$(function(){
	mui.init({
		swipeBack:false //启用右滑关闭功能
	});
	var vehicle_id = localStorage.getItem("vehicle_id");
	$("#vehicle_id").html(vehicle_id);
	car_trace_no = localStorage.getItem("car_trace_no");
	hand_point_id = localStorage.getItem("hand_point_id");
	if(segNo=="00118"){
	 	$("#morebut").attr("style","display:none;");  
	}
});


/**
 * 页面加载获取转库入库扫描页面出传递过来的捆包号与资源号
 */
window.onload = function onload(){
	mui.plusReady(function(){
		pack_id = plus.webview.currentWebview().pack_id;
		product_id = plus.webview.currentWebview().product_id;
		putin_scan_transfer_page = plus.webview.getWebviewById("putin_scan_transfer");
		upload_image_page = plus.webview.getWebviewById("upload_image");
		$("#pack_id").val(pack_id);
		$("#product_id").val(product_id);
	});
}



/**
 * 删除页面上准备上传的图片
 */
/*function deleteImage(ths) {
	var btnArray = ["取消", "确认"];
	mui.confirm("确认删除这张图片吗？","",btnArray,function(e) {
		if (e.index == 1) {
			var id = $(ths).attr("id");
			console.log("被删除的ID：" + id);
			$(ths).remove();
			console.log(files);
			files.splice(id, 1); //从存放文件的数组中删除图片
			base64ImageCode.splice(id,1);
			console.log(files);
			console.log(files[0]);
			console.log(files[1]);
		}
	});
}*/

//删除指定下标数组 传入数组 和指定的下标 最终返回遍历重组后的数组
function remove(arr,key){
    var arr_tmp = new Array();
    for(key_tmp in arr){
        if(key_tmp != key){
            arr_tmp[key_tmp] = arr[key_tmp];
        }
    }
    return arr_tmp;
}


//选取相册中的照片
var files = new Array(); //定义数组存放被选中的图片
var imgName = new Array(); //定义数组存放被选中的图片的名称
var index = 1;
var appendStr = "";
var base64ImageCode = new Array(); //定义数组存放被选中的图片的base64编码
$("#select_image").click(function(){
	var actionbuttons = [
		//{title: "拍照"}, 
		{title: "从相册中选择"}
	];
	var actionstyle = {
		title: "选择图片",
		cancel: "取消",
		buttons: actionbuttons
	};
	plus.nativeUI.actionSheet(actionstyle, function(e) {
		switch(e.index) {
			//case 1: //拍照
				//plus.camera.getCamera().captureImage(function(p) {
				//	appendFile(p);
				//});
				//break;
			case 1: //从相册中选择
				plus.gallery.pick(function(e) {
					if((files.length + e.files.length) > 3) {
						mui.toast('一次性最多只能选择三张照片！');
						return;
					}
					console.log(files.length);
					console.log(e.files.length);
					//遍历选中的图片文件
			        for(var i in e.files){
			        	console.log(files.push(e.files[i]));
			        	var fileName = e.files[i].substr(e.files[i].lastIndexOf('/') + 1); 
			        	imgName[i] = fileName;
			        	var name = "_doc/upload/"+fileName;  
			        	//压缩
						plus.zip.compressImage({
								src:e.files[i],//src: (String 类型 )压缩转换原始图片的路径    
								dst:name,//压缩转换目标图片的路径    
								quality:40,//quality: (Number 类型 )压缩图片的质量.取值范围为1-100    
								overwrite:true//overwrite: (Boolean 类型 )覆盖生成新文件    
							},
							function(zip) {
								//页面显示图片  
								plus.io.resolveLocalFileSystemURL(zip.target, function(entry) {
									entry.file(function(file){  
										var fileReader = new plus.io.FileReader();  
										fileReader.readAsDataURL(file);  
										fileReader.onloadend = function(e) {  
											var picUrl = e.target.result.toString();  
											base64ImageCode.push(picUrl); //将被选中图片的base64编码放入数组
										}
					               	});
								}, function(e) {
									mui.toast('读取照片文件错误：' + e.message);
								});
							},function(error) {
								mui.toast("压缩图片失败，请稍候再试");    
						}); 
			            appendStr = appendStr + '<img onclick="deleteImage(this)" id="' + i + '"src="' + e.files[i] + '" style="float:left;" width="32%"/>';
			            console.log(appendStr);
			        }
					$("#show_image").html(appendStr);  //将图片放入页面显示
				},function (e) {
					console.info("取消选择照片");
				},{
					filter:"image",//定义只能选择图片的过滤器
			    	multiple: true, //设置支持多张上传
					animation: true, //
					maximum: 2, 
					//system: false,
					selected: files, //设置选中的图片放入files数组
					onmaxed: function() {
						//mui.toast("一次只能选择一张照片");
				    }//过滤器只要图片，多选
				});
				break;
		}
	});
	
});

/**
 * 上传图片
 */
$("#upload_image").click(function() {
	if (files.length <= 0) {
		mui.toast("请选择图片！");
		return;
	}
	var btnArray = ['确认', '取消'];
	mui.confirm('确认上传选中图片?', '提示', btnArray, function(e) {
		if (e.index == 0) {
			uploadImage();
		}
	},'div');
});


function uploadImage() {
	var pack_id = $("#pack_id").val();  //获取捆包号
	var product_id = $("#product_id").val(); //获取资源号
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAZKService';
	var method = "exeUploadImg";
	console.log(outUri);
	console.log(innerUri);
	var tempDate = new Date().getTime();
	console.log("product_id:===>" + product_id);
	var params = {"seg_no":segNo,"imageCode":base64ImageCode,"user_id":user_id,"pack_id":pack_id,"product_id":product_id,"putin_id":tempDate};
	console.log("params:" + params);
	params = JSON.stringify(params);
	params = window.encodeURIComponent(params);
	console.log("params:" + params);
	$.ajax({
    	type: "post",
		url : outUri,
		data: {innerUri:innerUri,params:params,method:method},
		dataType : "json",
		timeout : 5000,
		async : true,
		cache : false,
	    success : function(data) {
	    	if (data.result == "1") {
	    		mui.toast(data.result_desc);
	    		var imageName = data.resultImageName;
				var ws = plus.webview.currentWebview();
				plus.webview.close(ws);
				var putinScanTransferPage = plus.webview.getWebviewById("putin_scan_transfer");
				mui.fire(putinScanTransferPage,'uploadSuccessed',{
					putin_id: tempDate,
					imageName : imageName,
					pack_id : pack_id
				});
				putin_scan_transfer_page.show();
				upload_image_page.close();
	    	} else {
	    		mui.alert("连接超时，上传失败！", "提示", "确认", null, "div");
	    	}
	    },
	    error : function() {
	    	mui.toast("服务器异常，请稍后再试！");
	    }
    });
}

/*function test() {
	
	var map = new Map().set("3", "c").set("2", "b").set("1", "a");
	for (var key of map.keys()) {
		alert(key + "||" + map.get(key));
	}
	
}*/
/*window.onload = function onload(){
	mui.plusReady(function(){
		var putin_scan = plus.webview.getWebviewById("putin_scan");
		plus.webview.close(putin_scan, "none");
	});
}*/

//将图片压缩转成base64编码 
/*function getBase64Image(img) { 
    var canvas = document.createElement("canvas"); 
    var width = img.width; 
    var height = img.height;
    // calculate the width and height, constraining the proportions 
    if (width > height) { 
	    if (width > 100) { 
	        height = Math.round(height *= 100 / width); 
	        width = 100; 
	    } 
    } else { 
	    if (height > 100) { 
	        width = Math.round(width *= 100 / height); 
	        height = 100; 
	    } 
    } 
    console.log("width======>" + width);
    console.log("height=====>" + height);
    canvas.width = width;   //设置新的图片的宽度
    canvas.height = height; //设置新的图片的长度
    var ctx = canvas.getContext("2d");
    ctx.drawImage(img, 0, 0, width, height); //绘图
    var dataURL = canvas.toDataURL();
    //var dataURL = canvas.toDataURL("image/png", 0.7);
    //return dataURL.replace("data:image/png;base64,", "");
    return dataURL;
}*/

/*mui.back = function() {
}*/