/** 明细样式 */

.detail_row {
	height: 49px;
}

.li-text {
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 78px;
}

.li-text p span font {
	text-align: center;
	font-size: 22px;
	color: #000000;
}

.li-height {
	margin-top: 4px;
}


/** 列表样式 */

.pack_location_target {
	color: red;
	font-size: 22px;
}

.icon-search {
	position: absolute;
	left: 320px;
	z-index: 5;
	background-image: url(../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 140px;
	/*图片显示的高*/
	right: 5px;
}

ul {
	width: 100%;
	float: right;
	margin-right: 9px;
}

li {
	/*  text-align: center;*/
}


/** 弹出图层设置 */

#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	/*text-align: center;*/
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
	margin-left: 10px;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}


/*更多按钮*/

.morebut_s {
	margin-left: 25%;
	margin-top: -6px;
	background-color: green;
	color: white;
}

.text {
	float: left;
	width: 28%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	margin-left: 4px;
	margin-top: 3px;
}


/*蓝色按钮,绝对定位*/

.blueButton {
	position: absolute;
	display: block;
	width: 100px;
	height: 40px;
	background-color: #007aff;
	color: #fff;
	text-decoration: none;
	text-align: center;
	font: normal normal normal 16px/40px 'Microsoft YaHei';
	cursor: pointer;
	border-radius: 4px;
}

.blueButton:hover {
	text-decoration: none;
}


/*自定义上传,位置大小都和a完全一样,而且完全透明*/

#myFileUpload {
	position: absolute;
	display: block;
	width: 100px;
	height: 40px;
	opacity: 0;
}


/*显示上传文件夹名的Div*/

.show {
	position: absolute;
	top: 40px;
	width: 100%;
	height: 30px;
	font: normal normal normal 14px/30px 'Microsoft YaHei';
}