#electric_signature,
#start_load,
#start_unload,
#end_load,
#leave_unload,
#wait_work_vehicle ,   
#fenpei{ 
	width: 100%;
	font-size: 22px;
	line-height: 1.8;
	margin: 10px 0px 0px 2px;
} 

.detail_row {
	height: 55px;
}

.text {
	float: left;
	width: 28%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#hand_point_name {
	background: #CCCCCC;
}

#vehicle_id,
#hand_point_name {
	width: 72%;
	padding: 0px 5px;
	font-size: 20px;
}


/** 合计框样式 */

.icon-setting {
	position: absolute;
	left: 260px;
	top: 10px;
	z-index: 5;
	background-image: url(../../resource/setting.gif);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}


/*			.icon-search{
				position: absolute;left: 300px;z-index:5;
				background-image: url(../resource/search.png);
				background-repeat: no-repeat; 
				background-position: right; 
				width: 40px; 
				height: 40px; 
				right: 5px;
			}*/


/*			ul{
				width: 70%;
				float: right;
				margin-right: 6px;
			}*/


/* 半透明的遮罩层 */

.vehicle button {
	font-size: 15px;
	width: 32%;
}


/** 弹出图层设置 */

#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

.vehicle_num {
	color: blue;
	font-size: 22px;
}

.active {
	background-color: #2AC845;
	color: white;
}


/*更多按钮*/

.morebut_s {
	margin-left: 25%;
	margin-top: -6px;
	background-color: green;
	color: white;
}

#backbutton {
	width: 45%;
	font-size: 22px;
	line-height: 1.8;
	margin-left: 10px;
	margin-right: 10px;
}

#confirm {
	width: 45%;
	font-size: 22px;
	line-height: 1.8;
	margin-left: 0px;
}
.mui-navigate-right:after{
				content: '';
			}