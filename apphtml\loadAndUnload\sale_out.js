var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var ownPackInfoList = new Array(); //自带捆包
var scanPackList = new Array(); //扫描捆包
var checkList = new Array();
var pack_count = 0; //捆包合计
var scan_number = 0; //已扫描个数
var scanBillList = new Array(); //已扫描出库单集合

//资源号
var product_id = "";

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//出厂确认按钮
mui(document.body).on('tap', '#confirm', function() {
	/*if(pack_count == scan_number){*/
	// 出厂操作     --更新捆包表的状态为出厂     --不限制一定要全部扫描
	exeOwnGoodVehicleOut();
	/*}else{
		var leak = pack_count-scan_number;
		mui.alert("您有"+leak+"个捆包未扫描，请检查后重试！", "提示", "确定", function() {}, "div");
	}*/
});

$(function() {
	mui.plusReady(function() {
		ownPackInfoList = plus.webview.currentWebview().ownPackInfoList;
		showMateSuccessPackInfo();
	});
	//出库单号输入框获得焦点
	$("#to_scan_pack")[0].focus();
});

/* //出库单扫描输入按下监听
$("#to_scan_outStock").keypress(function(e) {
	var chuku_id = $("#to_scan_outStock").val();
	console.log(chuku_id);
	if(ownPackInfoList.length != 0) {
		mui.alert(chuku_id + "出库单已扫完成，请执行出厂捆包扫描操作！", "提示", "确定", function() {}, "div");
		return false;
	} else {
	if (e.keyCode == 13) {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var outUri = domainName + "webService_test.jsp";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		var params = '{"seg_no":"' + segNo + '","chuku_id":"' + chuku_id + '"}';
		console.log(params);
		params = encodeURI(params, 'utf-8');
		var method = "saleOutKunbao";
		$.ajax({
			type: "post",
			//contentType:"application/json",
			async: true,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(result) {
				ownPackInfoList= result.saleOutList;
				pack_count = ownPackInfoList.length;
				console.log(JSON.stringify(ownPackInfoList));
				showMateSuccessPackInfo();
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				//console.log(getnowtime());
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
				this;
			}
		});
	}
	//}
}); */

//出厂捆包扫描输入按下监听
$("#to_scan_pack").keypress(function(e) {
	console.log($("#to_scan_pack").val());
	if (scan_number == pack_count) {
		mui.alert(pack_id + "捆包已扫完成，请执行出厂操作！", "提示", "确定", function() {}, "div");
		return false;
	} else {
		if (e.keyCode == 13) {
			pack_id = $("#to_scan_pack").val();
			matePutoutPack(pack_id, ownPackInfoList);
			$("#to_scan_pack").val("");
			$("#to_scan_pack")[0].focus();
		}
	}
});

// 显示要出厂的捆包信息
function showMateSuccessPackInfo() {
	//console.log("ownPackInfoList:" + JSON.stringify(ownPackInfoList));
	//绘制已扫捆包信息
	var phtml = "";
	var prot = "";
	$.each(ownPackInfoList, function(i, item) {
		pack_count += item.length;
		$.each(item, function(j, jtem) {
			if (jtem.pack_type == 1) {
				prot = "已出厂";
			} else {
				prot = "未出厂";
			}
			phtml = phtml + '<li class="mui-table-view-cell">' +
				'<div class="mui-slider-handle">' +
				'<div id="pack_id" onclick=""  data="' + jtem.label_id + '">' + jtem.label_id + '</div>' +
				'<div>' +
				'<div id="status" class="left"><span>捆包状态</span><label>' + jtem.pack_status + '</label></div>' +
				'<div id="prot" ><span>出厂确认</span><label>' + prot + '</label></div>' +
				'</div>' +
				'</a>' +
				'</li>';
		});
	});
	$("#pack_count").text(pack_count);
	$("#phtml").html(phtml);
}

// 匹配应出厂捆包明细
function matePutoutPack(pack_id, ownPackInfoList) {
	var scanPackInfo = new HashMap();
	var flag = 0;
	$.each(ownPackInfoList, function(j, jvalue) {
		$.each(jvalue, function(i, value) {
			if (value.label_id == pack_id || value.pack_id == pack_id) {
				product_id = value.product_id;
				if (value.label_id != value.pack_id) {
					pack_id = value.pack_id;
				}
				//如果捆包有
				flag = 1;
				return true;
			}
		});
	});
	if (flag == 1) {
		if (IsInArray(checkList, pack_id)) {
			mui.alert(pack_id + "捆包已扫描过", "提示", "确定", function() {}, "div");
			return false;
		} else {
			scanPackInfo.put("seg_no", segNo);
			scanPackInfo.put("pack_id", pack_id);
			scanPackInfo.put("product_id", product_id);
			//scanPackInfo.put("status", "匹配成功");
			scanPackList.push(scanPackInfo);
			console.log("scanPackList:" + JSON.stringify(scanPackList));
			checkList.push(pack_id);
			scan_number = scanPackList.length;
			$("#scan_number").text(scan_number);
		}
	} else {
		mui.alert(pack_id + "此捆包与车辆不匹配", "提示", "确定", function() {}, "div");
		return false;
	}
	//showMateSuccessPackInfo();
}

function IsInArray(arr, val) {
	console.log(JSON.stringify(arr));
	var testStr = ',' + arr.join(",") + ",";
	return testStr.indexOf("," + val + ",") != -1;
}

// 出厂
function exeOwnGoodVehicleOut() {
	mui("#confirm").button('loading');
	if (scanPackList.length == 0) {
		mui.alert("请扫描要出厂的捆包!", "提示", "确定", function() {}, "div");
		mui("#confirm").button('reset');
	} else {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var outUri = domainName + "webService_test.jsp";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		var params = JSON.stringify(scanPackList);
		console.log(params);
		params = encodeURI(params, 'utf-8');
		var method = "updateSaleOutKunbao";
		$.ajax({
			type: "post",
			//contentType:"application/json",
			async: true,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(result) {
				mui("#confirm").button('reset');
				console.log(result)
				if (result = "true") {
					mui.alert("出厂成功!", "提示", "确定", function() {
						window.location.href="sale_out_bill.html";
					}, "div");

				} else {
					mui.alert("服务器出错", "提示", "确定", function() {
						window.location.href="sale_out_bill.html";
					}, "div");
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(getnowtime());
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
				this;
			}
		});
	}
}
