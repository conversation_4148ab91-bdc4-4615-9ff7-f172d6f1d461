<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>车辆装卸货管理</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" type="text/css" href="vehicle_load_manage.css" </head>

		<body>
			<div class="mui-bar mui-bar-nav">
				<i class="icon-setting"></i>
				<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
				<h4 class="mui-title">车辆装卸货管理</h4> 
			</div>
			<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
				<div style="text-align: left;">
					<!-- 具体内容 -->
					<!-- 装卸点 -->
					<div class="detail_row" id="loading_point">
						<div class="text">装&nbsp;卸&nbsp;点</div>
						<div> <input id="hand_point_name" type="text" disabled="disabled" class="mui-input-clear"> </div>
					</div>

					<!-- 车牌号 -->
					<div class="detail_row">
						<div class="text" id="vehicle_no_display">车&nbsp;牌&nbsp;号</div>
						<div> <input id="vehicle_id" type="text" class="mui-input-clear" readonly="readonly">
							<input id="car_trace_no" type="text" class="mui-input-clear" hidden="hidden">
							<input id="check_type" type="text" class="mui-input-clear" hidden="hidden">
							<input id="allocate_vehicle_id" type="text" class="mui-input-clear" hidden="hidden">
							<input id="allocate_scope" type="text" class="mui-input-clear" hidden="hidden">
						</div> 
					</div>
					<div style="width: 100%;" class="vehicle-button">
						<p>
							<font style="font-size: 16px; color: #0000FF; font-weight: bold;">已入厂车辆:</font><button id="morebut" type="button" class="mui-btn morebut_s">其他作业点车辆</button></p>

						<div id="vehicle">
							<!--<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>
			        		<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>
			        		<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>
			        		<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>
			        		<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>
			        		<button type="button" class="mui-btn" style="width:31%;margin:1px ;" >粤A12345</button>-->
						</div>
					</div>
					<!-- 按钮 --> 
					<div>
						<button id="start_load" type="button" class="mui-btn mui-btn-primary">开始装货</button>
						<button id="start_unload" type="button" class="mui-btn mui-btn-primary">开始卸货</button>
						<button id="electric_signature" type="button" class="mui-btn mui-btn-primary">电子签名</button>
						<button id="wait_work_vehicle" type="button" class="mui-btn mui-btn-primary">等待作业车辆看板</button>
						<button id="leave_unload" type="button" class="mui-btn mui-btn-danger" style="display: none;">未装离厂</button>
						<button id="end_load" style="display: none;" type="button" class="mui-btn mui-btn-danger">结束装卸</button>
					   <button id="fenpei" style="display: none;" type="button" class="mui-btn mui-btn-primary">装卸点分配</button> 
					</div>
				</div>
			</div>

			<!-- 弹出捆包信息图层 -->
			<div id="pop_car_info">
				<div class="title">车辆信息</div>
				<div style="text-align: left;">
					<!--车牌查询-->
					<div class="mui-input-row mui-search">
						<input id="car_no" class="mui-input query-input" style="margin-bottom: 0px; padding: 2px 10px; font-size: 20px;" type="text" placeholder="车牌号" />
						<input id="peichedanhao" type="text" hidden="hidden" />
						<input id="zhuangxietype" type="text" hidden="hidden" />
						<input id="genzonghao" type="text" hidden="hidden" />
						<input id="p_thid" type="text" hidden="hidden" />
						<input id="p_scope" type="text" hidden="hidden" />
						<a href="#"><span class="mui-icon mui-icon-search" id="query_button"></span></a>
					</div>
					<!--显示列表-->
					<div style="margin: 5px; height: 201px; overflow: auto; background-color: #ECECEC;" id="carinfo">
						<ul id="carList" class="mui-table-view mui-table-view-radio" />
					</div>
					<!--出库车辆确认-->
					<div class="mui-input-row" style="margin-top: 1px;">
						<button id="backbutton" type="button" class="mui-btn mui-btn-primary">返回</button>
						<button id="confirm" type="button" class="mui-btn mui-btn-primary">确认</button>
					</div>
				</div>
			</div>
			<!-- 覆盖背景图层 -->
			<div id="pop_car"></div>

			<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
			<script src="../../js/mui.min.js"></script>
			<script src="../../js/pda/putin.js"></script>
			<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
			<script type="text/javascript" src="vehicle_load_manage.js"></script>
		</body>

</html>