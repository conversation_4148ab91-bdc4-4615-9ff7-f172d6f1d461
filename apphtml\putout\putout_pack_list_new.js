			/**
			 * 初始化变量信息 
			 */
			
			var putinPackList = new Array();//入库捆包列表
			var unUploadPack = new HashMap();//未上传捆包信息  Map<voucher_id,packList>
			var uploadPack = new HashMap();//已上传捆包信息  Map<voucher_id,packList>
			var packInfo = new HashMap();//捆包信息  Map<pack_id,packInfo>
			var contractSubInfo = new HashMap();//形式提单子项信息  Map<contract_subid,weight_qty_info>
			var selectPackList =  new Array();//已选择待删除捆包
			var locationPack = [];
			var specNumber = new Array();//不同规格捆包的数量
			var firstLoad = true;//第一次加载
			$(function(){
				mui.init({
					swipeBack:true, //启用右滑关闭功能
					gestureConfig:{
						longtap: true,
						doubletap: true
					}
				});
			});
			(function($) {
				$('.mui-scroll-wrapper').scroll({
					indicators: true //是否显示滚动条
				});
			})(mui);
			window.onload = function onload(){
				mui.plusReady(function(){
					putinPackList = plus.webview.currentWebview().putinPackList;
					unUploadPack['map'] = plus.webview.currentWebview().unUploadPack['map'];
					uploadPack['map'] = plus.webview.currentWebview().uploadPack['map'];
					packInfo['map'] = plus.webview.currentWebview().packInfo['map'];
					contractSubInfo['map'] = plus.webview.currentWebview().contractSubInfo['map'];
					//console.log("unUploadPack:" + JSON.stringify(unUploadPack));
					//console.log("uploadPack:" + JSON.stringify(uploadPack));
					//console.log("packInfo:" + JSON.stringify(packInfo));
					//console.log("contractSubInfo:" + JSON.stringify(contractSubInfo));
					//添加捆包信息到ul中
					showPackList();
				});
			}
			
			mui(document.body).on('tap','#back',function(){
				mui.back();
			});
			
			
			mui.back = function(){
				if(!checkBeforeback()){
					return false;
				}
				//返回
				var page = plus.webview.getWebviewById('putout_scan_new');
				mui.fire(page,'back',{
					unUploadPack : unUploadPack,
					uploadPack : uploadPack,
					packInfo : packInfo,
					contractSubInfo : contractSubInfo,
					putinPackList : putinPackList
				});
				mui.openWindow({
					id:'putout_scan_new'
				});
			}
			
			function showPackList(){
				//绘制已扫捆包信息
				var phtml="";
				var upload = uploadPack.keySet();
				$.each(upload, function(i,item) {
					var list = uploadPack.get(item);
					$.each(list, function(j,jitem) {
						var pack = packInfo.get(jitem.pack_id);
						var customerName = "";
							if(pack.customer_name != null && pack.customer_name != undefined){
								customerName = pack.customer_name.substr(0,8);
							}
						phtml=phtml+'<li class="mui-table-view-cell">'+
										'<a class="mui-navigate-right">'+
										'<div>'+
											'<div id="pack_id"  data="'+pack.pack_id+'">'+pack.pack_id+'<span>客</span><label>'+customerName+'</label></div>'+
											'<div>'+
												'<div id="weight" class="left"><span>重</span><label>'+pack.putin_weight+'</label></div>'+
												'<div id="spec"><span>规</span><label>'+pack.spec+'</label></div>'+
											'</div>'+
										'</div>'+
										'</a>'+
									'</li>';
						if(firstLoad){
							if(!specNumber[pack.spec]){
								specNumber[pack.spec] = 1;
							}else{
								specNumber[pack.spec] += 1;
							}
							
						}
					});
				});
				$("#phtml").html(phtml);
				locationPack=[];
			//绘制未扫捆包信息
				var unUploada = unUploadPack.keySet();
				console.info("unUploadunUploadunUpload::"+unUploada);
					$.each(unUploada, function(i,item) {
						var lista = unUploadPack.get(item);
						$.each(lista, function(j,jitem) {
						var pack = packInfo.get(jitem.pack_id);
							console.info("aaa"+ JSON.stringify(pack));
							console.info("pack库位：："+pack.location_desc);
							locationPack.push(pack);
						});
					});
					console.info("nihao :"+JSON.stringify(locationPack));
					var map = {},
				    dest = [];
				for(var i = 0; i < locationPack.length; i++){
				    var ai = locationPack[i];
				    if(!map[ai.location_desc]){
				        dest.push({
				            location_desc: ai.location_desc,
				            packoject: [ai]
				        });
				        map[ai.location_desc] = ai;
				    }else{
				        for(var j = 0; j < dest.length; j++){
				            var dj = dest[j];
				            if(dj.location_desc == ai.location_desc){
				                dj.packoject.push(ai);
				                break;
				            }
				        }
				    }
				}
				var shtml="";
				var dest1 = new Array();
				//xsp 20200402  未扫捆包排序升序
				$.each(dest, function(j,jitem) {
						dest1[j] = jitem.location_desc;
				});	
				console.info("dest1::"+dest1.sort());
				$.each(dest1.sort(), function(j,jitem) {
						shtml=shtml+'<li class="mui-table-view-cell mui-collapse">'+
										'<a class="mui-navigate-right">'+jitem+'</a>'+
											'<ul class="mui-table-view mui-table-view-chevron" id="'+jitem+'">'+
											'</ul>'+
									'</li>';
				});
				$("#shtml").html(shtml);
				$.each(dest, function(j,jitem) {
					var shtmlsub = "";
					var location_desc = jitem.location_desc;
						$.each(jitem.packoject, function(k,kitem){
							var customerName = "";
							if(kitem.customer_name != null && kitem.customer_name != undefined){
								customerName = kitem.customer_name.substr(0,8);
							}
						shtmlsub=shtmlsub+'<li class="mui-table-view-cell">'+
											'<a class="mui-navigate-right">'+
												'<div>'+
													'<div id="pack_id"  data="'+kitem.pack_id+'">'+kitem.pack_id+'<span>客</span><label>'+customerName+'</label></div>'+
													//'<div id="location_desc"><span></span><label>' +/*+kitem.location_desc+*/'</label></div>' +
												'<div>'+
													'<div id="spec" class="left"><span>规</span><label>'+kitem.spec+'</label></div>'+
													'<div id="weight"><span>重</span><label>'+kitem.putin_weight+'</label></div>'+
												'</div>'+
											'</a>'+
										'</li>';
						});
						$("#"+location_desc).html(shtmlsub);
					});
				firstLoad = false;
				showNumber();
//				//绘制未扫捆包信息
//				var shtml="";
//				var unUpload = unUploadPack.keySet();
//				console.info("unUploadunUploadunUpload::"+unUpload);
//				$.each(unUpload, function(i,item) {
//					var list = unUploadPack.get(item);
//					$.each(list, function(j,jitem) {
//						var pack = packInfo.get(jitem.pack_id);
//						shtml=shtml+'<li class="mui-table-view-cell">'+
//										'<a class="mui-navigate-right">'+
//										'<div>'+
//											'<div>'+
//												'<div id="pack_id" class="left" data="'+pack.pack_id+'">'+pack.pack_id+'</div>'+
//												'<div id="location_desc"><span>库</span><label>'+pack.location_desc+'</label></div>' +
//											'<div>'+
//											'<div>'+
//												'<div id="weight" class="left"><span>重</span><label>'+pack.putin_weight+'</label></div>'+
//												'<div id="spec"><span>规</span><label>'+pack.spec+'</label></div>'+
//											'</div>'+
//										'</div>'+
//										'</a>'+
//									'</li>';
//					});
//				});
//				$("#shtml").html(shtml);
			}
			
			//显示捆包规格数量
			function showNumber(){
				$("#spec1").html("");
				var spceHtml = "";
				for(var key in specNumber){
					console.log("key:" + key + ",value:" + specNumber[key]);
					spceHtml += '<li class="mui-table-view-cell">' +
													'<div>' +
														'<div id="spec" class="left" style="width:70%;"><span>规</span><label>'+key+'</label></div>' +
														'<div id="weight"><span>件</span><label>'+specNumber[key]+'</label></div>'+
													'</div>' + 
											
												'</li>'
				}
				$("#spec1").html(spceHtml);
			}
			
			//绑定已扫描捆包点击事件
			mui(document.body).on('tap','#putout li',function(){
				var a = $(this).children('a');
				if(a.hasClass("select")==false){
					a.addClass("select");
					var pack_id = $(this).children('a').children().children().attr("data");
					var pack_info = packInfo.get(pack_id);
					selectPackList.push(pack_info);
				}else if(a.hasClass("select")==true){
					a.removeClass("select");
					var pack_id = $(this).children('a').children().children().attr("data");
					var index = getIndexById(pack_id,selectPackList);
					selectPackList.splice(index,1);
				}
				console.log("selectPackList:"+JSON.stringify(selectPackList));
			});
			
			mui(document.body).on('longtap','.mui-table-view .mui-table-view-chevron li',function(){
				//TODO 需要根据捆包信息重绘捆包信息弹出层 #pop_pack_info
				$("#pop_bg").toggleClass('show');
				$("#pop_pack_info").toggleClass('show');
				var pack_id=$(this).children().children().children('#pack_id').text();
				popWind(pack_id);
			});
			function popWind(pack_id){
				var pack = packInfo.get(pack_id);
				$("#united_pack_id").html(pack.united_pack_id);
				$("#npack_id").html(pack.pack_id);
				$("#label_id").html(pack.label_id);
				$("#nspec").html(pack.spec);
				$("#nputin_weight").html(pack.putin_weight);
				$("#wprovider_desc").html(pack.wprovider_desc);
				$("#nlocation_desc").html(pack.location_desc);
				$("#steel_support_id").html(pack.steel_support_id);
			}
			
			
			mui(document.body).on('tap','#pop_bg',function(){
				$("#pop_bg").toggleClass('show');
				$("#pop_pack_info").toggleClass('show');
			});
			
			//删除  
			mui(document.body).on('tap','#delete',function(){
				if(selectPackList.length > 0){
//					$.each(selectPackList,function(i,item){
//						delPackFromUpload(item);
//					});
					for(var index in selectPackList){
						
						console.log(selectPackList[index].spec);
						if(specNumber[selectPackList[index].spec]){
							specNumber[selectPackList[index].spec] -= 1;
						}
						if(specNumber[selectPackList[index].spec] == 0){
							delete specNumber[selectPackList[index].spec];
						}
						showNumber();
						delPackFromUpload(selectPackList[index]);
						delete selectPackList[index];
					}
				}else{
					mui.alert("请先勾选要删除的已选捆包","提示","确认",function() {}, "div");
				}
				//重绘捆包清单
				showPackList();
			});
			
			//将捆包信息从未扫捆包列表中移动到已扫捆包列表中
	        function delPackFromUpload(pack_info){
	        	console.log("delPackFromUpload pack_info: " + JSON.stringify(pack_info)  + "--------------------------------------");
	        	var packList_up = uploadPack.get(pack_info.voucher_id);
	        	console.log(packList_up + "---------------------------------");
	        	var index_up = getIndexById(pack_info.pack_id,packList_up);
	        	
	        								
	        	//未上传捆包信息中肯定有该单据信息
	        	//直接将捆包信息添加到对应单据下的捆包列表
	        	var list = unUploadPack.get(pack_info.voucher_id);
	        	console.log("list1:"+JSON.stringify(list));
	        	list.push(packList_up[index_up]);
	        	console.log("list2:"+JSON.stringify(list));	
	        	console.log("unUploadPack222:"+JSON.stringify(unUploadPack));	
	        	//将已扫捆包list的捆包信息移除
	        	packList_up.splice(index_up,1);
	        	console.log("after delPackFromUpload : ");
	        	console.log("unUploadPack:" + JSON.stringify(unUploadPack));
	        	console.log("uploadPack" + JSON.stringify(uploadPack));
	        	//修改超发相关信息
	        	modiOutDeliverInfo(pack_info);
	        }
			
			//修改超发相关信息
			function modiOutDeliverInfo(pack_info){
				if(pack_info.advice_style == "20"){//捆包属于形式提单
					//根据订单子项号获取订单子项发货信息
					var voucherSubInfo = contractSubInfo.get(pack_info.voucher_subid);
					console.log("contractSubInfo before:" + JSON.stringify(contractSubInfo));
					//按重量发货
					if (pack_info.price_style=="10") {
						//已出库量
						var act_weight=parseFloat(voucherSubInfo.act_weight);
						//当前捆包重量
						var putin_weight=parseFloat(pack_info.putin_weight);

						voucherSubInfo.act_weight = parseFloat(act_weight - putin_weight);
						contractSubInfo.put(pack_info.voucher_subid,voucherSubInfo);
						console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
					} else if (pack_info.price_style=="20") {////按数量发货
						//已出库数量
						var act_qty = parseFloat(voucherSubInfo.act_qty);
						//当前捆包数量
						var putin_qty = parseFloat(pack_info.putin_qty);

						voucherSubInfo.act_qty = parseFloat(act_qty - putin_qty);
						contractSubInfo.put(pack_info.voucher_subid,voucherSubInfo);
						console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
					}
				}else{
					;
				}
			}
			
			function checkBeforeback(){
				var flag = false;
				if($("#pop_bg").hasClass('show')){
					$("#pop_bg").toggleClass('show');
					$("#pop_pack_info").toggleClass('show');
				}else{
					flag = true;
				}
				return flag;
			}
			
