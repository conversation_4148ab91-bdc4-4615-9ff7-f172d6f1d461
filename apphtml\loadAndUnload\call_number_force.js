/*
 * 定义变量
 **/

var vehicle_no = ""; //车牌号
var car_trace_no = ""; //车辆跟踪号
var hand_point_id = ""; //装卸点代码
var vehicleInfo = new Map(); //车牌信息  Map<vehicle_no,vehicleInfo>
var vehicleList = new Array();
//页面加载时查询强制叫号车辆
window.onload = function onload() {
	mui.plusReady(function() {
		queryQueueVehicleNo();
	});
}

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});
mui.back = function() {
	mui.openWindow({
		id: "load_unload_menus",
		url: "load_unload_menus.html",
		createNew: true
	});
}

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var index = $(e.detail.el).index();
	console.log("选择了：" + index);
	// vehicle_no = e.detail.el.innerText.trim().substr(0, 7); //获取车牌号
	vehicle_no = vehicleList[index].vehicle_no;
	car_trace_no = vehicleInfo.get(vehicle_no).car_trace_no;
	hand_point_id = vehicleInfo.get(vehicle_no).hand_point_id;
	
	console.log("选择了：" + index +",车牌号："+vehicle_no);
});

//叫号按钮绑定事件
mui(document.body).on('tap', '#call_number', function() {
	if (vehicle_no == null || vehicle_no == "") {
		mui.alert("请选择车牌号", "提示", "确定", null, 'div');
		return;
	} else {
		mui("#call_number").button("loading");
		console.log(vehicle_no);
		CallNumberForce();
	}
});

//刷新
mui(document.body).on('tap', '#refresh',
	function() {
		queryQueueVehicleNo();
	});

//强制叫号查询
function queryQueueVehicleNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //PDABoardVehicleService
	//var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDABoardVehicleService';
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "queryQueueVehicleNo";
	console.log(method + "参数：" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(method + "返回：" + JSON.stringify(data));
		if (data != null) {
			//先清空当前列表中的牌号信息
			var lihtml = "";
			$("#companyList").html(lihtml);
			vehicleList = data.returnList;
			if (data.returnList.length > 0) {
				$.each(data.returnList, function(i, item) {
					vehicleInfo.set(item.vehicle_no, item);
					if (item.hand_point_subname == '' || item.hand_point_subname == null) {
						lihtml = lihtml +
							'<li class="mui-table-view-cell" >' +
							'<a class="mui-navigate-right">' +
							'<div style="width: 100%; float: left; text-align: left;" >' +
							'<label style="color: blue;">' + item.vehicle_no + '</label>' +
							'</div>' +
							'<div style="width:100%; float: left; text-align: left;">' +
							'<label style="color: red;">' + item.hand_business_name + '</label></label>/<label style="color: red;">' +
							item.hand_point_name + '</label>' +
							'</div>' +
							'</a>' +
							'</li>';
					} else {
						lihtml = lihtml +
							'<li class="mui-table-view-cell" >' +
							'<a class="mui-navigate-right">' +
							'<div style="width: 100%; float: left; text-align: left;" >' +
							'<label style="color: blue;">' + item.vehicle_no + '</label>' +
							'</div>' +
							'<div style="width:100%; float: left; text-align: left;">' +
							'<label style="color: red;">' + item.hand_business_name + '</label>/<label style="color: red;">' + item.hand_point_name +
							'</label>/<label style="color: red;">' + item.hand_point_subname + '</label>' +
							'</div>' +
							'</a>' +
							'</li>';
					}

				});
				$("#companyList").html(lihtml);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//叫号
function CallNumberForce() {
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var userId = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //PDABoardVehicleService
	//var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDABoardVehicleService';
	var params = '{"seg_no":"' + segNo + '","modi_person":"' + userId + '","vehicle_no":"' + vehicle_no +
		'","car_trace_no":"' + car_trace_no + '","hand_point_id":"' + hand_point_id + '"}';
	console.log("canshu =====" + params);
	params = encodeURI(params, 'utf-8');
	var method = "exeCallNumberForce";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if (data != null) {
			mui("#call_number").button("reset");
			if (data.returnStatus == "1") {
				mui.alert("操作成功", "提示", "确定", function() {
					mui.openWindow({
						id: "load_unload_menus",
						url: "load_unload_menus.html",
						createNew: true
					});
				}, 'div');
				return;
			} else {
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui("#confirm").button("reset");
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
