/*
 * 定义全局变量
 */
var webServiceUrl = localStorage.getItem("webServiceUrl");
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account"); //采用localStorage存储数据

//查询条件初始化
var q_voucher_id = ""; //提单号
var q_customer_name = ""; //客户名称

var hand_type; //装卸类型
var vehicle_id; //车牌号
var customer_id; //承运商代码
var customer_name; //承运商名称
var car_arrive_time; //车辆到达时间
var deliver_type; //交货方式
var driver_name; //司机名称
var id_card; //身份证号
var phone_number; //手机号
var voucherinfo = new HashMap(); //物流提单信息
var selectVoucherList = new Array(); //已选物流提单
var queryVoucherList = new Array();

var allocate_vehicle_id; //配车单号
var patch_flag; //加单标记
/*
 * 页面初始化
 */
window.onload = function onload() {
	mui.plusReady(function() {
		self = plus.webview.currentWebview();
		hand_type = self.hand_type;
		vehicle_id = self.vehicle_id;
		customer_id = self.customer_id;
		customer_name = self.customer_name;
		car_arrive_time = self.car_arrive_time;
		deliver_type = self.deliver_type;
		driver_name = self.driver_name;
		id_card = self.id_card;
		phone_number = self.phone_number;
		patch_flag = self.patch_flag;
		allocate_vehicle_id = self.allocate_vehicle_id;
		console.log(allocate_vehicle_id + patch_flag + "》》" + driver_name);
	});
}

//绑定单据点击事件
mui(document.body).on('tap', 'li', function() {
	var a = $(this).children('a');
	var voucher_id = a.children('div').children('div').children('span').html().trim();
	var voucher = voucherinfo.get(voucher_id);
	//console.log(voucher_id + ">>>>>>>>>>>>" + voucher);
	if(a.hasClass("select") == false) {
		a.addClass("select");
		selectVoucherList.push(voucher);
	} else if(a.hasClass("select") == true) {
		a.removeClass("select");
		//删除已选单据
		delSelectVoucherList(voucher);
	}
});

//删除已选单据
function delSelectVoucherList(voucher) {
	var index;
	$.each(selectVoucherList, function(i, item) {
		if(item.logistics_id == voucher.logistics_id) {
			index = i;
			return false;
		}
	});
	selectVoucherList.splice(index, 1);
}

//查询
mui(document.body).on('click', '#btn_query', function() {
	exeQueryLogisticsPlan();
});

/**
 * 查询物流计划提单
 */
function exeQueryLogisticsPlan() {
	q_voucher_id = $("#voucher_id").val(); //物流计划号
	q_customer_name = $("#customer_name").val(); //提单号
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeQueryLogisticsPlan";
	var params = '{"seg_no":"' + segNo + '","voucher_id":"' + q_voucher_id + '","customer_name":"' + q_customer_name + '"}';
	console.log(params)
	params = encodeURIComponent(params);
		
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		cache: false,
		success: function(data) {
			console.log(JSON.stringify(data.resultDesc));
			if(data != null) {
				var voucherHtml = "";
				if("1" == data.resultStatus) {
					$.each(data.resultDesc, function(i, item) {
						voucherinfo.put(item.voucher_num, item);
						voucherHtml = voucherHtml + '<li class="mui-table-view-cell">' +
							'<a class="mui-navigate-right">' +
							'<div>' +
							'<div class="row"><span id="voucher_num">' + item.voucher_num + '</span><span class="icon">量</span>' + item.trans_weight + '</div>' +
							'<div class="row"><span class="icon">客</span>' + item.customer_name + '</div>' +
							'</div>' +
							'</a>' +
							'</li>';
					});
				} else {
					mui.toast("没有查询到相应物流提单");
				}
				$("#voucher_list").html(voucherHtml);
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//保存
mui(document.body).on('click', '#btn_save', function() {
	if(null == selectVoucherList || "" == selectVoucherList) {
		mui.alert("请选择物流提单进行保存！");
		return false;
	}
	mui("#btn_save").button('loading');
	if("1" == patch_flag) {
		exeAddAllocateVehicleOrder();
	} else {
		exeSaveAllocateVehicle();
	}
});

/**
 * 保存配车单主子项
 */
function exeSaveAllocateVehicle() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeSaveAllocateVehicle";
	var params = JSON.stringify({
		seg_no: segNo,
		user_id: user_id,
		deliver_type: deliver_type,
		customer_id: customer_id,
		vehicle_id: vehicle_id,
		driver_name: driver_name,
		id_card: id_card,
		phone_number: phone_number,
		car_arrive_time: car_arrive_time,
		hand_type: hand_type,
		selectVoucherList: selectVoucherList,
		allocate_scope: '10',
	});
	console.log(params);
	params = encodeURI(params, 'utf-8');
	console.log(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		success: function(data) {
			if(data != null) {
				if(data.resultStatus == "1") {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						mui.openWindow({
							id: "allocate_vehicle_menus",
							url: "allocate_vehicle_menus.html"
						});
					}, 'div');
					return;
				} else {
					mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
			} else {
				mui.toast("保存失败，请稍后再试！");
			}
		},
		error: function() {
			console.log("失败");
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

/**
 * 保存配车单主子项
 */
function exeAddAllocateVehicleOrder() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeAddAllocateVehicleOrder";
	var params = JSON.stringify({
		seg_no: segNo,
		user_id: user_id,
		selectVoucherList: selectVoucherList,
		allocate_vehicle_id: allocate_vehicle_id,
		allocate_scope: '10',
	});
	params = encodeURI(params, 'utf-8');
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		success: function(data) {
			if(data != null) {
				if(data.resultStatus == "1") {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						mui.openWindow({
							id: "allocate_vehicle_menus",
							url: "allocate_vehicle_menus.html"
						});
					}, 'div');
					return;
				} else {
					mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
			} else {
				mui.toast("保存失败，请稍后再试！");
			}
		},
		error: function() {
			console.log("失败");
			mui.toast("网络超时，请稍后再试！");
		}
	});
}