/**
 * 初始化变量信息 
 */
var opt_type = ""; //单据类型(备用暂时没用)
var open_from_url = ""; //通过哪个页面打开该页面
var open_from_id = ""; //通过哪个页面打开该页面
var putoutVoucherList = new Array(); //已选出库单据
var voucher_count_max; //扫描单据上限数(页面传值)
var queryVoucherList = new Array(); //待选出库单据列表
var selectVoucherList = new Array(); //已选出车辆
var voucherinfo = new HashMap();
var query_print_batch_id_list = 0; //是否从仓储过来
var voucherinfo = new HashMap();
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");


$(function() {
	mui.init({
		swipeBack: true, //启用右滑关闭功能
	});
});
(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);
window.onload = function onload() {
	mui.plusReady(function() {
		open_from_url = plus.webview.currentWebview().open_from_url;
		open_from_id = plus.webview.currentWebview().open_from_id;
		opt_type = plus.webview.currentWebview().opt_type;
		voucher_count_max = plus.webview.currentWebview().voucher_count_max;
		putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
		queryVoucherList = plus.webview.currentWebview().queryVoucherList;
		query_print_batch_id_list = plus.webview.currentWebview().query_print_batch_id_list;
		console.log("open_from_url:" + open_from_url +
			" open_from_id:" + open_from_id +
			" opt_type:" + opt_type +
			" voucher_count_max:" + voucher_count_max +
			" query_print_batch_id_list:" + query_print_batch_id_list +
			" putoutVoucherList:" + JSON.stringify(putoutVoucherList) +
			" queryVoucherList:" + JSON.stringify(queryVoucherList));
		//添加单据信息到ul中
		showVoucherList(queryVoucherList);
		//tangli add 出库单据默认全部勾选
/* 		if (query_print_batch_id_list == 1) {

			if (queryVoucherList.length > voucher_count_max) {
				mui.alert("已扫描单据数已达到上限:" + voucher_count_max);
			} else {
				//将单据默认全部勾选  
				$.each(queryVoucherList, function(i, item) {
					var a = $('li').children('a');
					//新增已选单据
					if (addSelectVoucherList(item.voucher_id, item)) {
						a.addClass("select");
					}
				});
			}
		} */
	});
}

function showVoucherList(putoutVoucherList) {
	if (putoutVoucherList.length > 0) {
		var html = "";
		$.each(putoutVoucherList, function(i, item) {
			voucherinfo.put(item.voucher_id, item);
			var pli_thid = "";
			var pli_thname = "";
			if (item.target_hand_point_id == "" || item.target_hand_point_id == null) {
				if (item.current_hand_point_id == "" || item.current_hand_point_id == null) {
					pli_thid = " ";
					pli_thname = " ";
				} else {
					pli_thid = item.current_hand_point_id;
					pli_thname = item.current_hand_point_name;
				}
			} else {
				pli_thid = item.target_hand_point_id;
				pli_thname = item.target_hand_point_name;
			}
			html = html + '<li class="mui-table-view-cell">' +
				'<a class="mui-navigate-right">' +
				'<div>' +
				'<div class="row"><span id="voucher_num">' + item.vehicle_no + '</span></div>' +
				'<div class="row"><span class="icon"></span>' + pli_thname + '</div>' +
				//'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
				'</div>' +
				'</a>' +
				'</li>';
		});
		$("#voucher_list").html(html);
	} else {
		$("#voucher_list").html("");
	}
}

//绑定单据点击事件
mui(document.body).on('tap', 'li', function() {
	var a = $(this).children('a');
	if (a.hasClass("select") == false) {
		var voucher_id = a.children('div').children('div').children('span').html().trim();
		var voucher = voucherinfo.get(voucher_id);
		if (addSelectVoucherList(voucher_id, voucher)) {
			a.addClass("select");
		}
		JSON.stringify(a);
	} else if (a.hasClass("select") == true) {
		a.removeClass("select");
		var voucher_id = a.children('div').children('div').children('span').html().trim();
		//删除已选单据
		delSelectVoucherList(voucher_id);
	}
	console.log("selectVoucherList.length:" + selectVoucherList.length + JSON.stringify(selectVoucherList));
});

//添加单据信息
function addSelectVoucherList(voucher_id, voucher_info) {
	//校验单据是否已在putoutVoucherList中存在    select本身根据样式区分不用校验
	var index = getIndexByVoucherId(voucher_id, putoutVoucherList);
	console.info("indexindexindexindexindex:::::::" + index);
	if (index != -1) {
		mui.alert(voucher_id + "该单据已经在已扫单据列表中,不能重复添加", "提示", "确认", function() {}, "div");
		return false;
	} else {
		//可扫单据数量校验
		if ((putoutVoucherList.length + selectVoucherList.length) == voucher_count_max) {
			mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
			return false;
		} else {
			var query_index = getIndexByVoucherId(voucher_id, queryVoucherList);
			selectVoucherList.push(queryVoucherList[query_index]);
			return true;
		}
	}
}

//查询putoutVoucherList记录 返回index
function getIndexByVoucherId(voucher_id, voucherList) {
	var index = -1;
	$.each(voucherList, function(i, item) {
		if (item.vehicle_no == voucher_id) {
			index = i;
			return false;
		}
	});
	return index;
}

//删除信息
function delSelectVoucherList(voucher_id) {
	var query_index = getIndexByVoucherId(voucher_id, selectVoucherList);
	selectVoucherList.splice(query_index, 1);
}

//未装离厂
mui(document.body).on('tap', '#confirm', function() {
	
 	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	if (!confirm("车辆确定要未装离厂吗？")) {
		return false;
	} 

	
 	$.each(selectVoucherList, function(i, item) {

		var vehicle_id = item.vehicle_no;
		var car_trace_no = item.car_trace_no;
		console.log(vehicle_id + car_trace_no);


		var outUri = domainName + "webService.jsp?callback=?";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
		//var vehicle_id = $("#vehicle_id").val();
		//var car_trace_no = $("#car_trace_no").val();
		var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","car_trace_no":"' + car_trace_no + '","user_id":"' + user_id + '"}';
		var method = "exeLeaveUnloadCar";
		console.log("exeLeaveUnloadCar:" + params);
		params = encodeURI(params, 'utf-8');
		$.getJSON(outUri, {
			innerUri: innerUri,
			params: params,
			method: method
		}, function(data) {
			console.log(data + "123123123123" + JSON.stringify(data));
			//如返回对象有一个username属性
			if (data != null) {
				if (data.resultStatus == "1") {	
					//循环到最后一个执行刷新未装离场页面 获取最新数据
					var le = selectVoucherList.length-1;					
					if(le == i){
						queryEnterFacoryVehicleNo();
					}
					return;									
				} else {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						queryEnterFacoryVehicleNo();						
					}, 'div');
					return;
				}
			} else {
				mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
				return;
			}
		});
	});	
	
});

function queryEnterFacoryVehicleNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var vehicle_id = "";
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","hand_point_id":""}';
	var method = "exeQueryVehicleId2";
	console.log("queryEnterFacoryVehicleNo:" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			//console.log("data:" + JSON.stringify(data));
			console.log("data:" + data.codeList.length);
 			if(data.codeList.length == 0) {				
				mui.alert('', "没有查询到单据信息");
				return false;			
			} else { 
				queryVoucherList = data.codeList;
				//刷新页面数据
				showVoucherList(queryVoucherList);
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//查询车牌号
function queryVehicleId(queryType) {
	console.info("查询车牌号。。。。。。。。。");
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	if(queryType == '20') {
		vehicle_id = $("#vehicle_id").val();
	} else {
		vehicle_id = "";
	}
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","hand_point_id":"' + hand_point_id + '"}';
	var method = "exeQueryVehicleId2";
	//console.log("queryVehicleId:"+params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			$("#vehicle").html(" ");
			if(data.codeList.length == 0) {
				//mui.alert("该装卸点下未查询到车牌号信息。", "提示", "确定", function() {
				$("#vehicle_id").val("");
				$("#vehicle_id").focus();
				//}, 'div');
				return;
			} else {
				var html = "";
				if(queryType == '20') {
					$.each(data.codeList, function(i, item) {
						/*html=html+'<button type="button" class="mui-btn"  p_id="'+item.vehicle_no+'" p_no="'+item.car_trace_no+'">'+item.vehicle_no+'</button>';*/
						$("#vehicle_id").val(item.vehicle_no);
						$("#car_trace_no").val(item.car_trace_no);
					});
				} else {
					if(data.codeList.length > 0 && segNo == "00126") { //佛山宝钢个性化，要求页面加载将车辆信息填充在车牌号上
						console.log(data.codeList.length + ">>>>>>>>>>>>>>>>>>>>>>>" + data.codeList[0].vehicle_no);
						$("#vehicle_id").val(data.codeList[0].vehicle_no);
						$("#allocate_vehicle_id").val(data.codeList[0].allocate_vehicle_id);
						$("#allocate_scope").val(data.codeList[0].allocate_scope);
						$("#car_trace_no").val(data.codeList[0].car_trace_no);
						$("#check_type").val(data.codeList[0].check_type);
						if($("#check_type").val() == "10") { //装货
							$("#start_unload").hide();
							$("#start_load").show();
						} else if($("#check_type").val() == "20") { //卸货
							$("#start_load").hide();
							$("#start_unload").show();
						} else {
							$("#start_load").show();
							$("#start_unload").show();
						}
					}
					$.each(data.codeList, function(i, item) {
						html = html + '<button type="button" class="mui-btn" style="width:31%;margin:1px ;" p_id="' + item.vehicle_no + '" p_no="' + item.car_trace_no + '" p_check="' + item.check_type + '" p_allv="' + item.allocate_vehicle_id + '">' + item.vehicle_no + '</button>';
					});
				}
				$("#vehicle").html(html);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}