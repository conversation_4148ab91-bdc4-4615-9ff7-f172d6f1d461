/**
 * 变量定义
 */
var productionOrderCode = "";//工单号
var partId = "";//零件号
var packId = "";//捆包号
var customerId = "";//客户代码
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var startfrom = "";
var putin_method_switch = "";

var transfer_flag = "";
var packId_null = "";
mui.init({
	swipeBack: true //启用右滑关闭功能
});
var quality = localStorage.getItem("quality");
var html = "";
$(function() {	
	if(quality == '1'){
	    html = html + '<i class="icon-search"></i>' +		
		'<input id="productionOrderCode" onchange="kunbaoChange()" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="工单号" />';		
		$("#zhiliangjiance").html(html);		
    }else{		
		html = html + '<i class="icon-search"></i>' +				
			'<input id="kunbao" onchange="kunbaoChange()" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="捆包号" />';		
		$("#zhiliangjiance").html(html); 
	}
});

//输入捆包号或者工单号后触发查询方法
function kunbaoChange(){
			
		if(quality == '1'){
			
			//查询工单号
			productionOrderCode = document.getElementById("productionOrderCode").value.replace( /^\s*/, '');
			
			//模糊查询工单号
			toWebServicesMoHu(productionOrderCode);
			
		}else{
			//查询捆包
			packId = document.getElementById("kunbao").value.replace( /^\s*/, '');
			if(null != packId && "" != packId){
				toWebPackServices(packId);
				html = "";
				html = html + '<i class="icon-search"></i>' +
					'<input id="kunbao" onchange="kunbaoChange()" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="捆包号" />'+
					'<input id="productionOrderCode" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="工单号" />';		
				$("#zhiliangjiance").html(html);
			}else{
				alert("捆包号不能为空！");
			}
		}
	
	
}

//捆包号扫描
$("#pack_id").keypress(function(e) {
	if(e.keyCode == 13) {
		packId = $("#kunbao").val();
		if(packId == "") {
			mui.alert("捆包号不能为空", " ", "确定", function() {}, 'div');
			$("#kunbao").val("");
			$("#kunbao")[0].focus();
			return false;
		}
		toWebPackServices(packId);
			html = "";
			html = html + '<i class="icon-search"></i>' +
				'<input id="kunbao" onchange="kunbaoChange()" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="捆包号" />'+
				'<input id="productionOrderCode" type="text" style="height: 50px;width: 94%;margin-left: 3%;font-size: 20px;" placeholder="工单号" />';		
			$("#zhiliangjiance").html(html);
		
	}
})

//工单号扫描
$("#productionOrderCode").keypress(function(e) {
	if(e.keyCode == 13) {
		productionOrderCode = $("#productionOrderCode").val();
		if(productionOrderCode == "") {
			mui.alert("捆包号不能为空", " ", "确定", function() {}, 'div');
			$("#productionOrderCode").val("");
			$("#productionOrderCode")[0].focus();
			return false;
		}
		toWebServices(productionOrderCode);
	}
})

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	spec = el_J.find("#spec").text();
	partId = el_J.find("#partId").text();
	packId = el_J.find("#packId").text();
	productionOrderCodeMoHu = el_J.find("#productionOrderCodeMoHu").text();	
	if("" != productionOrderCodeMoHu){
		toWebServices(productionOrderCodeMoHu);
	}
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	//判断是否选择仓库
	if(null == spec || "" == spec) {
		mui.alert("请选择规格", " ", "确定", function() {}, 'div');
		return false;
	}	
	localStorage.setItem("partId", partId);
	localStorage.setItem("umcLoginName", "admin" );
	localStorage.setItem("segNo", segNo );
	localStorage.setItem("customerId", customerId );
	mui.openWindow({
		url: 'product_quality_information.html',
		id: 'index',
		createNew: true
	});
});
//成品质量检测
function toWebServices(productionOrderCode) {
		$("#productionOrderCode").val(productionOrderCode);
		$("#kunbao").val(packId);
		localStorage.setItem("productionOrderCode", productionOrderCode);
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo ,
			productionOrderCode: productionOrderCode
		};
		params = JSON.stringify(params);		
		var method = "queryQualityByOrder";
		console.log("params" + params);
		$.ajax({ 
			type: "post", 
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json", 
			success: function(data) {    
				console.log(JSON.stringify(data));
				 if(null != data){//查询成功
					if(data.returnValue == "1" && data.orderOutputTotal == "1") { 
						$.each(data.orderOutputList, function(i, item) {
							localStorage.setItem("productionOrderCode", item.productionOrderCode);
							localStorage.setItem("partId", item.partId);
							localStorage.setItem("umcLoginName", "admin" );
							localStorage.setItem("segNo",segNo);
						});
						mui.openWindow({
							url: 'product_quality_information.html',
							createNew: true
						});
					}else if(data.returnValue == "1" && data.orderOutputTotal == "0"){
						alert("没有找到当前工单："+productionOrderCode);
					}else{
							//console.log(data.orderOutputList.length);	
							if(data.orderOutputList.length > 0) { 
							var chtml = ""; 
							$.each(data.orderOutputList, function(i, item) {
							chtml = chtml + '<li class="mui-table-view-cell">' +
									'<a class="mui-navigate-right">' +
									'<div>' + 
									'<label id="spec">规格：'+item.spec+'</label>' +
									'</div>' +
									'<div id="partId1">' +
									'零件号：<label id="partId">'+item.partId+'</label>' +
									'</div>' +
									'<div style= "display:none">捆包号：' +
									'<label id="packId">'+item.packId+'</label>' +
									'</div>' +
									'</a>' +
									'</li>';
							});
							$("#companyList").html(chtml);
							$("#companyList1").html("");
						} 				
					} 
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
} 
//原料质量检测
function toWebPackServices(packId) {
		//var packId = "PACKPEIPEI04";//localStorage.getItem("account"); //采用localStorage存储数据
		localStorage.setItem("packId", packId);
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo ,
			packId: packId
		};
		params = JSON.stringify(params);//queryBookSetting  queryQualityCheckPlus
		//igc.t_g_quality_check_plus saveQualityCheckPlus
		var method = "queryOrderByPackId";
		console.log("params" + params);
		$.ajax({ 
			type: "post", 
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json", 
			success: function(data) {    
				console.log(JSON.stringify(data));
				if(null != data){//查询成功
					if(data.returnValue == "1") {//这里加上零件号和客户代码的显示框
						//toWebServices(data.productionOrderCode);
						var chtm2 = "";
						chtm2 = chtm2 + '<li class="mui-table-view-cell">' +
								'<a class="mui-navigate-right">' +
								'<div id="partId1">' +
								'原料零件号：<label id="partId">'+data.packInfo.partId+'</label>' +
								'</div>' +
								'<div id="customerId">客户代码：' +
								'<label id="customerId">'+data.packInfo.customerId+'</label>' +
								'</div>' +
								'</div>' +
								'<div id="spec">规格：' +
								'<label id="spec">'+data.packInfo.spec+'</label>' +
								'</div>' +
								'</a>' +
								'</li>';
						$("#companyList").html(chtm2);
						$("#companyList1").html("");
						$("#productionOrderCode").val(data.packInfo.productionOrderCode);
						$("#kunbao").val(data.packInfo.packId);
						customerId  = data.packInfo.customerId;
					}else{
						alert(data.returnDesc); 
					}
				}else {
					alert(data.returnDesc); 
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
}

//工单模糊查询
function toWebServicesMoHu(productionOrderCode) {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo ,
			productionOrderCode: productionOrderCode,
			umcLoginName :user_id
		};
		params = JSON.stringify(params);		
		var method = "queryProcessOrderInfo";
		console.log("params" + params);
		$.ajax({ 
			type: "post", 
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json", 
			success: function(data) {    
				console.log(JSON.stringify(data));
				 if(null != data){//查询成功
							if(data.pdaProcessOrderInfoList.length > 0) { 
								var chtml = ""; 
								$.each(data.pdaProcessOrderInfoList, function(i, item) {
								chtml = chtml + '<li class="mui-table-view-cell">' +
										'<a class="mui-navigate-right">' +
										'<div>' + 
										'<label id="productionOrderCodeMoHu">'+item.productionOrderCode+'</label>' +
										'</div>' +
										'</a>' +
										'</li>';
								});
								$("#companyList1").html(chtml);
								$("#companyList").html("");
							}else{
								alert("未查询到工单！");
							} 				
					} 
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		});
} 