/*
 * 定义全局变量
 */
var webServiceUrl = localStorage.getItem("webServiceUrl");
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account"); //采用localStorage存储数据

//查询条件初始化
var pack_id; //捆包号
var wprovider_id; //仓库代码
var factory_product_id; //钢厂资源号
var customer_id; //客户代码
var part_id; //零件号
var AllocateList; // 主项信息
var pre_list_id; //主项清单号

var voucherinfo = new HashMap(); //子项捆包信息
var selectVoucherList = new Array(); //已选捆包信息


/*
 * 页面初始化
 */
window.onload = function onload() {
	mui.plusReady(function() {
		self = plus.webview.currentWebview();
		AllocateList = self.AllocateList;
		console.log(JSON.stringify(AllocateList))
		part_id = AllocateList.part_id;
		customer_id = AllocateList.customer_id;
		pre_list_id = AllocateList.pre_list_id;
	});
}


//查询
mui(document.body).on('click', '#btn_query', function() {
	exeQueryLogisticsPlan();
});

/**
 * 查询子项捆包信息
 */
function exeQueryLogisticsPlan() {
	pack_id = $("#pack_id").val(); //捆包号
	wprovider_id = $("#wprovider_id").val(); //仓库代码
	factory_product_id = $("#factory_product_id").val(); //钢厂资源号
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeQueryYouniWorkDDataPack";
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '","factory_product_id":"' + factory_product_id +
		'","part_id":"' + part_id + '","customer_id":"' + customer_id + '","wprovider_id":"' + wprovider_id + '"}';
	console.log(params)
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(data) {
			console.log(JSON.stringify(data.resultDesc));
			if (data.resultDesc != '' && data.resultDesc != null) {
				var voucherHtml = "";
				$.each(data.resultDesc, function(i, item) {
					item.pre_list_id = pre_list_id;
					item.seg_no = segNo;
					item.user_id = user_id;
					voucherinfo.put(item.pack_id, item);
					voucherHtml = voucherHtml + '<div class="mui-input-row mui-checkbox"><li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div><input name="checkbox1" class="checkboxes" value="' + item.pack_id + '" type="checkbox">' +
						'<div class="row"><span id="voucher_num">' + item.pack_id + '</span>' +
						'<div class="row"><span class="icon">重</span>' + item.putin_weight.toFixed(3) +
						'<span class="icon" style="margin-left: 30px;">件</span>' + item.putin_qty +
						'<span class="icon" style="margin-left: 30px;">钢</span>' + item.factory_product_id + '</div>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
			} else {
				mui.toast("没有查询到相应子项捆包信息");
			}
			$("#voucher_list").html(voucherHtml);
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//保存
mui(document.body).on('click', '#btn_save', function() {
	selectVoucherList = new Array(); //已选捆包信息
	var rdsObj = document.getElementsByClassName('checkboxes');
	for (i = 0; i < rdsObj.length; i++) {
		var checkVal = "";
		if (rdsObj[i].checked) {
			selectVoucherList.push(voucherinfo.get(rdsObj[i].value));
		}
	}
	if (null == selectVoucherList || "" == selectVoucherList) {
		mui.confirm('请勾选要保存的捆包信息！', '提示', ['确认'], function(e) {
		}, 'div');
		return false;
	} else {
		mui.confirm('是否保存选中记录？', '提示', ['确认', '取消'], function(e) {
			if (e.index == 0) {
				mui("#btn_save").button('loading');
				exeSaveAllocateVehicle();
			}
		}, 'div');
	}
});

/*
 * 新增子项
 */
function exeSaveAllocateVehicle() {
	console.log(JSON.stringify(selectVoucherList))
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeAddYouniWorkDDataPack";
	var params = JSON.stringify({
		selectVoucherList: selectVoucherList
	});
	console.log(params);
	params = encodeURI(params, 'utf-8');
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(data) {
			if (data.status == "1") {
				mui.confirm(data.desc, '提示', ['确认'], function(e) {
					if (e.index == 0) {
						mui.openWindow({
							url: 'pda_youni_work.html',
							id: "pda_youni_work",
							createNew: true
						});
					}
				}, 'div');
			} else {
				mui.alert(data.desc);
			}
			mui("#btn_save").button('reset');
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
			mui("#btn_save").button('reset');
		}
	});
}
