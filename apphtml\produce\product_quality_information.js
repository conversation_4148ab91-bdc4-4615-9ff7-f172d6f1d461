var segNo = localStorage.getItem("segNo");
var productionOrderCode = localStorage.getItem("productionOrderCode");
var partId = localStorage.getItem("partId");
var packId = localStorage.getItem("packId");
var umcLoginName = localStorage.getItem("umcLoginName");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var scanPackList = new Array(); //已扫描捆包列表
var ownPackInfoList = new Array(); //接收捆包查询结果
var checkstatus = "0"; // 判断扫描捆包是否为扫描捆包列表信息中
var quality = localStorage.getItem("quality");
var customerId = localStorage.getItem("customerId");

//mui.back()
mui.init({
	swipeBack: true //启用右滑关闭功能
});
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

/* mui.back = function() {
	mui.openWindow({
		url: 'produce_menus.html',
		id: "produce_menus",
		createNew: true
	});
} */

$(function() {
	if(quality == '1'){
		var html = "";
	    html = html + '<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>' +		
		'<h4 class="mui-title">成品质量信息录入</h4>';		
		$("#zhiliangxinxi").html(html);	
		//成品调用webservices接口
	}else{
		var html = "";
		html = html + '<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>' +		
		'<h4 class="mui-title">原料质量信息录入</h4>';		
		$("#zhiliangxinxi").html(html);		
	}
	//调用webservices接口
	toWebServices();	
});
var jishu;
var qualityCheckPlusList = new Array();
var qualityCheckRmList = new Array();
//成品信息录入
mui(document.body).on('tap', '#save', function() {
	//var inputValue = document.getElementById("checkValue0").value;	
	for(i = 0; i <= jishu; i++){
		if(quality == '1'){
			qualityCheckPlusList[i].checkValue = document.getElementById("checkValue"+i).value;
		}else{
			qualityCheckRmList[i].checkValue = document.getElementById("checkValue"+i).value;		
		}
	}
	//console.log("qualityCheckPlusList:" + JSON.stringify(qualityCheckPlusList));
	exeOwnGoodVehicleOut();
});

// 提交
function exeOwnGoodVehicleOut() {	    
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		if(quality == '1'){
			var params = {
				segNo: segNo,
				productionOrderCode: productionOrderCode,
				umcLoginName : umcLoginName,
				qualityCheckPlusList : qualityCheckPlusList
			};
		}else{
			var params = {
				segNo: segNo ,
				productionOrderCode: productionOrderCode,
				packId : packId,
				umcLoginName : umcLoginName,
				qualityCheckRmList : qualityCheckRmList
			};
		}	
		
		//checkValue 这个是你修改的值 packId这是原料捆包号 productionOrderCode工单号 
		//customerId是从产品规格信息里带过来的 checkItem就是settingId字段，
		params = JSON.stringify(params);
		if(quality == '1'){
			var method = "saveQualityCheckPlus";
		}else{
			var method = "saveQualityCheckRM ";
		}	
		
		console.log("params" + params);
		$.ajax({ 
			type: "post", 
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace 
			},
			dataType: "json", 
			success: function(data) {    
				console.log(JSON.stringify(data));  
				 if(null != data){//查询成功
				    alert(data.returnDesc); 
				 } else {
					alert(data.returnDesc); 
				 } 
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
}
function toWebServices() {
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		if(quality == '1'){			
			var params = {
				segNo: segNo ,
				productionOrderCode: productionOrderCode,
				partId : partId,			
				umcLoginName : umcLoginName 
			};
		}else{
			var params = {
				segNo: segNo ,
				productionOrderCode: productionOrderCode,
				partId : partId,	
				packId : packId,
				customerId : customerId,
				umcLoginName : umcLoginName 
			};
		}	
		
		params = JSON.stringify(params);
		if(quality == '1'){
			var method = "queryBookSetting";
		}else{
			var method = "queryBookSettingRM";
		}		
		console.log("params" + params);
		$.ajax({ 
			type: "post", 
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace 
			},
			dataType: "json", 
			success: function(data) {    
				console.log(JSON.stringify(data));  
				 if(null != data){//查询成功
						if(quality == '1'){
						    //console.log(data.orderOutputList.length);	
							if(data.prbookSettingList.length > 0) { 
							var chtml = ""; 
							$.each(data.prbookSettingList, function(i, item) {
							    chtml = chtml + '<tr>' +
									'<td width="110" align="center">'+item.settingName+'</td>' +
									'<td width="250">' +
									'<input id="checkValue'+i+'" type="number"  style="height: 45px; width: 80%; font-size: 20px;margin-bottom: 3px;margin-top: 3px;margin-left:5px;" value="'+item.checkValue+'"/>' +
									'</td>' +
									'</tr>' ;
									jishu = i;
									var scanPackInfo = {};
									scanPackInfo.seq_no = item.seqNo; 
									scanPackInfo.checkValue = item.checkValue;
									scanPackInfo.customerId = item.customerId;
									scanPackInfo.partId = item.partId;
									scanPackInfo.settingId = item.settingId;
									scanPackInfo.settingName = item.settingName;
									qualityCheckPlusList.push(scanPackInfo);
							});
							$("#tablezhiliang").html(chtml);  
						    } 	 
						}else{
							//console.log(data.orderOutputList.length);
							if(data.prbookSettingRmList.length > 0) { 
							var chtml = ""; 
							$.each(data.prbookSettingRmList, function(i, item) {
							    chtml = chtml + '<tr align="center">' +
									'<td width="110">'+item.checkItemName+'</td>' +
									'<td width="250">' +
									'<input id="checkValue'+i+'" type="number" style="height: 30px;width: 80%;font-size: 20px;margin-top: 15px;" value="'+item.checkValue+'"/>' +//
									'</td>' +
									'</tr>' ;
									jishu = i;
									var scanPackInfo = {};
									scanPackInfo.checkValue = item.checkValue;
									scanPackInfo.customerId = item.customerId;
									scanPackInfo.partId = item.partId;
									scanPackInfo.checkItem = item.checkItem;
									scanPackInfo.checkItemName = item.checkItemName;
									qualityCheckRmList.push(scanPackInfo); 
							});
							$("#tablezhiliang").html(chtml);
							} 	
						}
				} else {
					alert(data.returnDesc); 
				}
				
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
}

