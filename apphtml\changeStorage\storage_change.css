/** 明细样式 */

#storage_but,
#storage_realloc {
	width: 99%;
	font-size: 22px;
	line-height: 1.8;
	margin: 10px 0px 5px 1px;
}

.detail_row {
	height: 49px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#pack_id,
#pack_location_m,
#pack_location_d {
	width: 70%;
	padding: 0px 5px;
	font-size: 20px;
}

#pack_location_d {
	background-color: #CCCCCC;
}

.pack_list {
	height: 156px;
	overflow: auto;
	border: 1px solid #AAAAAA;
}

.storage_pack {
	width: 60%;
	margin-top: 12px;
	text-align: left;
	padding-left: 4px;
}

.li-text {
	font-size: 20px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 77px;
}

.li-text p span font {
	text-align: center;
	font-size: 20px;
	color: #000000;
}

.item a {
	display: block;
	float: left;
	background-color: #ff0000;
	color: #fff;
	width: 20%;
	margin-left: 0.16rem;
	font-size: 0.14rem;
	text-align: center;
	text-decoration: none;
}

.li-height {
	margin-top: 4px;
	text-align: left;
}


/** 列表样式 */

.pack_location_target {
	color: black;
	font-size: 20px;
	margin-right: 5px;
}

.pack_location_now {
	color: black;
	font-size: 20px;
	margin-top: 8px;
	margin-right: 5px;
}

.mui-btn-red {
	font-size: 22px;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
}

#packLabel {
	background-color: darkblue;
	color: white;
	font-size: 20px;
	margin-right: 2px;
}

#newLocationLabel {
	background-color: orangered;
	color: white;
	font-size: 20px;
	margin-right: 5px;
}

#oldLocationLabel {
	background-color: forestgreen;
	color: white;
	font-size: 20px;
	margin-right: 5px;
}

#ReallocLocationDiv.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#ReallocLocationDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#ReallocLocationDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}