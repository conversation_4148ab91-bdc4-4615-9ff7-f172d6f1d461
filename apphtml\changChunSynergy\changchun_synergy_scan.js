//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ TODO 页面显示结束~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/**
 * 初始化变量信息  
 */
var successFlag = true;
var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
var location_id = "";
var vehicle_id = localStorage.getItem("vehicle_id"); //车辆信息
var wprovider_id = localStorage.getItem("wprovider_id"); //仓库信息
var team_id = localStorage.getItem("team_id");
var selectVehicleNo = localStorage.getItem("putin_vehicle_no") == null ? "" : localStorage.getItem("putin_vehicle_no"); //自动分配开关打开；库位精细化管理。入库钱必须选择车牌号
var productProcessListHtml = ""; //首道加工工序下拉列表
var product_process_id = "";
var productProcessAry = new Array();
var if_alloc_switch = "";
var if_judge_pack_location = "";

var packObj = {};
$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
	//库位输入框获得焦点
	//$("#pack_location_m")[0].focus();
	$("#pack_id")[0].focus();

});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	if(scanPackList == null || scanPackList == "") {
		mui.openWindow({
			url: '../public/index.html',
			id: 'index',
			createNew: false
		});
	} else {
		var btnArray = ['退出', '取消'];
		mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			console.log(JSON.stringify(plus.webview));
			if(e.index == 0) {
				setTimeout(function() {
					var ws = plus.webview.currentWebview();
					plus.webview.close(ws);
					mui.openWindow({
						url: '../public/index.html',
						id: 'index',
						createNew: false
					});
				}, 0);
			}
		}, 'div');
	}
};

//页面加载事件
window.onload = function onload() {
	mui.plusReady(function() {
		queryAllocStorageCCSwitch();

		/*
		 * 选择仓库及车辆信息
		 */
		$("#wprovider_name_span").html("仓库：" + localStorage.getItem("wprovider_name"));
		if(selectVehicleNo != "") {
			$("#vehicle_no_span").html("&nbsp;车牌：" + selectVehicleNo);
		}
		if(vehicle_id != "") {
			$("#vehicle_no_span").html("&nbsp;车牌：" + vehicle_id);
		}
		console.log("进入页面加载信息>>>>>>>>>>>>>>>>" + localStorage.getItem("wprovider_name") + "--------------" + selectVehicleNo + "<<<<<<<<<<<<<<<<" + vehicle_id + ">>>>>>>>>>>>>" + if_alloc_switch);
		queryProductProcessId();
	});
}

//智慧仓库add by wangshengbo171211
function setDefaultInputStyle() {
	console.log("setDefaultInputStyle" + if_alloc_switch);
	$("#pack_id")[0].focus(); //自动库位分配光标默认聚焦到捆包号输入框
	setInputReadOnly("#pack_location_m", true);
};

//智慧仓库add by wangshengbo171211
function setInputReadOnly(inputName, isReadOnly) {
	$(inputName).attr("readOnly", isReadOnly);
	if(isReadOnly) {
		$(inputName).css("background-color", "#E0E0E0");
	} else {
		$(inputName).css("background-color", "#FFFFFF");
	}
}

//仓库和车牌选择
mui(document.body).on("tap", ".icon-setting", function() {
	mui.openWindow({
		url: 'select_wprovider_id.html',
		id: 'select_wprovider_id',
		createNew: true
	});
});

mui(document.body).on("tap", ".icon-car", function() {
	mui.openWindow({
		url: 'select_vehicle_no.html',
		id: 'select_vehicle_no',
		extras: {
			open: 'synergy', //扩展参数
		},
		createNew: true
	});
});

/**
 * 列表删除捆包信息
 */
function deleteLi(ele, productId, packId) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '提示', btnArray, function(e) {
		if(e.index == 0) {
			li.parentNode.removeChild(li); //删除DOM节点，
			//但是同时也要删除数据容器
			for(var i = 0; i < scanPackList.length; i++) {
				if(scanPackList[i].product_id == productId && scanPackList[i].pack_id == packId) {
					scanPackList.splice(i, 1);
					break;
				}
			}
			initData();
		}
	}, 'div');
}

function initData() {
	$("#storage_list li").remove();
	console.log(">>>>>>scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
	scanPackList = scanPackList.reverse();
	var loca = "";
	$.each(scanPackList, function(i, item) {
		var li = $(' <li class="mui-table-view-cell mui-media li-text">' +
			' <div class="mui-slider-right mui-disabled">' +
			' <a class="mui-btn mui-btn-red" onclick=' + 'deleteLi(this,"' + item.product_id + '","' + item.pack_id + '")' + '>删除</a>' +
			' </div>' +
			' <div class="mui-slider-handle">' +
			' <div  class="storage_pack mui-pull-left">' +
			'<span id="packLabel">捆</span><label>' + item.pack_id + '</label>' +
			' </div>' +
			' <div class="mui-media-body li-height">' +
			' <div class="pack_location_now">' +
			'<span id ="LocationLabel">库</span><label>' + item.new_location_desc + '</label>' +
			'</div> ' +
			' </div>' +
			' </div>' +
			' <div style="display:none">' +
			' <label id="product_id">' + item.product_id + '</label>' +
			' </div>' +
			' </li>');
		$("#storage_list").append(li);
	});
}

//增加keypress监听
//库位
var pack_location_target = "";
$("#pack_location_m").keypress(function(e) {
	if(e.keyCode == 13) {
		if(if_alloc_switch != "1" && $("#pack_location_m").val() == null) {
			mui.alert("手工指定库位不能为空", "提 示", "确定", null, 'div');
			return;
		}
		//捆包输入框获得焦点
		$("#pack_id")[0].focus();
	}
});

//已扫描捆包列表
var scanPackList = new Array();
$("#pack_id").keypress(function(e) {
	if(e.keyCode == 13) {
		var pack_location_m = $("#pack_location_m").val();
		if(if_alloc_switch != "1" && (pack_location_m == null || pack_location_m == "")) {
			mui.alert("手工库位方式下库位必填", "提示", "确认", function() {}, "div");
			$("#pack_location_m")[0].focus();
			return false;
		}
		var currentVeicleNo = localStorage.getItem("putin_vehicle_no") == null ? "" : localStorage.getItem("putin_vehicle_no");
		if(!currentVeicleNo) {
			mui.alert("先选择车牌号", "提示", "确定", function() {}, "div");
			$("#pack_id").val("");
			$("#pack_id").focus();
			return false;
		}
		//判断捆包是否已经被扫描
		var pack_id = $("#pack_id").val();
		if(pack_id == "") {
			mui.alert("扫描或手工输入的捆包号不能为空。", "提 示", "确定", null, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		//packIfAlreayScaned(pack_id);
		if(selectById(pack_id, scanPackList)) {
			mui.alert("不能扫描重复的捆包号", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		queryLocationChangePack(); //查捆包信息
		/*
					if(if_alloc_switch == "1"){
						setProductProcessListHtml();//弹出首道加工工序选择
		        		pack_id = formatPackId(pack_id);
						$("#pack_id").val(pack_id);
					}else{
						pack_id = formatPackId(pack_id);
						$("#pack_id").val(pack_id);
					   //已扫描列表追加一条捆包记录
						pack_location_target = $("#pack_location_m").val().trim();
						queryLocationChangePack();
					}*/

	}
});

// 增加首道加工工序precessKey 按下事件
$("#product_process_id").keypress(function(e) {
	if(e.keyCode == 13) {
		pack_location_target = $("#pack_location_m").val().trim(); //目标库位，可空
		queryLocationChangePack();
		console.log("首道加工工序precessKey 按下事件后scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
	}
});

// 是否已经扫描
function packIfAlreayScaned(pack_id) {
	console.log("数组 pack_id==========" + pack_id + ",当前扫描pack_id=========" + pack_id);
	var result = false;
	$.each(scanPackList, function(i, value) {
		if(value.pack_id == pack_id) {
			result = true;
			return; //跳出循环  return true 等价于  continue   
		}
	});
	return result;
};

function queryLocationChangePack() {
	mui.plusReady(function() {
		var curNetConnetType = plus.networkinfo.getCurrentType();
		if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
			curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
			console.log("netNotConnect");
			queryPackNetNotConnect();
		} else {
			console.log("netConnect");
			queryPackNetConnect();
		}
	});
};

//无网络连接时，只记录扫描捆包号
function queryPackNetNotConnect() {
	var pack_id = $("#pack_id").val();
	$("#pack_id").val("");
	$("#pack_id").focus();
	var curPackObj = {
		pack_id: pack_id,
		product_id: "",
		scan_time: getnowtime(),
		product_process_id: $("#product_process_id").val(),
		spec: "",
		weight: "",
		location_desc: pack_location_target
	};
	refreshSum(curPackObj);
	scanPackList.push(curPackObj);
	//initData();
};

//有网络环境时，调用接口获取捆包信息
function queryPackNetConnect() {
	<!-- 查询前先关闭软键盘 -->
	//document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","wprovider_id":"' + wprovider_id + '","pack_id":"' + $("#pack_id").val() + '"}';
	console.log("params >>>>>>>>>>>>>>>>>>>>>>" + JSON.stringify(params));
	var method = "exeQueryCcSynergyPackInfoAutomatchProductProcessId";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 5000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			//data.result != 0
			if(data != null) {
				console.log("data != null >>>>>>>>>>>>>>>>>>>>>>" + JSON.stringify(data));
				if(data.out_result == 0) {
					var pack_id = $("#pack_id").val();
					mui.alert(data.out_result_desc, "提示", "确定", null, 'div');
					$("#pack_id").val("");
					$("#pack_id").focus();
					/*packObj = {
					    pack_id: pack_id,
					    product_id: "",
					    scan_time: getnowtime(),
					    new_location_desc: pack_location_target,//location_id,//
					    location_desc: "",
					    pack_location:pack_location_target,
					    loc_view_id: "",
					    product_process_id: product_process_id,
					    spec: "",
					    weight: ""
					};
					scanPackList.push(packObj);
					packObj = {};
					setProductProcessListHtml();//弹出首道加工工序选择
					*/
				} else {
					console.log("data.packList.length > 0 >>>>>>>>>>>>>>>>" + JSON.stringify(data));
					if(pack_location_target == null || pack_location_target == "") {
						$.each(data.packList, function(i, item) {
							//$("#pack_location_m").val(item.location_desc);
							packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: '待定',
								pack_location: $("#pack_location_m").val(),
								loc_view_id: item.loc_view_id,
								product_process_id: item.product_process_id,
								spec: item.spec,
								weight: item.putin_weight
							};
							console.log(JSON.stringify(packObj));
							if(item.pack_status != "10") {
								var status_desc = "";
								if(item.pack_status == "20") {
									status_desc = "出库";
								} else if(item.pack_status == "30") {
									status_desc = "封锁";
								} else {
									status_desc = "转库";
								}
								mui.confirm('捆包为' + status_desc + '状态，不是自由在库。卸货确认，无法联动长春做转库，是否继续？', '提示', ['确认', '取消'], function(e) {
									if(e.index == 0) {
										if(packObj.product_process_id == "N") {
											setProductProcessListHtml();
											packObj.product_process_id = $("#product_process_id").val();
										} else {
											$("#product_process_id").val(packObj.product_process_id);
											scanPackList.push(packObj);
											refreshSum(packObj);
											packObj = {};
										}
									} else {
										packObj = {};
										$("#pack_id").val("");
										$("#pack_id").focus();
									}
								}, 'div');
							} else {
								if(packObj.product_process_id == "N") {
									setProductProcessListHtml();
									packObj.product_process_id = $("#product_process_id").val();
								} else {
									$("#product_process_id").val(packObj.product_process_id);
									scanPackList.push(packObj);
									refreshSum(packObj);
									packObj = {};
								}
								// 重制页面属性
								reSum(scanPackList, $("#pack_id").val());
								console.log(">>>>>>>>>>>>>>>扫描捆包时获取的ScanPackList" + JSON.stringify(scanPackList));
							}
							$("#pack_id").val("");
							$("#pack_id").focus();
						});
					} else {

						$.each(data.packList, function(i, item) {
							packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: item.location_desc,
								pack_location: $("#pack_location_m").val(),
								loc_view_id: item.loc_view_id,
								product_process_id: item.product_process_id,
								spec: item.spec,
								weight: item.putin_weight
							};
							scanPackList.push(packObj);
							refreshSum(packObj);
							packObj = {};
						});
					}
				}
			} else { //连接失败
				//mui.alert("服务器连接失败或未找到长春协同捆包信息，是否继续？", "提示", "确定", null, 'div');
				var btnArray = ['确认', '取消'];
				mui.confirm('未找到捆包信息，请联系长春宝钢入库', '提示', btnArray, function(e) {
					/*if (e.index == 0) {
						queryPackNetNotConnect();
					} else {
						return;
					}*/
					if(e.index == 0) {
						$("#pack_id").val("");
						$("#pack_id").focus();
						return;
					} else {
						//$("#pack_id").val("");
						$("#pack_id").focus();
						return;
					}
				}, 'div');
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if(textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});

};

//
mui(document.body).on('tap', '#storage_realloc', function() {
	console.log(">>>>>>scanPackList:" + JSON.stringify(scanPackList) + ">>>>>>>>>>>>");
	if(scanPackList == null || scanPackList == "") {
		mui.alert("已扫描列表中无捆包记录", "提示", "确定", null, 'div');
		return false;
	}
	/*
	if(if_alloc_switch == "1" && (product_process_id == null || product_process_id =="")){
		mui.alert("请选择首道加工工序", "提示", "确定", null, 'div');
		return false;
	}*/
	var reallocParams = {};
	$.each(scanPackList, function(i, item) {
		reallocParams.pack_id = item.pack_id;
		reallocParams.product_id = item.product_id;
		reallocParams.pack_location = item.new_location_desc; //传入参数
		reallocParams.old_pack_locaiton = item.location_desc
		reallocParams.loc_view_id = item.loc_view_id;
	});

	console.log(">>>>>>reallocParams:" + JSON.stringify(reallocParams) + ">>>>>>>>>>>>");
	exeReallocLocation();
});

// TODO 改成匹配加工工序
mui(document.body).on('tap', '#storage_but', function() {
	var pack_location_m = $("#pack_location_m").val()
	console.log("手工库位方式下库位必填" + pack_location_m + if_alloc_switch);
	if(if_alloc_switch != "1" && (pack_location_m == null || pack_location_m == "")) {
		plus.nativeUI.toast("手工库位方式下库位必填");
		$("#pack_location_m")[0].focus();
		return false;
	}
	console.log("目标库位pack_location_target" + pack_location_target);
	pack_location_target = scanPackList[0].new_location_desc;
	if(pack_location_target == null || pack_location_target == "") {
		mui.alert("已扫描列表中无捆包记录", "提 示", "确定", null, 'div');
		return false;
	}

	if(scanPackList == null || scanPackList == "") {
		mui.alert("已扫描列表中无捆包记录", "提示", "确定", null, 'div');
		return false;
	}
	mui("#storage_but").button("loading");
	$("#overlay").addClass("overlay");
	exePackLocationChangeUpload();
});

/*
 * 修改了超时请求的方式 170922 wangshengbo
 */
function exePackLocationChangeUpload() {
	document.activeElement.blur();
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + seg_no + '","user_id":"' + user_id + '","pack_list":' + JSON.stringify(scanPackList) + '}';
	var method = "exePackLocationChangeUpload";
	var outtime = 10000; //默认超时未10秒 超过100条记录设置为30000
	if(scanPackList.length > 100 && scanPackList.length <= 200) {
		outtime = 30000; //30000
	} else if(scanPackList.length > 200) {
		outtime = 60000; //60000
	}
	try {
		$.ajax({
			type: "post",
			async: true,
			timeout: outtime,
			url: outUri,
			dataType: "json",
			data: {
				innerUri: innerUri,
				params: params,
				method: method
			},
			success: function(data) {
				successFlag = true;
				if(data != null) {
					mui("#storage_but").button("reset");
					$("#overlay").removeClass("overlay");
					if(data.resultStatus == "1") {
						var msg = data.resultDesc;
						console.log(msg)
						mui.alert(msg, "提示", "确定", function() {
							//清空本次已扫描捆包列表数据
							scanPackList = [];
							$("#pack_location_m").val("");
							$("#pack_id").val("");
							$("#product_process_id").val("");
							$("#spec").val("");
							$("#weight").val("");
						}, 'div');
						return;
					} else {
						mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
						return;
					}
				} else { //连接失败
					mui("#storage_but").button("reset");
					$("#overlay").removeClass("overlay");
					mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
					return;
				}
			},
			error: function(xhr, textStatus, errorThrown) {
				console.log("xhr.readystate>>>>>" + xhr.readyState + "textStatus>>>>>>>>>>>>>" + textStatus);
				mui.plusReady(function() {
					var curNetConnetType = plus.networkinfo.getCurrentType();
					if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
						curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
						plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
					} else if(textStatus == "timeout") {
						xhr.abort();
						mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
					} else {
						mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
					}
					successFlag = true;
					mui("#storage_but").button('reset');
					$("#overlay").removeClass("overlay");
				});
			}
		})
	} catch(e) {
		successFlag = true;
		alert("error:" + e.message);
	}
}

//判断库位是否存在
function judgeLocationExist() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_location":"' + $("#pack_location_m").val() + '"}';
	var method = "exeQueryLocation";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if(null != data) {
			console.log("resultStatus:" + data.resultStatus);
			console.log("locationList:" + JSON.stringify(data.locationList));
			if(data.resultStatus == "0") {
				mui.alert("库位不存在", "提示", "确认", null, "div");
				$("#pack_location_m")[0].focus();
				return;
			} else {
				$("#pack_id")[0].focus();
			};
			if(data.resultStatus == "1") {
				if(data.locationList.length > 0) {
					$.each(data.locationList, function(i, item) {
						location_id = item.location_id;
					});
				}
			}
		}
	})
};

function exeReallocLocation() {
	mui("#storage_realloc").button("loading");
	$("#overlay").addClass("overlay");
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var pack_location = "";
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","scanPackList":' + JSON.stringify(scanPackList) + ',"vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","wprovider_id":"' + wprovider_id + '"}';
	console.log("params:" + JSON.stringify(params));
	params = encodeURI(params, 'utf-8');
	var method = "exeSynCDReturnPutinInfoToCC";
	var outtime = 10000; //默认超时未10秒 超过100条记录设置为30000
	if(scanPackList.length > 100 && scanPackList.length <= 200) {
		outtime = 15000; //30000
	} else if(scanPackList.length > 200) {
		outtime = 20000; //60000
	}
	$.ajax({
		type: "post",
		async: true,
		timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("data:" + JSON.stringify(data));
			if(data != null) {
				if("1" == data.out_result) {
					mui.alert(data.out_result_desc, "提示", "确定", function() {
						mui("#storage_realloc").button('reset');
					}, "div");

					scanPackList = [];
					clearData();
					$("#recent_pack_id").html("无");
					$("#sum_qty > #sum_number").text(0);
					$("#sum_weight > #sum_number").text(0);
					localStorage.setItem("putin_vehicle_no", "");
					$("#vehicle_no_span").html("车牌：");
					$("#overlay").removeClass("overlay");
					return true;
				} else {
					mui.alert(data.out_result_desc, "提示", "确定", function() {
						mui("#storage_realloc").button('reset');
					}, "div");
					$("#overlay").removeClass("overlay");
					return false;
				}
			} else {
				mui.alert("连接失败", "提示", "确定", function() {
					mui("#storage_realloc").button('reset');
				}, "div");
				$("#overlay").removeClass("overlay");
				return false;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {

			this; //调用本次ajax请求时传递的options参数*/
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if(textStatus == "timeout") {
					XMLHttpRequest.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				mui("#storage_realloc").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}
/*
function exeReallocLocation() {
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService'; 
	var pack_location = "";
	// '","product_process_id":"' + product_process_id + 
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id +  '","scanPackList":' + JSON.stringify(scanPackList) +  ',"vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","wprovider_id":"' + wprovider_id +'"}';
	console.log("params:" + JSON.stringify(params));
	params = encodeURI(params, 'utf-8');
	var method = "exeSynCDReturnPutinInfoToCC";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("data:" + JSON.stringify(data));
		if(data != null) {
			if("1" == data.out_result) {
				mui.alert(data.out_result_desc, "提示", "确定", function() {}, "div");
				scanPackList = [];
				clearData();
				$("#recent_pack_id").html("无");
				$("#sum_qty > #sum_number").text(0);
		        $("#sum_weight > #sum_number").text(0);
		        localStorage.setItem("putin_vehicle_no","");
		        $("#vehicle_no_span").html("车牌：");
				return true;
				}else {
				mui.alert(data.out_result_desc, "提示", "确定", function() {}, "div");
				return false;
			}
		} else{
			mui.alert("连接失败", "提示", "确定", function() {}, "div");
			return false;
		}
	});
}*/

function refreshSum(pack_info) {
	//最近捆包
	console.log(">>>>>>>>>>>>>>>>>>>" + JSON.stringify(pack_info));
	$("#recent_pack_id").text(pack_info.pack_id);
	//统计合计信息
	var qty = $("#sum_qty > #sum_number").text();
	if(parseInt(qty) < 1) {
		console.log(">>>>>>>>>>>>>>>>>>>1");
		qty = parseInt(qty) + 1;
	} else {
		console.log(">>>>>>>>>>>>>>>>>>>0");
		qty = parseInt(qty);
	}
	var weight = $("#sum_weight > #sum_number").text();
	weight = parseFloat(weight) + parseFloat(pack_info.weight * 1000);

	//取3位小数
	weight = weight.toFixed(3);
	$("#sum_qty > #sum_number").text(qty);
	$("#sum_weight > #sum_number").text(weight);
}

function reSum(packlist, pack_id) {
	//console.log(JSON.stringify(packlist));
	var recent_pack_id = $("#recent_pack_id").text();
	var recent = 0;
	var sum_qty = 0;
	var sum_weight = 0;
	var ppid = "";
	$.each(packlist, function(i, value) {
		//console.log(value.pack_id);
		if(recent_pack_id == value.pack_id) {
			recent = recent + 1;
		}
		sum_qty = sum_qty + 1;
		ppid = value.pack_id
		sum_weight = parseFloat(sum_weight) + parseFloat(value.weight * 1000);
	});
	if(pack_id == "" || pack_id == "undefine" || pack_id == null) {
		if(ppid != "" && ppid != "undefine" && ppid != null) {
			$("#recent_pack_id").text(ppid);
		} else {
			$("#recent_pack_id").text("无");
		}
	} else {
		$("#recent_pack_id").text(pack_id);
	}

	//保留三位小数
	sum_weight = sum_weight.toFixed(3);
	$("#sum_qty > #sum_number").text(sum_qty);
	$("#sum_weight > #sum_number").text(sum_weight);

	//清空信息焦点定位到捆包
	//clearInput();
}

function clearData() {
	$("#pack_location_m").val("");
	$("#pack_id").val("");
	$("#product_process_id").val("");
	$("#spec").val("");
	$("#weight").val("");
	$("#qty").val("");
	//reSum();
	$("#pack_id")[0].focus();
}

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ TODO add by lal 页面控制显示 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

//页面加载时判断库位开关:是否自动分配库位
function queryAllocStorageCCSwitch() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","switch_type":"IF_ALLOC_LOCATION_FOR_CC"}';
	var method = "exeStripOrPointLocation";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		console.log("IF_ALLOC_LOCATION_FOR_CC:" + JSON.stringify(data));
		if(null != data) {
			console.log("switch_con:" + data.switch_con);
			if_alloc_switch = data.switch_con;
		}
		if(if_alloc_switch == "1") {
			setDefaultInputStyle();
		} else {
			$("#pack_location_m").focus();
		}
	})
};

// 智慧仓库:显示首道加工工序列表
mui(document.body).on('tap', '#product_process_id_btn', function() {
	document.activeElement.blur();
	setProductProcessListHtml();
});

//智慧仓库: 显示首道加工工序
function setProductProcessListHtml() {
	var productProcessObject = $("#product_process_id");
	if(productProcessObject.attr("readOnly") != "readonly") {
		if(!productProcessListHtml) {
			loadProductProcess(); //需要的时候才加载卷内径下拉框列表
		}
		$("#ProductProcessIdDiv").toggleClass('show');
	}
}

//智慧仓库:加载首道加工工序
function loadProductProcess() {
	for(var idx in productProcessAry) {
		productProcessListHtml = productProcessListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label class ="productProcessValue">' + productProcessAry[idx] + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#ProductProcessIdList").html(productProcessListHtml);
}

//智慧仓库:查询首道加工工序值集
function queryProductProcessId() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryProductProcessId";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 2000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data != null) {
				$.each(data, function(i, item) {
					productProcessAry[i] = item.product_process_id;
				});
				console.log("productprocessid>>>>>>>>>>>>>>>>>>>>>>>" + JSON.stringify(productProcessAry));
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			if(textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
};

//智慧仓库:首道加工工序确认按钮点击事件
mui(document.body).on('tap', '#productProcessIdConfirm', function() {
	if(!product_process_id) {
		mui.alert("请选择首道加工工序", "提示", "确认", function() {}, "div");
		return false;
	} else {
		$("#ProductProcessIdDiv").toggleClass('show');
		$("#product_process_id").val(product_process_id);
		product_process_id = $("#product_process_id").val();
		console.log(">>>>>>>>>>>>>>>>>>>>>>>product_process_id:" + product_process_id);
		packObj.product_process_id = product_process_id;
		scanPackList.push(packObj);
		reSum(scanPackList, $("#pack_id").val());
		$("#pack_id").val("");
		$("#spec").val("");
		$("#product_process_id").val("");
		$("#weight").val("");
		$("#qty").val("");
		packObj = {};
	}
	$("#pack_id")[0].focus();
});

//智慧仓库:首道加工工序按钮点击事件
mui(document.body).on('tap', '#cancel', function() {
	$("#ProductProcessIdDiv").toggleClass('show');
});

//智慧仓库:绑定首道加工工序列表选中事件
mui(document.body).on('selected', '#ProductProcessIdInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el); //dom元素转化成jquery对象
	product_process_id = el_J.find(".productProcessValue").text();

});

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~TODO 查看list明细信息~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

//查看入库捆包清单
mui(document.body).on('tap', '#detail', function() {
	mui.openWindow({
		url: 'changchun_synergy_pack_list.html',
		id: 'changchun_synergy_pack_list',
		createNew: true,
		extras: {
			scanPackList: scanPackList,
			vehicle_id: vehicle_id,
			transfer_flag: 0
		}
	});
});
//绑定入库清单信息回写事件
window.addEventListener('back', function(e) {
	//获得事件参数
	scanPackList = e.detail.scanPackList;
	//重算合计信息
	reSum(scanPackList, '');
	//从详情直接返回扫描页面
});

//绑定入库清单信息修改事件
window.addEventListener('edit', function(e) {
	//获得事件参数
	scanPackList = e.detail.scanPackList;
	reSum(putinPackList, '');
	var edit_pack = e.detail.edit_pack;
	type = e.detail.type;
	console.log("type:" + type);

	//在捆包明细列表右滑选择删除之后，向当前页面全局变量添加捆包信息，便于之后修改标记的判断
	global_factory_procudt_id = edit_pack.factory_product_id;
	global_pack_id = edit_pack.pack_id;
	global_qty = edit_pack.putin_qty;
	global_spec = edit_pack.spec;
	global_weight = edit_pack.weight;
	refreshPackInfo(edit_pack);
	//clearPreviousLocation();//清空上一个捆包的库位
});