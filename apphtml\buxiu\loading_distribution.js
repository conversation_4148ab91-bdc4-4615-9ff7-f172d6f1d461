/**
 * 初始化变量信息 
 */
var scanBillList = new Array(); //点击确定保存数据的集合 
var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var user_id = localStorage.getItem("account");
var carnum = ""; //弹出层的车牌号
var num = ""; //当前点击的下标
var forLocationPack = {};
var info = {};
var infos;


window.onload = function onload() {
	console.log("sds");
	putoutVoucherDownLoad();

}
//点击保存插入表数据			

mui(document.body).on("tap", "#query_btn", function() {
	//TODO 调用后台查询单据信息
	//putoutVoucherDownLoad();
	//	console.log("dsd"+JSON.stringify(infos) + "=="+scanBillList);
	if (num == null || num == "") {
		mui.alert("请选择装卸点", "提示", "确定", function() {}, 'div');
		return;
	}
	var shu = [];
	
	$.each(forLocationPack, function(i, item) {
		if(item.hand_point_id != null){
			shu.push(item) ;
		}
		});
		if(shu.length < 1){
			mui.alert("请选择装卸点", "提示", "确定", function() {}, 'div');
			return;
			}
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALogisticService';
	var pack_id = $("#packId").val();
	var mark = "通过pda 捆包检查";
	var segNo = localStorage.getItem("segNo");
	var params = '{"seg_no":"' + segNo + '","veinfo":' + JSON.stringify(shu) + ',"user_id":"' + user_id +
	'"}';
	//console.log(params);
	var method = "insertloadingAllcation";
	console.log(params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		//console.log("查询捆包返回：" + JSON.stringify(data));
		if (null != data) {
			if (data.resultStatus == "1") {
				
				mui.alert("保存成功", "提示", "确定", function() {
				$("#pack_putin_list").html("");
				forLocationPack = {};
				putoutVoucherDownLoad();
				}, 'div');

				//var putinPackList1 = new Array();
				//putinPackList1 = putinPackList;
				forLocationPack = {};
			} else {
				mui.alert(data.returnDesc, "提示", "确认", function() {}, "div");
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确认", function() {}, "div");
		}
	});


});

/**
 * 
 * 查询车牌号
 */
function putoutVoucherDownLoad() {
	console.log(webServiceUrl);
	var t_putin_id = $("#t_putin_id").val();
	var start_date = ""
	start_date = $("#start_date").val();
	var end_date = ""; // 
	end_date = $("#end_date").val();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALogisticService';
	var params = '{"seg_no":"' + segNo + '"}';
	console.log(params);
	var method = "queryVehicleInfo";
	console.log(webServiceUrl);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(data);
		if (null != data) {
			if (data.resultStatus == "1") {
				console.log(JSON.stringify(data.vehicleList));
				if (data.vehicleList.length > 0) {
					forLocationPack = data.vehicleList;
					var chtml = ""; //<input id="wprovider_desc" value="'+item.wprovider_desc+'" />
					$.each(data.vehicleList, function(i, item) {
						$("#pack_putin_list").append(
							'<li class="mui-table-view-cell"><div class="mui-slider-handle">' +
							'<div id="vehicle_no' + i + '" class="left2">' +
							item.vehicle_no +
							'</div><div id="start_date' + i + '" class="left2" >' +
							item.check_date +
							'</div><div id="car_trace_no' + i + '" hidden="hidden" class="left2">' +
							item.car_trace_no +
							'</div><div  class="left2"><a  class="qing" id ="hand_point_id' + i +
							'" onclick=' +
							'ssinfo("' + i + '")' + '>' +
							"请选择" +
							'</a></div></div></li>'

						);
					});
					//$("#table").html(chtml);
				}
			}
		} else { //连接失败
			//mui.alert("连接服务器异常", "提示", "确认", function() {}, "div");
		}
	});
}
//点击厂区出来下拉框
/* mui(document.body).on("tap", "#hand_point_id", function(ww) {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#car_no").val("");
	console.log("queryEnterFacoryVehicleNo:" + ww);
	//查询车牌号
	queryEnterFacoryVehicleNo();

}) */

function queryEnterFacoryVehicleNo() {
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALogisticService';
	var vehicle_id = $("#car_no").val();
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeFindHandPoint";
	console.log("queryEnterFacoryVehicleNo:" + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("data:" + JSON.stringify(data));
			if (data.resultList.length == 0) {
				/*mui.alert("未查询到对应的车牌号信息", "提示", "确定", function() {}, 'div');
				return ;*/
			} else {
				var lihtml = "";
				$.each(data.resultList, function(i, item) {
					var pli_thid = "";
					var pli_thname = "";
					lihtml = lihtml +
						'<li class="mui-table-view-cell" >' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;"  >' +
						'<label><span class="vehicle_num">' + item.hand_point_name + '</span>' +
						'<span class="">&nbsp;&nbsp;&nbsp;' + pli_thname + '</span>' +
						'<span class="peichenum" hidden="hidden">' + item.allocate_vehicle_id +
						'</span>' +
						'<label><span class="zhuang" hidden="hidden">' + item.hand_point_id + '</span>' +
						'<span class="genzong" hidden="hidden">' + item.car_trace_no + '</span>' +
						'<span class="p_thid" hidden="hidden">' + pli_thid + '</span>' +
						'<span class="p_scope" hidden="hidden">' + item.allocate_scope + '</span>' +
						'</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#carList").html(lihtml);
			}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//返回事件
mui(document.body).on('tap', '#backbutton', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
});

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {

	var el = e.detail.el;
	var el_J = $(el);
	var thid = el_J.find(".p_thid").text();
	//if (hand_point_id == thid) {
	$("#confirm").attr("disabled", false);
	carnum = el_J.find(".vehicle_num").text();
	zhuang = el_J.find(".zhuang").text();
	peiche = el_J.find(".peichenum").text();
	zxtype = el_J.find(".zxtype").text();
	genz = el_J.find(".genzong").text();
	p_scope = el_J.find(".p_scope").text();
	$("#car_no").val(carnum);
	$("#genzonghao").val(genz);
	$("#peichedanhao").val(peiche);
	$("#zhuangxietype").val(zxtype);
	$("#p_thid").val(thid);
	$("#p_scope").val(p_scope);
	$("#zhuang").val(zhuang);

	/* } else {
		$("#confirm").attr("disabled", true);
	} */
});

function ssinfo(index) {
	console.log("====8888=======" + index);
	num = index;
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#car_no").val("");
	console.log("queryEnterFacoryVehicleNo:");
	//查询车牌号
	queryEnterFacoryVehicleNo();

}
//确认事件
mui(document.body).on('tap', '#confirm', function() {
	var thid = $("#p_thid").val();
	var car = $("#car_no").val();
	var stt = 'hand_point_id' + num;
	$('#hand_point_id' + num).html(carnum);
	console.log("对对对==" + $("#car_no").val() + "====" + stt+"===="+$("#zhuang").val());
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	//封装数据
	//if(num)
	var infos = forLocationPack[num];
	/* infos.vehicle_id =  $('#vehicle_no'+num).html();
	infos.start_date = $('#start_date'+num).html();
	infos.hand_point_id = $("#car_no").val();
	infos.car_trace_no = $('#car_trace_no'+num).html(); */
	//info = info + forLocationPack;
	// infos.hand_point_id = $("#car_no").val();
	 forLocationPack[num].hand_point_id = $("#zhuang").val();
	//scanBillList.push(infos);
	
	//scanBillList.push = (forLocationPack);
	console.log("对对对==" + JSON.stringify(forLocationPack) + "=====" );
	//reSum(putinPackList, pack_id);

});

function cacheVehivle() {
	var vehicle = $("#hand_point_id").val();
	if ('' != vehicle) {
		localStorage.setItem("putin_vehicle_no", vehicle);
	}
}
