/** 明细样式 */

.detail_row {
	height: 49px;
}

.detail_row input {
	width: 65%;
}

.detail_textarea {
	height: 100px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.quality_desc {
	float: left;
	width: 65%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: left;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 35%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#pack_id {
	padding: 0px 5px;
	font-size: 20px;
}

.pack_list {
	height: 0;
	overflow: auto;
	border: 0px solid #AAAAAA;
}

.storage_pack {
	width: 50%;
	margin-top: 12px;
}

.li-text {
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 78px;
}

.li-text p span font {
	text-align: center;
	font-size: 22px;
	color: #000000;
}

.li-height {
	margin-top: 4px;
}


/** 列表样式 */

.pack_location_target {
	color: red;
	font-size: 22px;
}

.pack_location_now {
	color: blue;
	font-size: 22px;
	margin-top: 8px;
}

#old_quality_grade_name {
	background: #CCCCCC;
}

#old_shopsign {
	background: #CCCCCC;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}

.mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
}