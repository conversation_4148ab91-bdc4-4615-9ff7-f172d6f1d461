/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var selectVoucherList = new Array(); //
var queryVoucherList = new Array(); //待选出库单据列表
var voucherinfo = new HashMap();
var voucherinfo = new HashMap();
var globalVehicleID = ""; //定义全局车牌号
var globalSysID =""; //定义车辆跟踪号
//查询按钮事件绑定			

mui(document.body).on("tap", "#query_button", function() {
	//TODO 调用后台查询单据信息
	document.activeElement.blur();
	putoutVoucherDownLoad();
});


/**
 * 入库查询
 * 入库单必填，入库日期必填，
 */
function putoutVoucherDownLoad() {
	console.log(webServiceUrl);
	var vehicle_id = $("#vehicle_id").val();
	var car_trace_no = $("#car_trace_no").val();
	var start_date = ""
	start_date = $("#start_date").val();
	var end_date = ""; // 
	end_date = $("#end_date").val();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAQueryService';
	var params = '{"seg_no":"' + segNo + '","vehicle_id":"' + vehicle_id + '","car_trace_no":"' + car_trace_no +
		'","start_date":"' + start_date +
		'","end_date":"' + end_date + '"}';
	console.log(params);
	var method = "queryVehicleListInfo";
	console.log(webServiceUrl);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log(data);
		if (null != data) {
			console.log("dsdwdwe==");
			if (data.resultStatus == "1") {
				console.log("dsdwdwe=8=");
				console.log(JSON.stringify(data.resultList));
				if (data.resultList.length > 0) {
					var chtml = ""; //<input id="wprovider_desc" value="'+item.wprovider_desc+'" />
					$.each(data.resultList, function(i, item) {
						voucherinfo.put(item.vehicle_no, item);
						chtml = chtml + '<li class="mui-table-view-cell" id="format">' +
							'<a class="mui-navigate-right">' +
							'<input id="putin_id" value="' + item.car_trace_no + '"/>' +
							'<div class="row"><span class="icon">' + item.vehicle_no + '</span>' +
							'</br>' + item.check_date + '</div>' +
							'<div class="row"><span class="icon">' + item.leave_factory_date +
							'</span>' +
							'</br>' + item.status + '</div>' +
							'</a>' +
							'</li>';
					});
					$("#table").html(chtml);
				}
			} else {
				console.log("dsdwdwe=88=");
				mui.alert("未查询到数据", "提示", "确认", function() {}, "div");
				$("#table").html("");
				console.log("未查询到数据");
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确认", function() {}, "div");
		}
	});
}
/*
//删除单据信息
function delSelectVoucherList(voucher_id) {
	var query_index = getIndexByVoucherId(voucher_id, selectVoucherList);
	selectVoucherList.splice(query_index, 1);
}
mui(document.body).on('tap', 'li', function() {
	console.log("dsdwdwe=88=" + selectVoucherList);
	var a = $(this).children('a');
	if (a.hasClass("select") == false) {
		var voucher_id = a.children('div').children('div').children('span').html();
		var voucher = voucherinfo.get(voucher_id);
		var query_index = getIndexByVoucherId(voucher_id, queryVoucherList);
		selectVoucherList.push(queryVoucherList[query_index]);
		a.addClass("select");
		console.log("dsdwdwe=88=99" + voucher_id);
	} else if (a.hasClass("select") == true) {
		a.removeClass("select");
		var voucher_id = a.children('div').children('span').html();
		//删除已选单据
		delSelectVoucherList(voucher_id);
	}
	//console.log("selectVoucherList.length:" + selectVoucherList.length + JSON.stringify(selectVoucherList));
});
*/
//电子签名按钮
mui(document.body).on("tap", "#autograph_button", function() {
	//跳转签字签名页面
	 if (globalVehicleID == "" && globalSysID == "") { //如果该变量为空，说明没有从车牌信息列表中选中数据，则获取车牌文本框里的数据
			mui.toast("请选择要补充签名的数据");
			return;
		}else{ 
			putoutSignature(globalVehicleID,globalSysID);
		}
	
});

function putoutSignature() {
	mui.openWindow({
		url: "electric_signature_bc.html",
		id: "electric_signature_bc",
		extras: {
			vehicle_no: globalVehicleID,
			car_trace_no: globalSysID,
			openType: "signature"
		},
		createNew: true
	});
}

//绑定单据点击事件
mui(document.body).on('tap','li',function(){
	var a = $(this).children('a');
	$("#car_num").blur();
	if(a.hasClass("select") == false){
		globalVehicleID = a.children('div').children('span').html();
		globalSysID = a.children('input').val();
		var dd = a.children('input').val();
		console.log(dd+"==="+globalVehicleID);
		$(".mui-navigate-right").removeClass("select"); //移除其他的车辆信息的红勾
		a.addClass("select"); //为当前的li元素添加勾选样式
	}else{
		globalVehicleID = "";
		globalSysID = "";
		a.removeClass("select");
	}
});
function exeConfigVeicleFinishHandPoint() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	$("#table").html("");
	console.log("=============================递四方速递=====");
}