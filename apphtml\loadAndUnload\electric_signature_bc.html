<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>电子签名</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="electric_signature_bc.css" />
	</head>

	<body>
		<div id="overlay" ></div>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<!--页名标签-->
			<h4 class="mui-title">电子签名</h4>
		</div>

		<div class="mui-content" style="margin-top: 4px;padding-top: 0px;">
			<!--签名区域 -->
			<div id="content">
				<div id="signatureparent">
					<div id="signature"></div>
				</div>
			</div>
			<div class="mui-input-row" style="margin-top: 5px;">
				<!--<button id="sure" type="button" class="mui-btn mui-btn-grey" style="width: 45%;font-size: 22px; line-height: 1.5;float: left;">确&nbsp; &nbsp;&nbsp;&nbsp;认</button>-->
				<button id="sure" float: left;></button>
				<button id="delete" float: right;></button>
			</div>
			<div id="displayarea"></div>
		</div>
		
		<!--引用jquery文件-->
		<script type="text/javascript" src="../../js/signature/jquery.js"></script>
		<script>
			(function($) {
				let topics = {};
				$.publish = function(topic, args) {
					if(topics[topic]) {
						let currentTopic = topics[topic],
							args = args || {}; 

						for(let i = 0, j = currentTopic.length; i < j; i++) {
							currentTopic[i].call($, args);
						}
					}
				};
				$.subscribe = function(topic, callback) {
					if(!topics[topic]) {
						topics[topic] = [];
					}
					topics[topic].push(callback);
					return {
						"topic": topic,
						"callback": callback
					};
				};
				$.unsubscribe = function(handle) {
					let topic = handle.topic;
					if(topics[topic]) {
						let currentTopic = topics[topic];

						for(let i = 0, j = currentTopic.length; i < j; i++) {
							if(currentTopic[i] === handle.callback) {
								currentTopic.splice(i, 1);
							}
						}
					}
				};
			})(jQuery);
		</script>
		
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script src="../../js/signature/jSignature.js"></script>
		<script src="../../js/signature/plugins/jSignature.CompressorBase30.js"></script>
		<script src="../../js/signature/plugins/jSignature.CompressorSVG.js"></script>
		<script src="../../js/signature/plugins/jSignature.UndoButton.js"></script>
		<script src="../../js/signature/plugins/signhere/jSignature.SignHere.js"></script>
		<script src="../../js/jquery.base64.js" ></script>
		<script src="../../js/mui.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="electric_signature_bc.js"></script>
	</body>

</html>