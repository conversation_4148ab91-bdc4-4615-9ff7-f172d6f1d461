/**
 * 初始化基本参数
 */
//var factory_area_name = localStorage.getItem("factory_area_name");  //厂区
//var hand_point_id = localStorage.getItem("hand_point_id"); //装卸点
var segNo=localStorage.getItem("segNo");
var user_id=localStorage.getItem("account");
var domainName = "http://ksh.baointl.com/pdaService/";
var webServiceUrl=localStorage.getItem("webServiceUrl");
//var segNo="00118";
//var webServiceUrl = "***********:7001";
//var user_id = "dev";

var vehicleLeaveList = new Array(); // 车辆出厂信息
var vehicleUnusualLeaveList = new Array(); // 未装离厂信息

var selectCarno =  new Array();//已选择要出厂车
var carMap= new  HashMap();//车辆信息  Map<vehicle_no,carinfo>
// 页面开始加载车辆信息
window.onload = function onload(){
	mui.plusReady(function(){
		//页面加载时查询车辆出厂及未装离厂的信息
		//queryVehicleList();
	});
}

$(function(){
	mui.init({
		swipeBack:true, //启用右滑关闭功能
		gestureConfig:{
			longtap: true,
			doubletap: true
		}
	});
	queryVehicleList();
});

(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);

/**
 * 出厂按钮绑定事件
 */
mui(document.body).on('tap','#vehicle_leave',function(){
	//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
	//mui("#vehicle_leave").button('loading');
	$("#overlay").addClass("overlay");
	//调用出厂
	exeVehicleLeave();
});


/**
 * 查车辆出厂信息
 */
function queryVehicleList(){
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"'+segNo+'"}';//,"user_id":"'+user_id+'"
	console.log(params+",innerUri:"+innerUri);
	var method = "exeQueryVehicleLeaveFactoryList";//查询方法名
	$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){   
		if(null != data){//连接成功
				if(data.returnStatus=='1'){
					console.log(JSON.stringify(data.returnList));
					
					$.each(data.returnList, function(i,item) {
						if(item.status=='06'){
							vehicleUnusualLeaveList.push(item);
							showUnusualVehicleList();
						}else{
							vehicleLeaveList.push(item) // 车辆出厂信息
							showVehicleList();
						}
						carMap.put(item.vehicle_no,item);
					});
				}else{
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {
				}, 'div');
			}
		}else{//连接失败
			mui.alert("连接服务器异常","提示","确定",function() {}, "div");
		}
	});
}
//车辆未装离厂信息
function showUnusualVehicleList(){
	//绘制已扫捆包信息
	var phtml="";
	console.log("vehicleUnusualLeaveList:"+JSON.stringify(vehicleUnusualLeaveList));
	$.each(vehicleUnusualLeaveList, function(i,item) {
			phtml=phtml+'<li class="mui-table-view-cell">'+
							'<a class="mui-navigate-right">'+
							'<div>'+
								'<div id="vehicle_no"  data="'+item.vehicle_no+'">'+item.vehicle_no+'</div>'+
								'<div>'+
									'<div id="status" class="left"><span>状态</span><label data="'+item.status+'">'+item.status_desc+'</label></div>'+
								'</div>'+
							'</div>'+
							'</a>'+
						'</li>';
		});
		$("#uphtml").html(phtml);
}
//车辆出厂信息
function showVehicleList(){
	console.log("vehicleLeaveList:"+JSON.stringify(vehicleLeaveList));
	var phtml="";
	$.each(vehicleLeaveList, function(i,item) {
			phtml=phtml+'<li class="mui-table-view-cell">'+
							'<a class="mui-navigate-right">'+
							'<div>'+
								'<div id="vehicle_no"  data="'+item.vehicle_no+'">'+item.vehicle_no+'</div>'+
								'<div>'+
									'<div id="status" class="left"><span>状态</span><label  data="'+item.status+'">'+item.status_desc+'</label></div>'+
								'</div>'+
							'</div>'+
							'</a>'+
						'</li>';
		});
		$("#yphtml").html(phtml);
}

//车辆出厂绑定单据点击事件
mui(document.body).on('tap', '#leave li', function() {
	var a = $(this).children('a');
	if(a.hasClass("select") == false) {
		a.addClass("select");
		var vehicle_no = a.children('div').children('div').html().trim();
		var carinfo=carMap.get(vehicle_no);
		var index = getIndexById(vehicle_no,selectCarno);
		if(index>0){
			//如果已经存在在待出厂列表中则不用再次添加
		}else{
		   /* var carinfo =new Object();
			carinfo.vehicle_no=vehicle_no;
			carinfo.status=carstatus;*/
			selectCarno.push(carinfo);
		}
		}
		});
/**
 * 查车辆出厂信息
 */
function queryVehicleList(){
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/IPDAVehicleTraceService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+user_id+'"}';
	params = JSON.stringify(params);
	console.log(params);
	var method = "exeQueryVehicleLeaveFactoryList";//查询方法名
	$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){   
		if(null != data){//连接成功
			console.log(data);
			if(data != null){
				vehicleLeaveList.push(data);
				showUnusualVehicleList(vehicleLeaveList);
			}else{
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {
				}, 'div');
			}
		}else{//连接失败
			mui.alert("连接服务器异常","提示","确定",function() {}, "div");
		}
		});
		}

/**
 * 查车辆出厂信息
 */
function queryVehicleList(){
	var outUri = domainName+"webService_test.jsp";//PDAVehicleTraceService   //PDABoardVehicleService
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAVehicleTraceService';
	//var params = '{"seg_no":"'+segNo+'","user_id":"'+user_id+'"}';
	var params = '{"seg_no":"'+segNo+'"}';
//				params = JSON.stringify(params);
	params=encodeURI(params,'utf-8');
	console.log(params);
	var method = "exeQueryVehicleLeaveFactoryList";//查询方法名
	$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){   
		if(null != data){//连接成功
			console.log(JSON.stringify(data));
			if(data != null){
				vehicleLeaveList.push(data);
//							showUnusualVehicleList(vehicleLeaveList);
				showUnusualVehicleList();
			}else{
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {
				}, 'div');
			}
		}else{//连接失败
			mui.alert("连接服务器异常","提示","确定",function() {}, "div");
		}
}); 
}
//未装车辆出厂绑定单据点击事件
mui(document.body).on('tap', '#unusual_leave li', function() {
	var a = $(this).children('a');
	if(a.hasClass("select") == false) {
		a.addClass("select");
		var vehicle_no = a.children('div').children('div').html().trim();
		//var carstatus=a.children('div').find("label").attr("data");
		var carinfo1=carMap.get(vehicle_no);
		var index = getIndexById(vehicle_no,selectCarno);
		if(index>0){
			//如果已经存在在待出厂列表中则不用再次添加
		}else{
		    /*var carinfo =new Object();
			carinfo.vehicle_no=vehicle_no;
			carinfo.status=carstatus;*/
			selectCarno.push(carinfo1);
		}
	} else if(a.hasClass("select") == true) {
		a.removeClass("select");
		var vehicle_no = a.children('div').children('div').html().trim();
		var index = getIndexById(vehicle_no,selectCarno);
		selectCarno.splice(index,1);
	}
	console.log(JSON.stringify(selectCarno));
}); 

//车辆出厂方法
function exeVehicleLeave(){
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAVehicleTraceService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+user_id+'","carList":'+JSON.stringify(selectCarno)+'}';
	console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "exeVehicleLeaveFactory";//车辆离厂
	$.getJSON(outUri,{innerUri:innerUri,params:params,method:method},function(data){   
		if(null != data){//连接成功
			if(data.returnStatus == "1"){
				vehicleLeaveList = new Array(); // 车辆出厂信息
				vehicleUnusualLeaveList = new Array(); // 未装离厂信息
				selectCarno =  new Array();//已选择要出厂车
				carMap= new  HashMap();//车辆信息  Map<vehicle_no,carinfo>
				//出厂成功之后，重新查询
				queryVehicleList();
			    //出厂成功
				mui("#confirm").button('reset');
				mui.alert("出厂成功!", "提示", "确定", function() {}, "div");
			}else{
				mui("#confirm").button('reset');
			    mui.alert("出厂失败!", "提示", "确定", function() {}, "div");
			}
		}else{//连接失败
			mui.alert("连接服务器异常","提示","确定",function() {}, "div");
		}
	});
	
}
function getIndexById(vehicle_no,carList){
	var index = -1;
	$.each(carList, function(i,value) {
		if(value.vehicle_no == vehicle_no){
			index = i;
			return false;
		}
	});
	return index;
}