//质量封闭
mui(document.body).on('click', '#seal', function() {
	mui.openWindow({
		url: 'quality_lock.html',
		createNew: true
	});
});
//质量解封
mui(document.body).on('click', '#dearchive', function() {
	mui.openWindow({
		url: 'quality_unlock.html',
		createNew: true
	});
});
//质量改判
mui(document.body).on('click', '#commute', function() {
	mui.openWindow({
		url: 'quality_commute.html',
		createNew: true
	});
});
//质量判废
mui(document.body).on('click', '#waste', function() {
	mui.openWindow({
		url: 'quality_waste.html',
		createNew: true
	});
});

/*//销售出库
mui(document.body).on('click', '#sale', function() {
	mui.openWindow({
		url: 'bingbao.html',
		createNew: true
	});
});*/