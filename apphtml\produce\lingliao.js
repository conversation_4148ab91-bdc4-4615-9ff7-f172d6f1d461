//页面类型 1：领料  2：退料
var operation;
//工单列表数据
var workOrderList;
//料台列表数据
var chargePlatList;
//查询出来的捆包信息
var packInfo;
//规格列表数据
var specList;
//工单输入扫描框
var workOrderKeywords;
//选择的工单
var pickedWorkerOrderId;
//选中的料台
var pickedChargePlatCode;
var pickedPlatNum;
//选中的成品规格
var pickedSpec;
//工单下的领顺序号
var numberNo = 1;
var machineId1 =localStorage.getItem("machineId");


//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false,
});

//获取传递的参数
mui.plusReady(function() {
	var self = plus.webview.currentWebview();
	operation = self.operation;
	pickedChargePlatCode = self.pickedPlat;
	pickedPlatNum = self.pickedPlatNum;
	

	var segNo = localStorage.getItem("segNo");
	var putin_method_switch = getSwitchValue(segNo, 'PDA_PICKED_PLAT_NUM');
	//隐藏垛位号 ERP_64963
	if(putin_method_switch == 1){
		$("#platnumpicker").hide();
		$("#change_platnumpicker_btn").hide();
		//$("#start_unload").show();
	}
	
	$('#gongdan-input').val(localStorage.getItem("pickedWorkerOrderId"));
	if (operation == 1) {
		console.log("-----------------pickedChargePlatCode:"+pickedChargePlatCode);
		$('#platpicker').text('料台号：'+pickedChargePlatCode);
		$('#platnumpicker').text('垛位号：'+pickedPlatNum);
	}else{
	//	$('#platpicker-info').attr("style","display:none;");
	//	$('#platpicker-label').attr("style","display:none;");
		$('#platpicker-info').hide();
		$('#platpicker-label').hide();
	//	$('#platpicker-info').style.display = "none";
	//	document.getElementById("platpicker-info").style.display = "none";
		$('#platpicker-title').attr("style","display:none;");
		$('#platpicker').attr("style","display:none;");
		$('#platnumpicker').attr("style","display:none;");
		$('#change_platpicker_btn').attr("style","display:none;");
		$('#change_platnumpicker_btn').attr("style","display:none;");
	}
	if (operation == 1) {
		//领料
		$('.mui-title').text('生产领料');
		$('#table-span').show();
		$('#table-div').show();

	} else {
		//退料
		$('.mui-title').text('生产退料');
		$('#table-span').hide();
		$('#table-div').hide();
	}

	// queryChargePlat();
});

//工单号筛选事件
mui(document.body).on('tap', '#gongdan-search', function(e) {
	console.log("工单号筛选" + workOrderList == null);
	if (workOrderList == null) {
		queryWorkerorder();
	} else {
		showWorkOrderPickDialog();
	}
});

//工单扫描事件
// $('#gongdan-input').keypress(function(e) {
// 	if (e.keyCode == 13) {
// 		//回车事件
// 		var gongdan = $('#gongdan-input').val();
// 		if (gongdan == null || gongdan == "") {
// 			//输入为空
// 			mui.alert("请扫描或者输入工单");
// 			return;
// 		}
// 	}
// });

//捆包号扫描事件
$('#pack-id-input').keypress(function(e) {
	if (e.keyCode == 13) {
		//查询捆包
		queryPackInfo();
		//查询规格
		querySpec();
		// if (chargePlatList == null) {
		// 	queryChargePlat();
		// }
	}
});

//料台号选中事件
mui(document.body).on('selected', '#table-ul', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	pickedChargePlatCode = el_J.find("#table_value").text();
	console.log("料台选择：" + pickedChargePlatCode);
});

//成品规格选中时间
mui(document.body).on('selected', '#spec-ul', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	pickedSpec = el_J.find("#spec_name").text();
	console.log("规格选择：" + pickedSpec);
});

//提交事件
mui(document.body).on('click', '#commit_btn', function(e) {
	
	document.getElementById("commit_btn").disabled =true;
	var workOrderCode = $('#gongdan-input').val();
	var packId = $('#pack-id-input').val();
	if (workOrderCode == null || workOrderCode == "") {
		//输入为空
		mui.alert("请扫描或者输入工单号");
		return;
	} else if (packId == null || packId == "") {
		//输入为空
		mui.alert("请扫描或者输入捆包号");
		return;
	} else if (packInfo == null) {
		//输入为空
		mui.alert("请查询扫描的捆包信息");
		return;
	} else if (operation == 1 && pickedChargePlatCode == null) {
		mui.alert("请选择料台");
		return;
	} else if (pickedSpec == null) {
		mui.alert("请选择成品规格信息");
		return;
	}

	// commitData();
	checkWorkOrderStatus();

});

//工单选中事件
mui(document.body).on('selected', '#work-order-list', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	pickedWorkerOrderId = el_J.find("#work-order-id").text();
});

//工单选择确定按钮事件
mui(document.body).on('click', '#picked-confirm', function(e) {
	$("#pick-dialog-div").toggleClass('show');
	console.log("料台选中：" + pickedWorkerOrderId);
	$('#gongdan-input').val(pickedWorkerOrderId);
	localStorage.setItem("pickedWorkerOrderId",pickedWorkerOrderId);
});

//工单选择确定取消事件
mui(document.body).on('click', '#picked-cancel', function(e) {
	$("#pick-dialog-div").toggleClass('show');
	pickedWorkerOrderId = '';
});


/**
 * 查询工单数据
 */
function queryWorkerorder() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	// var seg_no = '00118';
	// var user_id = 'test';
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		processOrderFlag: 0,
		machineId:machineId1
	};
	params = JSON.stringify(params);
	var method = "queryProcessOrderInfo";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				if (result.returnValue == 1 &&
					getJsonLength(result.pdaProcessOrderInfoList) > 0) {
					//查询到数据
					workOrderList = result.pdaProcessOrderInfoList;
					showWorkOrderPickDialog();
				} else {
					mui.alert("暂无工单数据");
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("工单查询失败");
		}
	})

}

//智慧仓库:加载首道加工工序
function showWorkOrderPickDialog() {
	var productProcessListHtml = '';
	for (var order in workOrderList) {
		productProcessListHtml = productProcessListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label id ="work-order-id">' + workOrderList[order].productionOrderCode + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#work-order-list").html(productProcessListHtml);
	$("#pick-dialog-div").toggleClass('show');
}

/**
 * 查询捆包数据
 */
function queryPackInfo() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	// var seg_no = '00118';
	// var user_id = 'test';

	var workOrderCode = $('#gongdan-input').val();
	var packId = $('#pack-id-input').val();
	if (workOrderCode == null || workOrderCode == "") {
		//输入为空
		mui.alert("请扫描或者输入工单号");
		return;
	} else if (packId == null || packId == "") {
		//输入为空
		mui.alert("请扫描或者输入捆包号");
		return;
	}

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: workOrderCode,
		packId: packId
	};
	params = JSON.stringify(params);
	var method = "queryBaleInfo";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				if (operation == 1 && getJsonLength(result.wtlPackInfo) > 0) {
					//领料捆包数据
					packInfo = result.wtlPackInfo[0];
					showPackInfo();
				} else if (operation == 2 && getJsonLength(result.tlPackInfo) > 0) {
					//退料捆包数据
					packInfo = result.tlPackInfo[0];
					showPackInfo();
				} else {
					packInfo = null;
					showPackInfo();
					mui.alert("未查询到捆包");
				}
				console.log(method + "---packInfo：" + JSON.stringify(packInfo));
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			packInfo = null;
			showPackInfo();
			mui.alert("捆包查询失败");
		}
	})

};

/**
 * 显示捆包信息
 */
function showPackInfo() {
	if (packInfo == null) {
		$('#pack-weight').text('重量：');
		$('#pack-count').text('数量：');
		$('#factory_id').text('钢厂资源号：');
		$('#shopsign').text('牌号：');
		$('#spec').text('规格：');
		$('#warehouse_id').text('仓库：');
		$('#variety').text('品种：');
	} else {
		$('#pack-weight').text('重量：' + packInfo.putinWeight + '吨');
		$('#pack-count').text('数量：' + packInfo.putinQty);
		$('#factory_id').text('钢厂资源号：' + packInfo.factoryProductId);
		$('#shopsign').text('牌号：' + packInfo.shopsign);
		$('#spec').text('规格：' + packInfo.spec);
		$('#warehouse_id').text('仓库：' + packInfo.wproviderName);
		$('#variety').text('品种：' + packInfo.productTypeId);
	}
}


/**
 * 查询料台
 */
function queryChargePlat() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	// var seg_no = '00118';

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no
	};
	params = JSON.stringify(params);
	var method = "queryChargePlat";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				if (result.returnValue == 1 &&
					getJsonLength(result.chargePlatList) > 0) {
					//查询到数据
					chargePlatList = result.chargePlatList;
					showChargePlatList();
				} else {
					chargePlatList = null;
					showChargePlatList();
					mui.alert(result.wtlPackInfoErrorDetai);
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			chargePlatList = null;
			showChargePlatList();
			mui.alert("料台数据查询失败");
		}
	})
};

function showChargePlatList() {
	var innerHtml = '';
	if (chargePlatList != null) {
		for (var index in chargePlatList) {
			innerHtml = innerHtml +
				'<li class="mui-table-view-cell">' +
				'<a class="mui-navigate-right">' +
				'<label id="table_name">' + chargePlatList[index].codeDesc + '</label>' +
				'<label id="table_value">' + chargePlatList[index].codeValue + '</label>' +
				'</a>' +
				'</li>';
		};
	}

	$("#table-ul").html(innerHtml);
}

/**
 * 查询规格
 */
function querySpec() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var productionOrderCode = $('#gongdan-input').val();
	console.log("querySpec productionOrderCode = " + productionOrderCode);
	// var seg_no = '00118';
	// var user_id = 'admin';
	// var productionOrderCode = '34H190903001';

	if (productionOrderCode == null || productionOrderCode == "") {
		//输入为空
		return;
	}

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: productionOrderCode
	};
	params = JSON.stringify(params);
	var method = "queryDetail";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				if (result.msgCode == 1 &&
					getJsonLength(result.fmPartList) > 0) {
					console.log('规格：' + JSON.stringify(result.fmPartList));
					//产出成品list查询到数据
					specList = result.fmPartList;
					showSpecList();
				} else {
					specList = null;
					showSpecList();
					mui.alert(result.wtlPackInfoErrorDetai);
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			specList = nuull;
			showSpecList();
			mui.alert("成品规格信息查询失败");
		}
	})
};

/**
 * 页面上显示规格列表
 */
function showSpecList() {
	var innerHtml = '';
	console.log('showSpecList：' + specList != null);
	if (specList != null) {
		for (var index in specList) {
			console.log('规格：' + specList[index].spec);
			innerHtml = innerHtml +
				'<li class="mui-table-view-cell">' +
				'<a class="mui-navigate-right">' +
				'<label id="spec_name">' + specList[index].spec + '</label>' +
				'</a>' +
				'</li>';
		};
	}
	$("#spec-ul").html(innerHtml);
}

/**
 * 校验工单启动状态
 */
function checkWorkOrderStatus() {
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var productionOrderCode = $('#gongdan-input').val();
	// var seg_no = '00118';
	// var user_id = 'test';
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: productionOrderCode
	};
	params = JSON.stringify(params);
	var method = "queryProcessOrderInfo";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				if (result.returnValue == 1 &&
					getJsonLength(result.pdaProcessOrderInfoList) == 1) {
					//查询到数据
					var orderData = result.pdaProcessOrderInfoList[0];
					if (orderData.status == '30') {
						//工单开启状态，可以提交
						if (operation == 1) {
							//领料，需要获取当前序号
							getPackNo();
						} else {
							commitData();
						}
					} else {
						//工单不是开启状态，跳转工单启动界面
						var operationMsg = operation == 1 ? '领料' : '退料';
						var machineId = orderData.machineId;
						mui.alert('要执行' + operationMsg + '操作，需先启动工单', '确定', function() {
							mui.openWindow({
								id: 'workorderoperation',
								url: 'workorderoperation.html',
								createNew: true,
								extras: {
									machineId: machineId,
									workOrderCode: orderData.productionOrderCode
								}
							});
						});
					}

				} else {
					document.getElementById("commit_btn").disabled =false;
					mui.alert("工单状态查询失败，请重试");
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("工单查询失败");
			document.getElementById("commit_btn").disabled =false;
		}
	})
}

/**
 * 查询工单下捆包no
 */
function getPackNo() {
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var productionOrderCode = $('#gongdan-input').val();
	// var seg_no = '00118';
	// var user_id = 'test';
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: productionOrderCode
	};
	params = JSON.stringify(params);
	var method = "queryDetail";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			if (result != null) {
				console.log(method + "---result：" + JSON.stringify(result.rmPartPackList));
				if (result.rmPartPackList != null &&
					getJsonLength(result.rmPartPackList) > 0) {
					var packInfo = result.rmPartPackList[0];
					console.log("packInfo.numberNo = " + packInfo.numberNo);
					if (packInfo.numberNo == null ||
						packInfo.numberNo.trim() == '') {
						numberNo = 1;
					} else {
						numberNo = parseInt(packInfo.numberNo);
						numberNo++;
					}

				} else {
					numberNo = 1;
				}

				console.log("当前序号：" + numberNo);

				commitData();

			} else {
				mui.alert("工单状态查询失败，请重试");
				document.getElementById("commit_btn").disabled =false;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("工单查询失败");
			document.getElementById("commit_btn").disabled =false;
		}
	})
}

/**
 * 提交数据
 */
function commitData() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据

	var workOrderCode = $('#gongdan-input').val();
	var packId = $('#pack-id-input').val();


	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';

	var method = '';
	var packArray = [];
	// var chargePlatParam = pickedChargePlatCode;
	var chargePlatParam = pickedPlatNum;
	if (operation == 1) {
		//领料
		method = 'putoutPack';
		packArray = [{
			packId: packInfo.packId,
			productId: packInfo.productId,
			productionOrderCode: packInfo.productionOrderCode,
			// chargePlatParam: packInfo.chargePlatParam,
			// chargePlat: chargePlatParam,
			chargePlat: pickedChargePlatCode,
			numberNo: numberNo

		}];
	} else {
		//退料
		method = 'retreatPack';
		packArray = [{
			packId: packInfo.packId,
			productId: packInfo.productId,
			productionOrderCode: packInfo.productionOrderCode,
			// chargePlatParam: packInfo.chargePlatParam,
			chargePlat: ''
		}];
	}

	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		productionOrderCode: workOrderCode,
		packList: packArray
	};
	params = JSON.stringify(params);

	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {

			console.log(method + "---result：" + JSON.stringify(result));

			if (result != null) {
				if (result.returnValue == 1) {
					//提交成功
					mui.alert(result.returnDesc, '确定', function() {
						commitSuccess();
					});
				} else {
					var message = result.errorDetail;
					if (message == null || message == "") {
						message = "提交失败";
					}
					mui.alert(message);
					document.getElementById("commit_btn").disabled =false;
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("提交失败，请重试");
			document.getElementById("commit_btn").disabled =false;
		}
	})

};

function commitSuccess() {
	//清空数据
//	$('#gongdan-input').val('');
	$('#pack-id-input').val('');
	packInfo = null;
	specList = null;
	showPackInfo();
	showSpecList();
	$('.mui-selected').each(function(i, n) {
		$(n).removeClass('mui-selected');
	});
	document.getElementById("commit_btn").disabled =false;
}

function getJsonLength(jsonData) {
	var jsonLength = 0;
	for (var item in jsonData) {
		jsonLength++;
	}
	return jsonLength;
}



//切换料台
mui(document.body).on('click', '#change_platpicker_btn', function(e) {
	
	var prePage = plus.webview.getWebviewById("produce_menus");
	mui.fire(prePage, 'onPlatPickedChoose', {
	});
	
	//关闭子页面
	mui.back();

});



//切换垛位号
mui(document.body).on('click', '#change_platnumpicker_btn', function(e) {
	
	var prePage = plus.webview.getWebviewById("produce_menus");
	mui.fire(prePage, 'onPlatPicked', {
		pickedPlat: pickedChargePlatCode
	});
	
	//关闭子页面
	mui.back();

});


