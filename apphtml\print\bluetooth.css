.text {
	float: left;
	width: 20%;
	font-size: 20px;
	padding: 10px 0px;
	text-align: center;
}

#spec,
#factory_product_id,
#grade,
#customer {
	width: 70%;
	padding: 0px 5px;
	font-size: 20px;
	background-color: #D3D3D3;
}

#pack_id {
	width: 70%;
	padding: 0px 5px;
	font-size: 20px;
}

#query_pack,
#print {
	width: 45%;
	height: 50px;
	font-size: 18px;
	line-height: 1.0;
	margin: 0px 0px 0px 0px;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}
#InnerVehicleDiv.show {
				visibility: visible;
				opacity: 1;
			}
			
			#InnerVehicleDiv {
				position: absolute;
				z-index: 999;
				width: 270px;
				/*height: 245px;*/
				left: 42%;
				top: 35%;
				margin-left: -100px;
				margin-top: -122px;
				border-radius: 10px;
				background: #FFFFFF;
				box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
				/** 动画效果 */
				visibility: hidden;
				opacity: 0;
				/** 文字效果 */
				font-size: 20px;
				text-align: left;
			}
			
			#InnerVehicleDiv>.title {
				text-align: center;
				padding: 8px 0px;
				font-size: 22px;
				border-bottom: 1px solid;
				border-color: #D8D8D8;
			}
			
			.mui-input-row .mui-icon-search {
				font-size: 24px;
				position: absolute;
				z-index: 1;
				top: 10px;
				right: 0;
				width: 42px;
				/*height: 30px;*/
				/*text-align: left;*/
				color: #999;
			}
			
			.mui-input-clear {
				width: 45%;
			}
			
			.select {
				width: 18%!important;
				font-size: 20px!important;
				background-color: white;
				border: 1px solid rgba(0, 0, 0, .2)!important;
			}
			
			.active {
				background-color: red;
				color: white;
			}
			/*弹出框end*/
			#aaa{
				text-align: center;
			}
			#pop_car {
				position: absolute;
				top: 0px;
				left: 0px;
				z-index: 998;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, .3);
				visibility: hidden;
				opacity: 0;
			}

			#pop_car.show {
				visibility: visible;
				opacity: 1;
			}