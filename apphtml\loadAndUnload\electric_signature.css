input {
	padding: .5em;
	margin: .5em;
}

select {
	padding: .5em;
	margin: .5em;
}
/*This is the Signature content*/

#signatureparent {
	color: darkblue;
	background-color: darkgrey;
	/*max-width:600px;*/
	padding: 20px;
}
/*This is the div within which the signature canvas is fitted*/

#signature {
	border: 4px;
	background-color: lightgrey;
}
/*This is the content for Signature*/

html.touch #content {
	float: left;
	width: 92%;
}

html.touch #scrollgrabber {
	float: right;
	width: 4%;
	margin-right: 2%;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAFCAAAAACh79lDAAAAAXNSR0IArs4c6QAAABJJREFUCB1jmMmQxjCT4T/DfwAPLgOXlrt3IwAAAABJRU5ErkJggg==)
}

html.borderradius #scrollgrabber {
	border-radius: 1em;
}
/* 半透明的遮罩层 */
.overlay {
    background-color: #777777;
     opacity: 0.5; /* 透明度 */
    /*filter: alpha(opacity=50); /* IE的透明度 
    
    display: none;
   
    top: 0px;
    left: 0px;*/
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 99999; /* 此处的图层要大于页面 */
    /*display:none;*/
}
#tishi{
	/* //padding: .5em;
	margin-right: 5%; */
	position: absolute;
	width: 100%;
	height: 100%;
	margin:2%;
	color: red;
}