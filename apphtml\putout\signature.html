<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>电子签名</title> 
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
	</head>
	<style>
		input {
			padding: .5em;
			margin: .5em;
		}
		
		select {
			padding: .5em;
			margin: .5em;
		}
		/*This is the Signature content*/
		
		#signatureparent {
			color: darkblue;
			background-color: darkgrey;
			/*max-width:600px;*/
			padding: 20px;
		}
		/*This is the div within which the signature canvas is fitted*/
		
		#signature {
			border: 4px;
			background-color: lightgrey;
		}
		/*This is the content for Signature*/
		
		html.touch #content {
			float: left;
			width: 92%;
		}
		
		html.touch #scrollgrabber {
			float: right;
			width: 4%;
			margin-right: 2%;
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAFCAAAAACh79lDAAAAAXNSR0IArs4c6QAAABJJREFUCB1jmMmQxjCT4T/DfwAPLgOXlrt3IwAAAABJRU5ErkJggg==)
		}
		
		html.borderradius #scrollgrabber {
			border-radius: 1em;
		}
		/* 半透明的遮罩层 */
			.overlay {
			    background-color: #777777;
			     opacity: 0.5; /* 透明度 */
			    /*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
			    position: absolute;
			    width: 100%;
			    height: 100%;
			    z-index: 99999; /* 此处的图层要大于页面 */
			    /*display:none;*/
			}
	</style>

	<body>
		<div id="overlay" ></div>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<!--页名标签-->
			<h4 class="mui-title">电子签名</h4>
		</div>

		<div class="mui-content" style="margin-top: 4px;padding-top: 0px;">
			<!--签名区域 -->
			<div id="content">
				<div id="signatureparent">
					<div id="signature"></div>
				</div>
			</div>
			<div class="mui-input-row" style="margin-top: 5px;">
				<!--<button id="sure" type="button" class="mui-btn mui-btn-grey" style="width: 45%;font-size: 22px; line-height: 1.5;float: left;">确&nbsp; &nbsp;&nbsp;&nbsp;认</button>-->
				<button id="sure" float: left;></button>
				<button id="delete" float: right;></button>
			</div>
			<div id="displayarea"></div>
		</div>
		<!--引用jquery文件-->
		<script type="text/javascript" src="../../js/signature/jquery.js"></script>
		<script>
			(function($) {
				var topics = {};
				$.publish = function(topic, args) {
					if(topics[topic]) {
						var currentTopic = topics[topic],
							args = args || {}; 

						for(var i = 0, j = currentTopic.length; i < j; i++) {
							currentTopic[i].call($, args);
						}
					}
				};
				$.subscribe = function(topic, callback) {
					if(!topics[topic]) {
						topics[topic] = [];
					}
					topics[topic].push(callback);
					return {
						"topic": topic,
						"callback": callback
					};
				};
				$.unsubscribe = function(handle) {
					var topic = handle.topic;
					if(topics[topic]) {
						var currentTopic = topics[topic];

						for(var i = 0, j = currentTopic.length; i < j; i++) {
							if(currentTopic[i] === handle.callback) {
								currentTopic.splice(i, 1);
							}
						}
					}
				};
			})(jQuery);
		</script>
		
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script src="../../js/signature/jSignature.js"></script>
		<script src="../../js/signature/plugins/jSignature.CompressorBase30.js"></script>
		<script src="../../js/signature/plugins/jSignature.CompressorSVG.js"></script>
		<script src="../../js/signature/plugins/jSignature.UndoButton.js"></script>
		<script src="../../js/signature/plugins/signhere/jSignature.SignHere.js"></script>
		<script src="../../js/jquery.base64.js" ></script>
		<script src="../../js/mui.min.js" type="text/javascript" charset="utf-8"></script>
		<script>
			var domainName = "http://ksh.baointl.com/pdaService/";
			var segNo=localStorage.getItem("segNo");
			var user_id=localStorage.getItem("account");
			var webServiceUrl=localStorage.getItem("webServiceUrl");
			var vehicle_id = localStorage.getItem("vehicle_id");
			var car_trace_no =localStorage.getItem("car_trace_no");
			var dataDriver = [];//司机签名
			var dataTrans = [];//物流人签名
			
			/*var unUploadPack=localStorage.setItem("unUploadPack",unUploadPack);
			var uploadPack=localStorage.setItem("uploadPack",uploadPack);
			var packInfo=localStorage.setItem("packInfo",packInfo);
			var contractSubInfo=localStorage.setItem("contractSubInfo",contractSubInfo);
			var last_html=localStorage.setItem("last_html","putout_scan");
			var operatorType=localStorage.setItem("operatorType",operatorType);
			var putout_confirm_flag=localStorage.setItem("putout_confirm_flag",putout_confirm_flag);*/

			var unUploadPack=localStorage.getItem("unUploadPack");
			var uploadPack = new HashMap();//已上传捆包信息  Map<voucher_id,packList>
			//var uploadPack=localStorage.getItem("uploadPack");
			var packInfo=localStorage.getItem("packInfo");
			var contractSubInfo=localStorage.getItem("contractSubInfo");
			var last_html=localStorage.getItem("last_html");
			var operatorType=localStorage.getItem("operatorType");
			var putout_confirm_flag=localStorage.getItem("putout_confirm_flag");
			//var putoutVoucherList = localStorage.getItem("putoutVoucherList");
			var putoutVoucherList = new Array();//出库单据列表
			var team_id=localStorage.getItem("team_id");//班组代码
			var work_shift=localStorage.getItem("class_id");//班次代码
			var signatureCount = "0";
			window.onload = function onload(){
				mui.plusReady(function(){
//					unUploadPack['map'] = plus.webview.currentWebview().unUploadPack['map'];
//					uploadPack['map'] = plus.webview.currentWebview().uploadPack['map'];
//					packInfo['map'] = plus.webview.currentWebview().packInfo['map'];
//					contractSubInfo['map'] = plus.webview.currentWebview().contractSubInfo['map'];
//					vehicle_id=plus.webview.currentWebview().vehicle_id;
//					car_trace_no=plus.webview.currentWebview().car_trace_no;
					putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
					uploadPack =plus.webview.currentWebview().uploadPack;
					//params =plus.webview.currentWebview().params;
					//console.log(uploadPack['map']);
			});
			}
		
			
			
			$(document).ready(function() {

				// This is the part where jSignature is initialized.
				var $sigdiv = $("#signature").jSignature({
						'UndoButton': true
					})

					// All the code below is just code driving the demo. 
					,
					$su = $('#sure'),
					$del = $('#delete'),
					$tools = $('#tools'),
					$extraarea = $('#displayarea'),
					pubsubprefix = 'jSignature.demo.'

				var export_plugins = $sigdiv.jSignature('listPlugins', 'export'),
					chops = ['<span><b>Extract signature data as: </b></span><select>', '<option value="">(select export format)</option>'],
					name
				for(var i in export_plugins) {
					if(export_plugins.hasOwnProperty(i)) {
						name = export_plugins[i]
						chops.push('<option value="' + name + '">' + name + '</option>')
					}
				}
				chops.push('</select><span><b> or: </b></span>')

				$(chops.join('')).bind('change', function(e) {
					if(e.target.value !== '') {
						var data = $sigdiv.jSignature('getData', e.target.value)
						$.publish(pubsubprefix + 'formatchanged')
						if(typeof data === 'string') {
							$('textarea', $tools).val(data)
						} else if($.isArray(data) && data.length === 2) {
							$('textarea', $tools).val(data.join(','))
							$.publish(pubsubprefix + data[0], data);
						} else {
							try {
								$('textarea', $tools).val(JSON.stringify(data))
							} catch(ex) {
								$('textarea', $tools).val('Not sure how to stringify this, likely binary, format.')
							}
						}
					}
				}).appendTo($tools)
				
				// sure--确认按钮
				$('<input type="button" value="确认">').bind('click', function(e) {
					console.log($sigdiv.jSignature('getData','image'));
					//被坑的地方 取数据
					var data = $sigdiv.jSignature('getData','image');
					var i = new Image()
					i.src = 'data:' + data[0] + ',' + data[1]
					dataDriver = $sigdiv.jSignature('getData','image');
					//dataTrans = $sigdiv.jSignature('getData','image');
					if(last_html == "putout_scan"){
						console.log("第一次签名111111111111111111");
						putoutPackUpload();
						 signatureCount = "1";
						//signatureUpload()
					}else{
						console.log("再次签名222222222222222222");
						signatureUpload()
					}
				}).appendTo($su)

				//rest--清除按钮
				$('<input type="button" value="清除">').bind('click', function(e) {
					$sigdiv.jSignature('reset')
				}).appendTo($del)

				$('<div><textarea style="width:100%;height:7em;"></textarea></div>').appendTo($tools)

				$.subscribe(pubsubprefix + 'formatchanged', function() {
					$extraarea.html('')
				})

				$.subscribe(pubsubprefix + 'image/svg+xml', function(data) {

					try {
						var i = new Image()
						i.src = 'data:' + data[0] + ';base64,' + btoa(data[1])
						$(i).appendTo($extraarea)
					} catch(ex) {

					}

					var message = [
						"If you don't see an image immediately above, it means your browser is unable to display in-line (data-url-formatted) SVG.", "This is NOT an issue with jSignature, as we can export proper SVG document regardless of browser's ability to display it.", "Try this page in a modern browser to see the SVG on the page, or export data as plain SVG, save to disk as text file and view in any SVG-capabale viewer."
					]
					$("<div>" + message.join("<br/>") + "</div>").appendTo($extraarea)
				});

//				$.subscribe(pubsubprefix + 'image/svg+xml;base64', function(data) {
//					var i = new Image()
//					i.src = 'data:' + data[0] + ',' + data[1]
//					$(i).appendTo($extraarea)
//
//					var message = [
//						"If you don't see an image immediately above, it means your browser is unable to display in-line (data-url-formatted) SVG.", "This is NOT an issue with jSignature, as we can export proper SVG document regardless of browser's ability to display it.", "Try this page in a modern browser to see the SVG on the page, or export data as plain SVG, save to disk as text file and view in any SVG-capabale viewer."
//					]
//					$("<div>" + message.join("<br/>") + "</div>").appendTo($extraarea)
//				});
//
//				$.subscribe(pubsubprefix + 'image/png;base64', function(data) {
//					var i = new Image()
//					i.src = 'data:' + data[0] + ',' + data[1]
//					$('<span><b>As you can see, one of the problems of "image" extraction (besides not working on some old Androids, elsewhere) is that it extracts A LOT OF DATA and includes all the decoration that is not part of the signature.</b></span>').appendTo($extraarea)
//					$(i).appendTo($extraarea)
//				});
//
//				$.subscribe(pubsubprefix + 'image/jsignature;base30', function(data) {
//					$('<span><b>This is a vector format not natively render-able by browsers. Format is a compressed "movement coordinates arrays" structure tuned for use server-side. The bonus of this format is its tiny storage footprint and ease of deriving rendering instructions in programmatic, iterative manner.</b></span>').appendTo($extraarea)
//				});
//
//				if(Modernizr.touch) {
//					$('#scrollgrabber').height($('#content').height())
//				}

	});
		
		//传司机和物流人员的信息
		function signatureInfo(){
			var params = {"seg_no":segNo,"user_id":user_id,"vehicle_id":vehicle_id,"car_trace_no":car_trace_no,
				"img_base64_d":'data:' + dataDriver[0] + ',' + dataDriver[1]/*,
				"img_base64_t":'data:' + dataTrans[0] + ',' + dataTrans[1]*/
		        }; 
			return params;
		}
		
		function signatureUpload(){
				var outUri = domainName+"webService_test.jsp";
				var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAPutoutService';
				//拼接参数信息
				var params = signatureInfo();
				params = JSON.stringify(params);
				console.log(params);
				params = window.encodeURIComponent(params);
				console.log("ddd:"+params);
				//params=encodeURI(params,'utf-8');
				// 定义接口里的方法
				var method = "exeuploadSignature";
				$.ajax({
					type: "post",
					async: true,
					url: outUri,
					data: {innerUri:innerUri,params:params,method:method},
					dataType: "json",
					success: function(result){
					    if(result!=null){
					    	mui.alert("签名上传成功","提示","确定",function() {
					    		if(signatureCount == "1"){
					    			mui.openWindow({
											url: '../loadAndUnload/hand_point_end.html',
											id: 'hand_point_end',
											createNew:true
										});
					    		}else{
					    			mui.openWindow({
											url:'../loadAndUnload/vehicle_load_manage.html',
											id:'vehicle_load_manage', 
											createNew:true
										});
									}
					    		}, "div");
					    }else{
							mui.alert("签名上传失败请重试","提示","确定",function() {}, "div");
						}
					},
		            error: function(){
						mui.alert("签名上传失败请重试","提示","确定",function() {}, "div");
		            }
		        });
		        
			}
		
		
			//出库捆包上传post方法
			function putoutPackUpload(){
				<!-- 查询前先关闭软键盘-->
				document.activeElement.blur();
				//console.log("name:"+mean_name);
				//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
				mui("#putout").button('loading');
				$("#overlay").addClass("overlay");
				//如果出现异常或者超时设置半分钟后可以再点一次
			    setTimeout(function() {
			        mui("#putout").button('reset');
			        $("#overlay").removeClass("overlay");
			    }.bind(this), 5000);
				
				var outUri = domainName+"webService_test.jsp";
				var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAPutoutService';
				//拼接参数信息
				//var params = initPutoutPackUploadParams();
				var params = plus.webview.currentWebview().params;
				console.log(params);
				params = JSON.stringify(params);
				console.log(params);
				//params=encodeURI(params,'utf-8');
				var method = "exeNewPutoutPackUpload";
				
				$.ajax({
					type: "post",
					async: true,
					url: outUri,
					data: {innerUri:innerUri,params:params,method:method},
					dataType: "json",
					success: function(result){
					    if(result!=null){
							//console.log("data:"+JSON.stringify(data));
							/*mui("#putout").button('reset');
	                        $("#overlay").removeClass("overlay");*/
							if(result.returnStatus=="1"){
							mui("#putout").button('reset');
	                       	 $("#overlay").removeClass("overlay");
								mui.alert(""+result.returnDesc,"提示","确定",function() {
									signatureUpload();
										/*mui.openWindow({
											url:'vehicle_load_manage.html',
											id:'vehicle_load_manage', 
											createNew:true
										});*/
								}, "div");
							}else{
								mui.alert("出库失败："+result.returnDesc,"提示","确定",function() {}, "div");
							}
						}else{
							mui("#putout").button('reset');
	                        $("#overlay").removeClass("overlay");
							mui.alert("出库失败请重试","提示","确定",function() {}, "div");
						}
					},
		            error: function(){
						mui("#putout").button('reset');
                        $("#overlay").removeClass("overlay");
						mui.alert("出库失败请重试","提示","确定",function() {}, "div");
		            }
		        });
		        
			}
			
			//初始化上传出库捆包参数
			function initPutoutPackUploadParams(){
				var params = {};
				params['seg_no'] = segNo;
				params['user_id'] = user_id;
				params['team_id'] = team_id;
				params['work_shift'] = work_shift;
				params['operatorType'] = operatorType;
				params['vechile_id'] = vehicle_id;
				//tangli  add
				params['putout_confirm_flag'] = putout_confirm_flag;
				var voucherList = new Array();
				var aaa = new HashMap(uploadPack);
				console.log(aaa);
				var voucherIdList = aaa.keySet();
				$.each(voucherIdList, function(i) {
					if(uploadPack.get(voucherIdList[i]).length > 0){//该单据下有捆包要出库
						var voucher = {};
						//console.log("weerrrr："+JSON.stringify(uploadPack));
						//console.log("weerrrr："+JSON.stringify(uploadPack.get(1)));
						//从出库单据列表中取单据验证码
						//console.log("qqqqqqqqqqqqqqqqqqqqf："+uploadPack.get(voucherIdList[i]));
						//console.log("qqqqqqqqqqqqqqqqqqq："+JSON.stringify(voucherIdList));
						//var varobj=voucherIdList[i];
						//console.log("qqqqqqqqqqqqqqqqvarobj ："+varobj);
						//var inmber=getIndexByVoucherId(voucherIdList[i],putoutVoucherList);
						//console.log("qqqqqqqqqqqqqqqqinmber："+inmber);
						//var identify_code = putoutVoucherList[inmber].identify_code;
						var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i],putoutVoucherList)]).identify_code;
						console.info("identify_codeidentify_codeidentify_code::"+identify_code);
						console.log("identify_code:" + identify_code);
						voucher['identify_code'] = identify_code;
						voucher['voucher_id'] = voucherIdList[i];
						voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
						//往单据列表中添加信息
						voucherList.push(voucher);
					}
				});
				params['voucherList'] = voucherList;
				
				return params;
			}
			
		</script>
	</body>

</html>