/*
 * 定义全局变量
 */
var webServiceUrl = localStorage.getItem("webServiceUrl");
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account"); //采用localStorage存储数据

//查询条件初始化
// var q_voucher_id = ""; //提单号
// var q_customer_name = ""; //客户名称

var hand_type; //装卸类型
var vehicle_id; //车牌号
var customer_id; //承运商代码
var customer_name; //承运商名称
var car_arrive_time; //车辆到达时间
var deliver_type; //交货方式
var driver_name; //司机名称
var id_card; //身份证号
var phone_number; //手机号
var voucherinfo = new HashMap(); //物流提单信息
var selectVoucherList = new Array(); //已选物流提单
var queryVoucherList = new Array();

var allocate_vehicle_id; //配车单号
var patch_flag; //加单标记
/*
 * 页面初始化
 */
window.onload = function onload() {
	console.log('111')
	mui.plusReady(function() {
		self = plus.webview.currentWebview();
		hand_type = self.hand_type;
		vehicle_id = self.vehicle_id;
		customer_id = self.customer_id;
		customer_name = self.customer_name;
		car_arrive_time = self.car_arrive_time;
		deliver_type = self.deliver_type;
		driver_name = self.driver_name;
		id_card = self.id_card;
		phone_number = self.phone_number;
		patch_flag = self.patch_flag;
		allocate_vehicle_id = self.allocate_vehicle_id;
		console.log(allocate_vehicle_id + patch_flag + "》》" + driver_name);
		console.log(self.id_card);
	});
}

//绑定单据点击事件
mui(document.body).on('tap', 'li', function() {
	var a = $(this).children('a');
	var voucher_id = a.children('div').children('div').children('span').html().trim();
	var voucher = voucherinfo.get(voucher_id);
	//console.log(voucher_id + ">>>>>>>>>>>>" + voucher);
	if (a.hasClass("select") == false) {
		a.addClass("select");
		selectVoucherList.push(voucher);
	} else if (a.hasClass("select") == true) {
		a.removeClass("select");
		//删除已选单据
		delSelectVoucherList(voucher);
	}
});

//删除已选单据
function delSelectVoucherList(voucher) {
	var index;
	$.each(selectVoucherList, function(i, item) {
		if (item.logistics_id == voucher.logistics_id) {
			index = i;
			return false;
		}
	});
	selectVoucherList.splice(index, 1);
}

//查询
mui(document.body).on('click', '#btn_query', function() {
	exeQueryLogisticsPlan();
});

/**
 * 查询物流计划提单
 */
function exeQueryLogisticsPlan() {
	const logistics_id = $('#logistics_id').val(); // 物流计划号
	const factory_product_id = $('#factory_product_id').val(); // 钢厂资源号
	const product_id = $('#product_id').val(); // 资源号
	const pack_id = $('#pack_id').val(); // 捆包号
	
	let outUri = domainName + "webService.jsp?callback=?";
	let innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	let params = '{"seg_no":"' + segNo + '","logistics_id":"' + logistics_id + '","factory_product_id":"' + factory_product_id + '","product_id":"' + product_id + '","pack_id":"' + pack_id + '"}';
	params = encodeURIComponent(params);
	console.log(params);
	console.log(innerUri); 
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: 'exeQueryLogisticsPlanToPackId',
		},
		dataType: "json",
		success: function(data) {
			console.log(JSON.stringify(data.resultDesc));
			if (data != null) {
				let voucherHtml = "";
				if ("1" == data.resultStatus) {
					$.each(data.resultDesc, function(i, item) {
						voucherinfo.put(item.voucher_num, item);
						voucherHtml = voucherHtml + `
						<li class="mui-table-view-cell">
							<a class="mui-navigate-right">
								<div>
									<div class="row">
										<span id="voucher_num">
											${item.voucher_num}
										</span>
										<span class="icon">重</span>
										${item.putin_weight}
									</div>
									<div class="row">
										<span class="icon">资</span>
										${item.product_id}
										<span class="icon">钢</span>
										${item.factory_product_id}
									</div>
									<div class="row">
										<span class="icon">捆</span>
										${item.pack_id}
										<span class="icon">计</span>
										${item.logistics_id}
									</div>
								</div>
							</a>
						</li>
						`;
					});
				} else {
					mui.toast("没有查询到相应物流提单");
				}
				$("#voucher_list").html(voucherHtml);
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//保存
mui(document.body).on('click', '#btn_save', function() {
	if (null == selectVoucherList || "" == selectVoucherList) {
		mui.alert("请选择物流提单进行保存！");
		return false;
	}
	mui("#btn_save").button('loading');
	if ("1" == patch_flag) {
		console.log('111');
		exeAddAllocateVehicleOrder();
	} else {
		console.log('222');
		exeSaveAllocateVehicle();
	}
});

/**
 * 保存配车单主子项
 */
function exeSaveAllocateVehicle() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeSaveAllocateVehicle";
	var params = JSON.stringify({
		seg_no: segNo,
		user_id: user_id,
		deliver_type: deliver_type,
		customer_id: customer_id,
		vehicle_id: vehicle_id,
		driver_name: driver_name,
		id_card: id_card,
		phone_number: phone_number,
		car_arrive_time: car_arrive_time,
		hand_type: hand_type,
		selectVoucherList: selectVoucherList,
		allocate_scope: '20', // 配车维度
	});
	console.log(params);
	params = encodeURI(params, 'utf-8');
	console.log(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		success: function(data) {
			if (data != null) {
				if (data.resultStatus == "1") {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						mui.openWindow({
							id: "allocate_vehicle_menus",
							url: "allocate_vehicle_menus.html"
						});
					}, 'div');
					return;
				} else {
					mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
			} else {
				mui.toast("保存失败，请稍后再试！");
			}
		},
		error: function() {
			console.log("失败");
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

/**
 * 保存配车单主子项
 */
function exeAddAllocateVehicleOrder() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeAddAllocateVehicleOrder";
	var params = JSON.stringify({
		seg_no: segNo,
		user_id: user_id,
		selectVoucherList: selectVoucherList,
		allocate_vehicle_id: allocate_vehicle_id,
		allocate_scope: '20', // 配车维度
	});
	params = encodeURI(params, 'utf-8');
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 3000,
		async: true,
		success: function(data) {
			if (data != null) {
				if (data.resultStatus == "1") {
					mui.alert(data.resultDesc, "提示", "确定", function() {
						mui.openWindow({
							id: "allocate_vehicle_menus",
							url: "allocate_vehicle_menus.html"
						});
					}, 'div');
					return;
				} else {
					mui.alert("操作失败!原因：" + data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
			} else {
				mui.toast("保存失败，请稍后再试！");
			}
		},
		error: function() {
			console.log("失败");
			mui.toast("网络超时，请稍后再试！");
		}
	});
}
