var seg_no = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl");
var seg_no_info = localStorage.getItem("segNoInfo");
var menu_switch = "";
var status = "";
var putin_method_switch = ""; //青岛宝井入库方式开关

$(function() {
	$(".mui-popover").hide();
	var team_name = localStorage.getItem("team_name");
	var class_name = localStorage.getItem("class_name");
	//add ERP_55764 xiesenpeng 成都宝钢
	if(seg_no == '00112') {
		toWebServices();
	};
	//end xiesenpeng
	var wprovider_name = localStorage.getItem("wprovider_name");
	var hand_point_name = localStorage.getItem("hand_point_name");
	console.log("seg_no:" + seg_no + ",")
	$("#seg_no").html(seg_no);
	$("#user_id").html(user_id);
	$("#team_name").html(team_name);
	$("#class_name").html(class_name);
	$("#wprovider_name").html(wprovider_name);
	//console.info("aaahand_point_name:bbbbd::"+hand_point_name+"  a   "+class_name+"   b  "+wprovider_name);
	$("#hand_point_name").html(hand_point_name);
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
	/*$(".mui-content a").click(function() {
		console.log(this.index);
	});*/

	//zhangjiamin 出厂物流优化
	menu_switch = getSwitchValue(seg_no, 'LEAVE_FACTORYO_PTIMIZE');
	putin_method_switch = getSwitchValue(seg_no, 'PDA_PUTIN_METHOD_CHOICE');
	if(menu_switch != "1") {
		$("#hpn").attr("style", "display:none;");
	}
	indexmenu();
});
//退出清缓存
function exeEdit() {
	localStorage.clear();
	localStorage.setItem("segNo", seg_no);
	localStorage.setItem("webServiceUrl", webServiceUrl);
	localStorage.setItem("segNoInfo", seg_no_info);
	localStorage.setItem("imesWebServiceUrl", imesWebServiceUrl);
}

window.onload = function onload() {
	mui.plusReady(function() {
		//add by penglei 返回首页关闭缓存页面 2018-10-16 16:39:05
		putinScanPage = plus.webview.getWebviewById("putin_scan");
		packListPage = plus.webview.getWebviewById("putin_pack_list");
		if(putinScanPage != "" && putinScanPage != null && putinScanPage != undefined) {
			console.log(putinScanPage);
			putinScanPage.close();
		}
		if(packListPage != "" && packListPage != null && packListPage != undefined) {
			packListPage.close();
		}
	});

	//			indexmenu();
}
window.addEventListener('back', function(e) {
	team_name = localStorage.getItem("team_name");
	class_name = localStorage.getItem("class_name");
	wprovider_name = localStorage.getItem("wprovider_name");
	var hand_point_name = localStorage.getItem("hand_point_name");
	user_id = localStorage.getItem("account");
	$("#user_id").html(user_id);
	$("#team_name").html(team_name);
	$("#class_name").html(class_name);
	$("#wprovider_name").html(wprovider_name);
	$("#hand_point_name").html(hand_point_name);
});

function indexmenu() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAUserService';
	//佛宝显示9位
	if(seg_no == "00126" || seg_no == "00112" || seg_no == "00120" ) {
		var params = '{"seg_no":"' + seg_no + '","row_num_start":"' + 0 + '","row_num_end":"' + 13 + '"}';
	} else {
		var params = '{"seg_no":"' + seg_no + '","row_num_start":"' + 0 + '","row_num_end":"' + 13 + '"}';
	}
	var method = "exeQueryMenu";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		var phtml = "";
		if(null != data) {
			console.log("首页菜单："+JSON.stringify(data));
			$.each(data.menuList, function(i, item) {
				if(data.returnStatus == "1") {
					phtml = phtml + '<li class="mui-table-view-cell mui-media mui-col-xs-4 mui-col-sm-4">' +
						'<a href="javaScript:tojude(\'' + item.menu_englishname_one + '\');">' +
						'<img class="mui-media-object" src="../../resource/' + item.menu_englishname_one + '.png" />' +
						'<div class="">' +
						item.menu_name_one +
						'</div>' +
						'</a>' +
						'</li>';
					$("#phtml").html(phtml);
				} else {
					$("#phtml").html("");
				}
			});
		}
	});
}

function tojude(menu_name_one) {
	// 2020/3/3 guoxiangsheng  ERP_57598  添加并包扫描页面
	if(menu_name_one == 'bingbao') {
		mui.openWindow({
			url: '../package/bingbao.html',
			id: 'bingbao',
			createNew: true
		});
	} else if(menu_name_one == 'products') {
		/*mui.openWindow({
			url: '../produce/produce_menus.html',
			id: 'produce_menus',
			createNew: true
		});*/
		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			//班组班次不存在，跳转选择班组班次
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else if(localStorage.getItem("machineName") == null || localStorage.getItem("machineName") == "" ||
			localStorage.getItem("machineId") == null || localStorage.getItem("machineId") == "") {
			//机组不存在，跳转选择机组
			mui.openWindow({
				url: '../produce/select_unit.html',
				id: 'select_unit',
				createNew: true,
			});
		} else {
			// 班组班次机组都不为空，进入生产加工页面
			mui.openWindow({
				url: '../produce/produce_menus.html',
				id: 'produce_menus',
				createNew: true
			});
		}
	} else if(menu_name_one == 'putin_scan') {
		//清除在装载页面存放于缓存中的车牌号码的信息
		localStorage.setItem("vehicle_id", "");
		clearLocalStorage();
		localStorage.setItem("name", menu_name_one);

		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else if(localStorage.getItem("wprovider_name") == null || localStorage.getItem("wprovider_name") == "" ||
			localStorage.getItem("wprovider_id") == null || localStorage.getItem("wprovider_id") == "") {
			mui.openWindow({
				url: 'select_wprovider_id.html',
				id: 'select_wprovider_id',
				createNew: true,
				extras: {
					startfrom: 'menu'
				}
			});
		} else {
			//add by Luo Yinghui  青岛宝井PDA改造专项
			//如果青岛宝井入库方式开关打开，将进入入库方式选择页面
			if(putin_method_switch == "1" && seg_no == '00113') {
				mui.openWindow({
					url: '../putin/putin_method_menus.html',
					id: 'putin_method_menus',
					createNew: false
				});
			} else if(localStorage.getItem("auto_loc_flag") == "1" &&
				localStorage.getItem("location_type") > "0") {
				mui.openWindow({
					url: 'select_vehicle_no.html',
					id: 'select_vehicle_no',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					createNew: true
				});
			}
		}
	} else if(menu_name_one == ("putout_scan")) {
		console.log(seg_no + "，" + localStorage.getItem("wprovider_id"));
		localStorage.setItem("name", menu_name_one);
		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else if((seg_no == "00138") && (localStorage.getItem("wprovider_id") == null || localStorage.getItem("wprovider_id") == "")) {
			mui.openWindow({
				url: 'select_wprovider_id.html',
				id: 'select_wprovider_id',
				extras: {
					openType: 'menu',
				},
				createNew: true
			});
		} else {
			clearLocalStorage();
			mui.openWindow({
				url: '../putout/putout_list_new.html',
				id: 'putout_list_new',
				createNew: true
			});
		}
	} else if(menu_name_one == 'load_menus') {
		localStorage.setItem("name", menu_name_one);

		if(menu_switch == "1") {
			if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
				localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
				mui.openWindow({
					url: 'select_class.html',
					id: 'select_class',
					createNew: true
				});
			} else if(localStorage.getItem("wprovider_name") == null || localStorage.getItem("wprovider_name") == "" ||
				localStorage.getItem("wprovider_id") == null || localStorage.getItem("wprovider_id") == "") {
				mui.openWindow({
					url: 'select_wprovider_id.html',
					id: 'select_wprovider_id',
					extras: {
						openType: 'popHandPoint',
					},
					createNew: true
				});
			} else if(localStorage.getItem("hand_point_id") == null || localStorage.getItem("hand_point_id") == "" ||
				localStorage.getItem("hand_point_name") == null || localStorage.getItem("hand_point_name") == "") {
				mui.openWindow({
					url: '../loadAndUnload/select_hand_point.html',
					id: 'select_hand_point',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../loadAndUnload/load_unload_menus.html',
					createNew: true
				});
			}
		} else {
			mui.openWindow({
				url: '../loadAndUnload/load_menus.html',
				createNew: true
			});
		}
	} else if(menu_name_one == 'storage_change') {
		mui.openWindow({
			url: '../changeStorage/storage_change.html',
			createNew: true
		});
	} else if(menu_name_one == 'quality_menus') {
		mui.openWindow({
			url: '../quality/quality_menus.html',
			createNew: true
		});
	} else if(menu_name_one == 'check_storage') {
		mui.openWindow({
			url: '../checkStorage/check_storage.html',
			createNew: true
		});
	} 
	//add by gll
	else if(menu_name_one == 'out_search_package'){
		clearLocalStorage();
		mui.openWindow({
			url: '../searchPackage/find_delivery_notice.html',
			id: 'find_dlivery_notice',
			createNew: true
		});
	}
	//end by gll
	else if(menu_name_one == 'search_menus') {
		mui.openWindow({
			url: '../search/search_menus.html',
			createNew: true
		});

	} else if(menu_name_one == 'steel_support_menus') {
		mui.openWindow({
			url: '../bracket/bracket_menus.html',
			createNew: true
		});
	} else if(menu_name_one == 'pack_report') {
		mui.openWindow({
			// url: 'chars.html',
			url: '../report/charts.html',
			createNew: true
		});
	} else if(menu_name_one == 'leave_factory') {
		if(seg_no == '00118') {
			mui.openWindow({
				url: '../leaveFactory/pack_leave_vehicle_scan.html',
				createNew: true
			});
		} else {
			mui.openWindow({
				url: '../leaveFactory/leave_factory_menus.html',
				createNew: true
			});
		}
	} else if(menu_name_one == 'unload') {
		mui.openWindow({
			url: '../unload/unload_pack.html',
			createNew: true
		});
	} else if(menu_name_one == "zc_putout_apply_list") {
		console.log("menu_name_one:" + menu_name_one);
		localStorage.setItem("name", menu_name_one);
		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else {
			mui.openWindow({
				url: '../zcPutout/zc_putout_apply_list.html',
				id: 'zc_putout_apply_list',
				createNew: true,
			});
		}
	} else if(menu_name_one == 'synergy_cc') {
		// add by lal 长春协同收货
		localStorage.setItem("vehicle_id", "");
		localStorage.setItem("name", menu_name_one);
		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else if(localStorage.getItem("wprovider_name") == null || localStorage.getItem("wprovider_name") == "" ||
			localStorage.getItem("wprovider_id") == null || localStorage.getItem("wprovider_id") == "") {
			mui.openWindow({
				url: 'select_wprovider_id.html',
				id: 'select_wprovider_id',
				createNew: true,
				extras: {
					startfrom: 'menu'
				}
			});
		} else {
			if(localStorage.getItem("auto_loc_flag") == "1" &&
				localStorage.getItem("location_type") > "0") {
				mui.openWindow({
					url: 'select_vehicle_no.html',
					id: 'select_vehicle_no',
					extras: {
						open: 'synergy'
					},
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../changChunSynergy/changchun_synergy_scan.html',
					id: 'changchun_synergy_scan',
					createNew: true
				});
			}
		}
	} else if(menu_name_one == 'print_menus') {
		mui.openWindow({
			url: '../print/bluetooth.html',
			createNew: true
		});
	} else if(menu_name_one == 'storage_alloc') {
		mui.openWindow({
			url: '../changeAllocStorage/alloc_storage_location.html',
			createNew: true
		});
	} else if(menu_name_one == 'strip_unload') {
		mui.openWindow({
			url: '../unloadConfirm/strip_unload_pack.html',
			createNew: true
		});
	} else if(menu_name_one == 'allocate_vehicle') {
		mui.openWindow({
			url: '../allocateVehicle/allocate_vehicle_menus.html',
			createNew: true
		});
	} else if(menu_name_one == 'test') { //tangli add test 20190620
		mui.openWindow({
			url: '../20190620test/test.html',
			createNew: true
		});
	} else if(menu_name_one == 'sgm_gf_putin') {
		localStorage.setItem("name", menu_name_one);
		if(localStorage.getItem("team_id") == null || localStorage.getItem("class_id") == "" ||
			localStorage.getItem("team_name") == null || localStorage.getItem("class_name") == "") {
			mui.openWindow({
				url: 'select_class.html',
				id: 'select_class',
				createNew: true
			});
		} else if(localStorage.getItem("wprovider_name") == null || localStorage.getItem("wprovider_name") == "" ||
			localStorage.getItem("wprovider_id") == null || localStorage.getItem("wprovider_id") == "") {
			mui.openWindow({
				url: 'select_wprovider_id.html',
				id: 'select_wprovider_id',
				createNew: true,
				extras: {
					startfrom: 'sgm_gf_putin'
				}
			});
		}else if(localStorage.getItem("auto_loc_flag") == "1" &&
				localStorage.getItem("location_type") > "0") {
				mui.openWindow({
					url: 'select_vehicle_no.html',
					id: 'select_vehicle_no',
					extras: {
						open: 'sgm_gf_putin'
					},
					createNew: true
				});
			} else {
			// 班组班次机组都不为空，进入SGM仓储类别选择页面
			mui.openWindow({
				url: '../sgmGFPutin/sgm_cch.html',
				id: 'sgm_cch',
				createNew: true
			});
		}
	} else if(menu_name_one == 'tm_putin') { //yss add  20210527
		mui.openWindow({
			url: '../tm/tm_list_new.html',
			id: 'tm_list_new',
			createNew: true
		});
	} 
	

}
//退出按钮事件
$("#exit").on("click", function() {
	mui.confirm("是否要退出", "提示", ['取消', '确认'], function(e) {
		if(e.index == 1) {
			//清除数据  返回登录页面
			var webServiceUrl = localStorage.getItem("webServiceUrl");
			var seg_no_info = localStorage.getItem("segNoInfo");
			localStorage.clear();
			exeEdit();
			console.log("back：" + seg_no + "，" + webServiceUrl + "，" + seg_no_info);
			mui.openWindow({
				url: 'login.html',
				extras: {
					seg_no_info: seg_no_info //扩展参数
				},
				createNew: true
			});
		}
	}, 'div');
});
//重写mui返回事件
mui.back = function() {
	var flag = true;
	mui.confirm("是否要退出", "提示", ['取消', '确认'], function(e) {
		if(e.index == 1) {
			exeEdit();
			//清除数据  返回登录页面
			mui.openWindow({
				url: 'login.html',
				createNew: true
			});
		} else {
			flag = false;
		}
	}, 'div');
	return flag;
}

mui('.mui-scroll-wrapper').scroll();
mui('body').on('shown', '.mui-popover', function(e) {
	var team_name = localStorage.getItem("team_name");
	var class_name = localStorage.getItem("class_name");
	var wprovider_name = localStorage.getItem("wprovider_name");
	var user_id = localStorage.getItem("account");
	$("#user_id").html(user_id);
	$("#wprovider_name").html(wprovider_name);
	$("#team_name").html(team_name);
	$("#class_name").html(class_name);
	//console.log('shown', e.detail.id);//detail为当前popover元素
});
mui('body').on('hidden', '.mui-popover', function(e) {
	//console.log('hidden', e.detail.id);//detail为当前popover元素
});

/*
 * 个人信息班组、班次修改
 */
$("#pop_team_update_button").on("click", function() {
	mui.openWindow({
		url: './select_class.html',
		id: 'select_class',
		extras: {
			openType: 'pop_team_update',
		},
		createNew: true
	});
});
/*
 * 个人信息库位修改
 */
$("#pop_wprovider_update_button").on("click", function() {
	mui.openWindow({
		url: 'select_wprovider_id.html',
		id: 'select_wprovider_id',
		extras: {
			openType: 'popwrovider',
		},
		createNew: true,
	});
});
/*
 * 个人信息装卸点修改
 */
$("#pop_hand_point_name_update_button").on("click", function() {
	mui.openWindow({
		url: '../loadAndUnload/select_hand_point.html',
		id: 'select_hand_point',
		extras: {
			openType: 'popHandPoint',
		},
		createNew: true,
	});
});

//add by penglei 清空缓存数据
function clearLocalStorage() {
	/*var team_name=localStorage.getItem("team_name");
	var class_name=localStorage.getItem("class_name");
	var wprovider_name =localStorage.getItem("wprovider_name");
	var user_id = localStorage.getItem("account");
	var webServiceUrl=localStorage.getItem("webServiceUrl");
	var segNo = localStorage.getItem("segNo");
	var team_id = localStorage.getItem("team_id");
	var class_id = localStorage.getItem("class_id");
	var wprovider_id = localStorage.getItem("wprovider_id");
	localStorage.clear();
	localStorage.setItem("team_name",team_name);//班组
	localStorage.setItem("class_name",class_name);//班次
	localStorage.setItem("wprovider_name",wprovider_name);//仓库
	localStorage.setItem("account",user_id);//用户名
	localStorage.setItem("webServiceUrl",webServiceUrl);//url
	localStorage.setItem("segNo",segNo);
	localStorage.setItem("team_id",team_id);
	localStorage.setItem("class_id",class_id);
	localStorage.setItem("wprovider_id",wprovider_id);*/
	localStorage.removeItem("allocate_vehicle_id");
	localStorage.removeItem("vehicle_id");
}

function toWebServices() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + seg_no + '"}';
	var method = "exeQueryWproviderInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
		if(null != data) { //连接成功
			//console.log(data.resultList.length);
			if(data.resultList.length > 0) {
				var chtml = "";
				$.each(data.resultList, function(i, item) {
					if(item.wprovider_id == "P80287") {
						localStorage.setItem("wprovider_name", item.provider_name);
						localStorage.setItem("wprovider_id", item.wprovider_id);
						localStorage.setItem("location_type", item.location_type);
						localStorage.setItem("auto_loc_flag", item.auto_loc_flag);
					};
				});
			}
		} else { //连接失败
			alert("服务器连接异常");
		}
	});
}