/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var imageNameList = []; //图片名称
var allocate_vehicle_id;
var vehicle_id;
var driver_name;
$(function() {
	mui.init({
		swipeBack: false //启用右滑关闭功能
	});
});

/**
 * 页面加载
 */
window.onload = function onload() {
	mui.plusReady(function() {
		self = plus.webview.currentWebview();
		allocate_vehicle_id = self.allocate_vehicle_id;
		vehicle_id = self.vehicle_id;
		driver_name = self.driver_name;
	});
}

/**
 * 删除页面上准备上传的图片
 */
function deleteImage(ths) {
	var btnArray = ["取消", "确认"];
	mui.confirm("确认删除这张图片吗？", "", btnArray, function(e) {
		if(e.index == 1) {
			var id = $(ths).attr("id");
			console.log("被删除的ID：" + id);
			$(ths).remove();
			console.log(files);
			files.splice(id, 1); //从存放文件的数组中删除图片
			base64ImageCode.splice(id, 1);
			//清空img图片标签
			appendStr = "";
			//遍历删除后要显示的图片
			for(var i in files) {
				appendStr = appendStr + '<img onclick="deleteImage(this)" id="' + i + '"src="' + files[i] + '" style="float:left;" width="32%"/>';
			}
			console.log(files);
			console.log(files[0]);
			console.log(files[1]);
		}
	});
}

//删除指定下标数组 传入数组 和指定的下标 最终返回遍历重组后的数组
function remove(arr, key) {
	var arr_tmp = new Array();
	for(key_tmp in arr) {
		if(key_tmp != key) {
			arr_tmp[key_tmp] = arr[key_tmp];
		}
	}
	return arr_tmp;
}

//选取相册中的照片
var files = new Array(); //定义数组存放被选中的图片
var imgName = new Array(); //定义数组存放被选中的图片的名称
var index = 1;
var appendStr = "";
var base64ImageCode = new Array(); //定义数组存放被选中的图片的base64编码
$("#select_image").click(function() {
	var actionbuttons = [{
		title: "从相册中选择"
	}];
	var actionstyle = {
		cancel: "取消",
		buttons: actionbuttons
	};
	plus.nativeUI.actionSheet(actionstyle, function(e) {
		switch(e.index) {

			//case 1: //拍照
			//plus.camera.getCamera().captureImage(function(p) {
			//	appendFile(p);
			//});
			//break;
			case 1: //从相册中选择
				plus.gallery.pick(function(e) {
					if((files.length + e.files.length) > 3) {
						mui.toast('一次性最多只能选择三张照片！');
						return;
					}
					console.log(files.length);
					console.log(e.files.length);
					//遍历选中的图片文件
					for(var i in e.files) {
						console.log(files.push(e.files[i]));
						var fileName = e.files[i].substr(e.files[i].lastIndexOf('/') + 1);
						imgName[i] = fileName;
						var name = "_doc/upload/" + fileName;
						//压缩
						plus.zip.compressImage({
								src: e.files[i], //src: (String 类型 )压缩转换原始图片的路径    
								dst: name, //压缩转换目标图片的路径    
								quality: 40, //quality: (Number 类型 )压缩图片的质量.取值范围为1-100    
								overwrite: true //overwrite: (Boolean 类型 )覆盖生成新文件    
							},
							function(zip) {
								//页面显示图片  
								plus.io.resolveLocalFileSystemURL(zip.target, function(entry) {
									entry.file(function(file) {
										var fileReader = new plus.io.FileReader();
										fileReader.readAsDataURL(file);
										fileReader.onloadend = function(e) {
											var picUrl = e.target.result.toString();
											base64ImageCode.push(picUrl); //将被选中图片的base64编码放入数组
										}
									});
								}, function(e) {
									mui.toast('读取照片文件错误：' + e.message);
								});
							},
							function(error) {
								mui.toast("压缩图片失败，请稍候再试");
							});
						appendStr = appendStr + '<img onclick="deleteImage(this)" id="' + i + '"src="' + e.files[i] + '" style="float:left;" width="32%"/>';
						console.log(appendStr);
					}
					$("#show_image").html(appendStr); //将图片放入页面显示
				}, function(e) {
					console.info("取消选择照片");
				}, {
					filter: "image", //定义只能选择图片的过滤器
					multiple: true, //设置支持多张上传
					animation: true, //
					maximum: 2,
					//system: false,
					selected: files, //设置选中的图片放入files数组
					onmaxed: function() {
						//mui.toast("一次只能选择一张照片");
					} //过滤器只要图片，多选
				});
				break;
				/*case 2:
					getImage();
					break;*/
		}
	});

});

/**
 * 上传图片
 */
$("#upload_image").click(function() {
	if(files.length <= 0) {
		mui.toast("请选择图片！");
		return;
	}
	var btnArray = ['确认', '取消'];
	mui.confirm('确认上传选中图片?', '提示', btnArray, function(e) {
		if(e.index == 0) {
			mui("#upload_image").button('loading');
			uploadImage();
		}
	}, 'div');
});

function uploadImage() {
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeAllocateUploadImg";
	console.log(outUri);
	console.log(driver_name);
	var params = {
		"seg_no": segNo,
		"imageCode": base64ImageCode,
		"user_id": user_id,
		"driver_name": driver_name,
		"vehicle_id": vehicle_id,
		"allocate_vehicle_id": allocate_vehicle_id
	};
	console.log("params:" + params);
	params = JSON.stringify(params);
	params = window.encodeURIComponent(params);
	console.log("params:" + params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 5000,
		async: true,
		cache: false,
		success: function() {
			alert("上传成功！");
			location.go(-1);
//			if(data.result == "1") {
//				//mui.toast(data.result_desc);
//				mui.alert(data.result_desc, "提示", "确定", function() {
//					mui.openWindow({
//						id: "allocate_vehicle_menus",
//						url: "allocate_vehicle_menus.html"
//					});
//				}, 'div');
//				var imageName = data.resultImageName;
//				console.log(imageName);
//				return;
//			} else {
//				mui.alert("连接超时，上传失败！", "提示", "确认", null, "div");
//			}
		},
		error: function() {
			
			mui.toast("服务器异常，请稍后再试！");
		}
	});
}