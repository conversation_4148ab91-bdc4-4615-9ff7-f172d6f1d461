/*        var wprovider_id=localStorage.getItem("wprovider_id");//仓库代码
            var team_id="10";//localStorage.getItem("team_id");//班组代码
			var work_shift="M";//localStorage.getItem("class_id");//班次代码
			var hand_point_id ="ZXDH1800075";//localStorage.getItem("hand_point_id");//装卸点
			var hand_point_name ="物控位-4号门厂内";//localStorage.getItem("hand_point_name");//装卸点名称
			var segNo="00118";//localStorage.getItem("segNo");
			var user_id="dev";//localStorage.getItem("account");
			var webServiceUrl="10.30.91.29:7001";localStorage.getItem("webServiceUrl");
			var factory_area_id= localStorage.getItem("factory_area_id");
			var factory_area_name= localStorage.getItem("factory_area_name");
			var vehicle_id="粤AN1143";//localStorage.getItem("vehicle_id");//车牌号
			var car_trace_no="CLGZ1800002";//localStorage.getItem("car_trace_no");//车辆跟踪号
			var next_target_value =""; //next_target_value  20离厂   10代表下个装卸点
			var hand_big_type="10";//localStorage.getItem("check_type");//装卸；类型  10 装 20卸 30 装卸
			var allocate_vehicle_id="PCDH1803080000000004";//localStorage.getItem("allocate_vehicle_id");//PCDH1803080000000004
			*/

var wprovider_id = localStorage.getItem("wprovider_id"); //仓库代码
var team_id = localStorage.getItem("team_id"); //班组代码
var work_shift = localStorage.getItem("class_id"); //班次代码
var hand_point_id = localStorage.getItem("hand_point_id"); //装卸点
var hand_point_name = localStorage.getItem("hand_point_name"); //装卸点名称
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var factory_area_id = localStorage.getItem("factory_area_id");
var factory_area_name = localStorage.getItem("factory_area_name");
var vehicle_id = localStorage.getItem("vehicle_id"); //车牌号
var car_trace_no = localStorage.getItem("car_trace_no"); //车辆跟踪号
var next_target_value = ""; //next_target_value  20离厂   10代表下个装卸点
var hand_big_type = localStorage.getItem("check_type"); //装卸；类型  10 装 20卸 30 装卸
var allocate_vehicle_id = localStorage.getItem("allocate_vehicle_id"); //PCDH1803080000000004

var putoutVoucherList = new Array(); //出库单据列表
var unUploadPack = new HashMap(); //未上传捆包信息  Map<voucher_id,packList>
var uploadPack = new HashMap(); //已上传捆包信息  Map<voucher_id,packList>
var packInfo = new HashMap(); //捆包信息  Map<pack_id,packInfo>
var contractSubInfo = new HashMap(); //形式提单子项信息  Map<contract_subid,weight_qty_info>
var labelInfo = new HashMap(); //标签号捆包号对照关系信息 

var putinPackList = new Array(); //入库捆包列表

var operatorType = ""; //操作类型 xs销售，zk转库 cc仓储

var pack_total_count = 0; //未扫描捆包个数
var pack_total_weight = 0; //为扫描捆包重量
var successput = 1;
var location_name = "";

$(function() {
	//$("#pack_id")[0].blur();
	/* mui.init();
	$('.scroll-wrapper').scroll();
	var thtml = "";
	thtml += '<li  style=" margin-top:20px; height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>1231</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>123</span>';
	thtml += '<li  style="  height:  40px;">捆包号： <span>1232</span>';
	$("#detail").html(thtml);
	//document.getElementById("test").addEventListener("tap", function() {
		//调用隐藏/显示弹出层
		mui("#popover").popover('toggle', document.getElementById("div"));
	//}) */
	mui.plusReady(function() {
		location_name = plus.webview.currentWebview().location_name;
		$("#location").val(location_name);
	});
	mui.init({
		swipeBack: true, //启用右滑关闭功能
	});
	$("#pack_id")[0].focus();
});
(function($) {
	$('.mui-scroll-wrapper').scroll({
		indicators: true //是否显示滚动条
	});
})(mui);

window.onload = function onload() {
	mui.plusReady(function() {
		console.log(hand_point_name)
		$("#pack_id")[0].focus();
		$("#hand_point_name").val(hand_point_name);
		//console.log(hand_big_type)
		//10开始装货，出库
		if (hand_big_type == "10") {
			$("#allocate_vehicle_id").val(allocate_vehicle_id);
			$("#pack_id")[0].focus();
			queryAllocateVehicle(20);
		} else if (hand_big_type == "20") { //20开始卸货，入库
			$("#allocate_vehicle_id").attr("disabled", "disabled");
			$("#allocate_vehicle_id").attr("style", "background:#CCCCCC");
			$("#pack_id")[0].focus();
		}
	});
	//$("#pack_id")[0].blur();
	//调用下拉框查询
	//getLocationId();
}

/*mui(document.body).on('tap', '#back', function() {
	mui.back();
});*/

mui.back = function() {

	if (!confirm("退出则不保存当前页面信息，确定退出？")) {
		return false;
	}

	/*	var wvs=plus.webview.all();  
		for(var i=0;i<wvs.length;i++){  
		console.log("webview"+i+": "+wvs[i].getURL());  
		} */

	/*if(uploadPack!=null || putinPackList.length>0){
		if (!confirm("已存在扫描的捆包信息，退出则不保存当前页面信息，确定退出？")) {
			return false;
		} 
	}*/
	/*var ws=plus.webview.currentWebview();
	       plus.webview.close(ws);*/
	mui.openWindow({
		url: 'vehicle_load_manage.html',
		id: 'vehicle_load_manage',
		createNew: true
	});
	/**/
	/*var w = plus.webview.getWebviewById('hand_manage');
	      plus.webview.close(w);*/

}

function queryAllocateVehicle(type) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDABoardPutInOroutService';
	//10是页面初始化，和点击配车单的放大镜，查询该车下面所有的配车单号。
	//20是，自己手打的判断配车单是否在该车下
	if (type == 10) {
		allocate_vehicle_id = "";
	} else {
		allocate_vehicle_id = $("#allocate_vehicle_id").val();
	}
	var params = '{"seg_no":"' + segNo + '","allocate_vehicle_id":"' + allocate_vehicle_id + '","vehicle_id":"' +
		vehicle_id + '"}';
	var method = "exeQueryAllocateVehicle";
	console.log(type + "," + params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		console.log(data)
		if (data != null) {
			console.log("data:" + JSON.stringify(data));
			if (data.resultList.length == 0) {
				mui.toast("未查询到配车单信息");
			} else {
				if (type == 20) {
					//mui.toast("请扫描捆包");
					vouchercount = data.resultList.length;
					$.each(data.resultList, function(i, item) {
						$("#allocate_vehicle_id").val(allocate_vehicle_id);
						//$("#pack_id").focus();
						//下载出库单号：物流计划类型（10普通提单 20形式提单 30转库单 40出库申请单）
						if (item.logistics_type == "10" || item.logistics_type == "20") {
							operatorType = "XS";
						} else if (item.logistics_type == "30") {
							operatorType = "ZK";
						} else if (item.logistics_type == "40") {
							operatorType = "CC";
						}
						//根据配车单号下载出库信息
						putoutVoucherDownLoad(item.voucher_num, operatorType, vouchercount);
					});
				} else {
					var chtml = "";
					$.each(data.resultList, function(i, item) {
						chtml = chtml + "<li class='mui-table-view-cell'>" + item.allocate_vehicle_id + "</li>";
					});
					$("#selectdata").html(lihtml);
				}
			}
			$("#pack_id")[0].focus();
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//根据配车单下载捆包信息
function loadPackinfo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	//10是页面初始化，和点击配车单的放大镜，查询该车下面所有的配车单号。
	//20是，自己手打的判断配车单是否在该车下
	if (type == 10) {
		allocate_vehicle_id = "";
	} else {
		allocate_vehicle_id = $("#allocate_vehicle_id").val();
	}
	var params = '{"seg_no":"' + segNo + '","allocate_vehicle_id":"' + allocate_vehicle_id + '","vehicle_id":"' +
		vehicle_id + '","user_id":"' + user_id + '"}';
	var method = "exeQueryAllocateVehicle";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			if (data.resultStatus == "1") {
				console.log("data:" + JSON.stringify(data));

			} else {}
		} else {
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});

}

//----------------------文本框触发事件			

//捆包后面删除按钮
mui(document.body).on('tap', '.packbutton button', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	carnum = el_J.attr("data");

});

function xunhuan() {
	pack_total_count = 0;

	/* let keys = unUploadPack.keys();
	let list = Array.from(keys); */
	if (list.length == 0) {
		pack_total_count = 0;
	} else {
		let keys = unUploadPack.keys();
		let arrs = Array.from(keys);
		for (let key of arrs) {
			$.each(list, function(i, item) {
				var packList = unUploadPack.get(key);
				console.log("11111packList:" + packList.length);
				if (packList.length > 0) {
					pack_total_count = packList.length;
				} else {
					pack_total_count = 0;
				}
			});
		}
		/*$.each(list, function(i, item) {
			var packList = unUploadPack.get(list[i]);
			console.log("11111packList:" + packList.length);
			if(packList.length > 0) {
				pack_total_count = packList.length;
			} else {
				pack_total_count = 0;
			}
		});*/
	}
	console.log("pack_total_count:" + pack_total_count);
	//$("#weiscancount").html("件："+pack_total_count);
	return pack_total_count;
}
//捆包号
$("#pack_id").keypress(function(e) {
	if (e.keyCode == 13) {
		if (hand_big_type == "10") {
			//xunhuan();
			/* if(pack_total_count == 0) {
				mui.alert("该配车单下没有可出库的捆包，请先检查，谢谢", " ", "确定", function() {}, 'div');
				return false;
			} */
			//下面有pack_id定义不要影响原先逻辑
			var packId = $("#pack_id").val();
			packId = formatPackId(packId);
			$("#pack_id").val(packId);
			//有可能扫描的时候扫描的标签号,这边做一次转换
			transLabelID2PackID(packId);

			var pack_id = $("#pack_id").val();
			//校验捆包号相关信息(是否在捆包列表中、是否在已扫捆包列表中、是否在未扫捆包列表中)
			if (!checkPackInfo(pack_id)) {
				$("#pack_id").val("");
				$("#pack_id")[0].focus();
				return false;
			} else {
				var pack_info = packInfo.get(pack_id);
				//初步校验通过、如果形式提单校验超发否则直接处理捆包信息
				if (checkOutDeliverCheck(pack_info)) {
					//添加扫描时间到单据-捆包信息中
					var time = getnowtime();
					var unUploadPackList = unUploadPack.get(pack_info.voucher_id);
					var un_index = getIndexById(pack_info.pack_id, unUploadPackList)
					unUploadPackList[un_index]['scan_time'] = time;
					//测试是否是地址引用  是地址引用
					//console.log("扫描捆包通过超发校验后,unUploadPack:" + JSON.stringify(unUploadPack['map']))

					//将捆包从未扫捆包信息中移动到已扫捆包信息中
					addPack2Upload(pack_info);
				}

				$("#pack_id").val("");
				$("#pack_id")[0].focus();

			}

		} else if (hand_big_type == "20") {
			//首先要判断仓库是否存在
			//校验捆包号是否重复
			if (selectById($('#pack_id').val(), putinPackList)) {
				mui.alert("不能扫描重复的捆包号", " ", "确定", function() {}, 'div');
				$("#pack_id").val("");
				$("#pack_id")[0].focus();
				return false;
			}
			/* var location_id = location_name.split('-')[1];
			if (location_id == "") {
				mui.alert("请选择库位", " ", "确定", function() {}, 'div');
				$("#pack_id").val("");
				return false;
			} else {
				$("#pack_id")[0].focus();
			} */
			//输入或扫描捆包号之后，调用捆包查询接口
			initData();
		}
	}
});
//扫描或者输入配车单号
$("#allocate_vehicle_id").keypress(function(e) {
	if (e.keyCode == 13) {
		if (hand_big_type == "10") {
			queryAllocateVehicle(20);
		} else if (hand_big_type == "20") { //20开始卸货，入库
			mui.toast("卸货不需要选择配车单，请知晓！");
			$("#pack_id").focus();
		}
	}
});

//点击搜索图标跳转页面
mui(document.body).on("tap", ".icon-search", function() {
	if (hand_big_type == "10") {
		queryAllocateVehicle(20);
	} else if (hand_big_type == "20") { //20开始卸货，入库
		mui.toast("卸货不需要选择配车单，请知晓！");
		$("#pack_id").focus();
	}
});

mui(document.body).on('tap', '.vehicleli', function() {
	var next_target = $(this).text();
	$("#allocate_vehicle_id").val(next_target);
	console.log("next_target：" + next_target);
	$("#query_div").hide();
});


//选择库位按钮，弹出页面选择
function selectLocation() {
	mui.openWindow({
		url: 'select_location.html',
		id: 'select_lcoation',
		createNew: true
	});
}

//---------------------------按钮
//装按钮，弹出页面选择
mui(document.body).on('tap', '#zhuang', function() {
	console.log(hand_big_type);
	if (hand_big_type == "10") {
		putoutPackUpload("zhuang");
		//清空各个集合map的值
		hand_big_type = "10"; //开始装货
	} else {
		//调用入库方法
		//清空各个集合map的值
		toPutinPackInfo("zhuang");
		console.log("我要做入库");
		hand_big_type = "10"; //开始装货
	}

});
//卸按钮，弹出页面选择
mui(document.body).on('tap', '#xiehuo', function() {
	console.log(hand_big_type);
	if (hand_big_type == "10") {
		putoutPackUpload("xiehuo");
		//清空各个集合map的值
		hand_big_type = "20"; //开始卸货
	} else {
		toPutinPackInfo("xiehuo");
		console.log("我要做入库");
		hand_big_type = "20"; //开始卸货
	}
});

//装卸结束按钮，弹出页面选择
mui(document.body).on('tap', '#jieshu', function() {
	console.log(location_name)
	/* if(location_name != undefined && location_name != ''){
		var location_id = location_name.split('-')[1];
		if (location_id == "") {
			mui.alert("请选择库位", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			return false;
		} else {
			$("#pack_id")[0].focus();
		}
	} */
	//var count=xunhuan();PVK18B01501
	var count = $("#pack_Ul").html().length;
	console.log($("#pack_Ul").html());
	console.log(count);
	if (count > 100) {
		console.log(hand_big_type);
		if (hand_big_type == "10") {
			putoutPackUpload("jieshu");
			//清空各个集合map的值
		} else {
			toPutinPackInfo("jieshu");
			console.log("我要做入库");
		}
	} else {
		$("#pop_car").toggleClass('show');
		$("#pop_car_info").toggleClass('show');
		$("#hid").val("");
		//查询装卸点信息
		queryzhuangxiedNo(10);
	}

});
/*//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
});*/

function clearListOrMap() {
	//清空捆包下载信息
	unUploadPack.removeAll(); //未上传捆包信息
	uploadPack.removeAll(); //已上传捆包信息
	packInfo.removeAll(); //捆包信息
	contractSubInfo.removeAll(); //形式提单子项信息
	labelInfo.removeAll();
	putoutVoucherList = [];
	$("#pack_Ul").html("");
};
//------------------------------入库调用方法
/**
 * 入库查询捆包事件
 */
var pack_putin_count = 0;

function initData() {
	if (location_name != undefined && location_name != '') {
		var outUri = domainName + "webService.jsp?callback=?";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
		var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + $("#pack_id").val() + '"}';
		console.log(params);
		var method = "exeDownLoadPutinPackInfo";
		$.getJSON(outUri, {
			innerUri: innerUri,
			params: params,
			method: method
		}, function(data) { //如返回对象有一个username属性   
			if (null != data) { //连接成功
				console.log(JSON.stringify(data));
				if (data.product_id == "" || data.product_id == null) {
					mui.alert("未找到该捆包信息", "提示", "确定", function() {}, "div");
				} else {
					//绘制捆包
					var li = $('<li class="mui-table-view-cell packLi" style=" line-height:  45px; height:  45px;">' + data.pack_id +
						' <span>' +
						'<button type="button"class="mui-btn mui-btn-danger packbutton" data="' + data.pack_id + '" onclick=' +
						'deleteLi(this,"' + data.pack_id + '","20")' + '>删除</button>' +
						'</span> </li>');
					$("#pack_Ul").append(li); //123
					var location_id = location_name.split('-')[1];
					console.log("入库捆包查询 存入库位id" + location_id);
					data.location_desc = location_id;
					data.putin_qty = 1;
					putinPackList.push(data);
					console.log(JSON.stringify(putinPackList));
					var putin_weight = 0,
						putin_qty = 0;
					$.each(putinPackList, function(i, item) {
						putin_weight += item.putin_weight;
						putin_qty += item.putin_qty;
					});
					$("#packCount").html(putinPackList.length);
					$("#packNumber").html(putin_qty);
					$("#packWeight").html(putin_weight.toFixed(6));
					//清空信息焦点定位到捆包
					$("#pack_id").val("");
					$("#pack_id")[0].focus();
				}
			} else { //连接失败
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		});
	} else {
		$("#pack_id").val("");
		$("#pack_id")[0].focus();
		mui.alert("请选择库位", " ", "确定", function() {}, 'div');
	}
}

function toPutinPackInfo(buttontt) {
	if (putinPackList.length > 0) {
		if (location_name != undefined && location_name != '') {
			$("#pack_id")[0].blur();
			if (buttontt == "zhuang") {
				mui("#zhuang").button('loading');
			} else if (buttontt == "xiehuo") {
				mui("#xiehuo").button('loading');
			} else {
				mui("#jieshu").button('loading');
			}
			$("#overlay").addClass("overlay");
			var outUri = domainName + "webService_test.jsp";
			var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
			var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","wprovider_id":"' + wprovider_id +
				'","team_id":"' +
				10 + '","vehicle_no":"' + vehicle_id + '","pack_list":' + JSON.stringify(putinPackList) + '}';
			console.log(params);
			params = encodeURI(params, 'utf-8');
			var method = "exeUploadPutinPackInfo";
			$.ajax({
				type: "post",
				async: true,
				url: outUri,
				dataType: "json",
				data: {
					innerUri: innerUri,
					params: params,
					method: method
				},
				success: function(result) {
					if (null != result) {
						console.log("qqqqqqqqqqqqqqqqqqqqqqq" + result.resultDesc);
						if (result.resultStatus == "1") {
							if (buttontt == "jieshu") {
								mui("#jieshu").button('reset');
								$("#pop_car").toggleClass('show');
								$("#pop_car_info").toggleClass('show');
								$("#hid").val("");
								//查询装卸点信息
								queryzhuangxiedNo(10);
							} else if (buttontt == "zhuang") {
								mui("#zhuang").button('reset');
							} else {
								mui("#xiehuo").button('reset');
							}
							$("#overlay").removeClass("overlay");
							//putinPackList.clear();
							mui.alert(result.resultDesc, "提示", "确定", function() {}, "div");
							putinPackList = [];
							console.log(putinPackList.length);
							successput = 0;
							//清空信息焦点定位到捆包
							$("#pack_id").val("");
							$("#pack_Ul").html("");
						} else {
							mui("#jieshu").button('reset');
							mui.alert("出库失败：" + result.resultDesc, "提示", "确定", function() {}, "div");
							$("#overlay").removeClass("overlay");
						}
					} else { //连接失败
						if (buttontt == "zhuang") {
							mui("#zhuang").button('reset');
						} else if (buttontt == "xiehuo") {
							mui("#xiehuo").button('reset');
						} else {
							mui("#jieshu").button('reset');
						}
						$("#overlay").removeClass("overlay");
						mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
					}
				},
				error: function(XMLHttpRequest, textStatus, errorThrown) {
					console.log(getnowtime());
					if (buttontt == "zhuang") {
						mui("#zhuang").button('reset');
					} else if (buttontt == "xiehuo") {
						mui("#xiehuo").button('reset');
					} else {
						mui("#jieshu").button('reset');
					}
					$("#overlay").removeClass("overlay");
					mui.alert("服务器连接异常", "提示", "确定", function() {}, "div");
					this; //调用本次ajax请求时传递的options参数
				}
			});
		} else {
			mui.alert("请选择库位", " ", "确定", function() {}, 'div');
		}
	} else {
		if (buttontt == "jieshu") {
			mui("#jieshu").button('reset');
			$("#pop_car").toggleClass('show');
			$("#pop_car_info").toggleClass('show');
			$("#hid").val("");
			//查询装卸点信息
			queryzhuangxiedNo(10);
		} else if (buttontt == "zhuang") {
			mui("#zhuang").button('reset');
		} else {
			mui("#xiehuo").button('reset');
		}
		$("#overlay").removeClass("overlay");
	}
}

//---------------调用出库方法

/**
 * 出库凭单查询
 * 业务类型必填，时间范围必填，单据号不必填
 */
function putoutVoucherDownLoad(voucher_id, opt_type, vouchercount) {
	console.log(webServiceUrl);
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","operator_type":"' + opt_type +
		'","restrict_date":"","customer_id":"","voucher_id":"' + voucher_id + '"}';
	console.log(params);
	var method = "exePutoutVoucherDownLoad";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		//如返回对象有一个username属性
		if (null != data) {
			console.log(JSON.stringify(data));
			if (data.resultStatus == "1") {
				console.log("222222222222222222222222222 状态等于 1");
				console.log("PDA_switch:" + data.pda_switch + "," + JSON.stringify(data.putoutVoucherList));
				pda_switch = data.pda_switch;
				//判断下载的单据列表类型是否和已扫描的单据一致
				if (opt_type != "" && data.operator_type != opt_type) {
					mui.alert("下载的单据类型与选择的不一致\n请重新选择单据类型或查询同一类型单据", "提示", "确认", function() {}, "div");
					return false;
				} else {
					opt_type = data.operator_type;
				}

				$.each(data.putoutVoucherList, function(i, item) {
					//提单安全性校验 查询捆包时先不输入验证码，等点击出库时输入验证码
					/*var result = inputVerify(item);
					if(result) {*/
					//将提单放进提单集合中
					var index = getIndexByVoucherId(item.voucher_id, putoutVoucherList);
					if (index != -1) {
						/*mui.alert("已存在该单据" + record.voucher_id + ",不能重复扫描", "提示", "确认", function() {}, "div");
						return false;*/
						console.log("已经存在了");
					} else {
						//putoutVoucherList.push(item);
						addPutoutVoucherList(item, vouchercount)
					}
					/*} else { 
						//没有添加成功
					}*/
				});

			}
		} else { //连接失败
			mui.alert("没有下载到出库单据信息", "提示", "确认", function() {}, "div");
		}
	});
}
//出库下载捆包信息
function putoutPackDownLoad(voucher) {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","voucher_id":"' + voucher.voucher_id + '"}';
	console.log(params);
	var method = "exePutoutPackDownLoad";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		//console.log(JSON.stringify(data))
		//console.log("putout_count: " + putout_count);
		if (data != null) {
			if (data.resultStatus == "1") {
				if (data.packList.length > 0) {
					//输出出库需要扫描的捆包信息
					handleDownloadPack(voucher, data.packList); //处理下载的捆包信息
				}
			} else {
				console.log("error");
				confirm_flag = false;
				mui.alert("单据" + voucher.voucher_id + "下载信息：" + data.resultDesc, "提示", "确认", function() {}, "div");
			}
		} else {
			console.log("error");
			confirm_flag = false;
			mui.alert("单据" + voucher.voucher_id + "下载信息：失败", "提示", "确认", function() {}, "div");
		}
	});

}

function handleDownloadPack(voucher, downloadPackList) {
	if (downloadPackList.length > 0) {
		if (voucher.advice_style == "20") { //形式提单先校验订单子项号是否重复
			var flag = true; //标记校验是否通过
			$.each(downloadPackList, function(i, item) {
				if (contractSubInfo.get(item.voucher_subid) != null) {
					mui.alert("形式提单:" + voucher.voucher_id + "与其他形式提单的订单子项号重复,不能在同一批次出库,已经自动移除该单据,请后续重新扫描", "提示", "确认", function() {},
						"div");
					flag = false;
					return false;
				}
			});
			if (flag) {
				var packList = new Array(); //未扫捆包信息集合
				$.each(downloadPackList, function(i, item) {
					//形式提单信息处理
					var info = {};
					info['price_style'] = item.price_style; //计价方式 按数量 按重量
					info['act_weight'] = item.act_weight; //act_weight 已出库重量
					info['act_qty'] = item.act_qty; //act_qty  已出库数量
					info['advice_weight'] = item.advice_weight; //advice_weight 发货通知重量
					info['advice_qty'] = item.advice_qty; //advice_qty  发货通知数量
					contractSubInfo.put(item.voucher_subid, info);

					//捆包信息
					var pack = {};
					pack["pack_id"] = item.pack_id;
					pack["label_id"] = item.label_id;
					pack["product_id"] = item.product_id;
					pack["scan_time"] = '';
					pack["steel_support_id"] = '';
					pack["putin_weight"] = item.putin_weight;
					pack["putin_qty"] = item.putin_qty;
					packList.push(pack); //添加捆包
					packInfo.put(item.pack_id, item); //添加捆包
				});
				unUploadPack.put(voucher.voucher_id, packList);
				console.log("packInfo:" + JSON.stringify(packInfo['map']));
				console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
				console.log("contractSubInfo:" + JSON.stringify(contractSubInfo['map']));
			}
		} else {
			var packList = new Array();
			$.each(downloadPackList, function(i, item) {
				if (i == 0) {
					console.log(JSON.stringify(item))
				}

				var pack = {};
				pack["pack_id"] = item.pack_id;
				pack["label_id"] = item.label_id;
				pack["product_id"] = item.product_id;
				pack["scan_time"] = '';
				pack["steel_support_id"] = '';
				pack["putin_weight"] = item.putin_weight;
				pack["putin_qty"] = item.putin_qty;
				packList.push(pack); //添加捆包
				packInfo.put(item.pack_id, item); //添加捆包
				labelInfo.put(item.label_id, item.pack_id); //添加标签号、捆包对照关系
			});
			unUploadPack.put(voucher.voucher_id, packList);
			//console.log("packInfo:" + JSON.stringify(packInfo['map']));
			//console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
			//console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
		}
	} else {
		console.log(voucher.voucher_id + "下载失败");
	}
}

function transLabelID2PackID(packId) {
	//console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
	var pack_id = labelInfo.get(packId);
	if (pack_id != null && pack_id != packId) {
		$('#pack_id').val(pack_id);
	}
}


// 根据捆包号 查询并弹出详情
function GetPackDetail(pack_id, product_id) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '","product_id":"' + product_id + '"}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "queryPackDetail";
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log(JSON.stringify(data))
			mui.init();
			$('.scroll-wrapper').scroll();
			var thtml = "";
			if (data.LABEL_ID != null && data.LABEL_ID != "") {
				thtml += '<li  style="margin-top:20px; height:  40px;">标签号： <span>' + data.LABEL_ID + '</span>';
			} else {
				thtml += '<li  style="margin-top:20px; height:  40px;">标签号： <span></span>';
			}
			if (data.M_WRAP_ID != null && data.M_WRAP_ID != "") {
				thtml += '<li  style=" height:  40px;">母卷号： <span>' + data.M_WRAP_ID + '</span>';
			} else {
				thtml += '<li  style=" height:  40px;">母卷号： <span></span>';
			}
			if (data.PUTIN_WEIGHT != null && data.PUTIN_WEIGHT != "") {
				thtml += '<li  style="height:  40px;">重量： <span>' + data.PUTIN_WEIGHT + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">重量： <span></span>';
			}
			if (data.PUTIN_QTY != null && data.PUTIN_QTY != "") {
				thtml += '<li  style="height:  40px;">件数： <span>' + data.PUTIN_QTY + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">件数： <span></span>';
			}
			if (data.SPEC != null && data.SPEC != "") {
				thtml += '<li  style="height:  40px;">规格： <span>' + data.SPEC + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">规格： <span></span>';
			}
			if (data.SHOPSIGN != null && data.SHOPSIGN != "") {
				thtml += '<li  style="height:  40px;">牌号： <span>' + data.SHOPSIGN + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">牌号： <span></span>';
			}
			if (data.CUST_PART_ID != null && data.CUST_PART_ID != "") {
				thtml += '<li  style="height:  40px;">客户零件号： <span>' + data.CUST_PART_ID + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">客户零件号： <span></span>';
			}
			if (data.CUSTOMER_NAME != null && data.CUSTOMER_NAME != "") {
				thtml += '<li  style="height:  40px;">客户名称： <span>' + data.CUSTOMER_NAME + '</span>';
			} else {
				thtml += '<li  style="height:  40px;">客户名称： <span></span>';
			}
			$("#detail").html(thtml);
			console.log(thtml)
			//调用隐藏/显示弹出层
			mui("#popover").popover('toggle', document.getElementById("div"));
		},
	});
}

//将捆包信息从未扫捆包列表中移动到已扫捆包列表中
function addPack2Upload(pack_info) {
	console.log("addPack2Upload pack_info: " + JSON.stringify(pack_info));
	var packList_un = unUploadPack.get(pack_info.voucher_id);
	var index_un = getIndexById(pack_info.pack_id, packList_un);

	//判断已上传捆包信息中是否有该单据(如果有单据必定有捆包list最多length为0)
	//并将捆包信息加到已扫捆包信息中
	if (uploadPack.get(pack_info.voucher_id) == null) {
		//添加单据及捆包list
		var list = new Array();
		list.push(packList_un[index_un]);
		uploadPack.put(pack_info.voucher_id, list);
	} else {
		//直接将捆包信息添加到对应单据下的捆包列表
		var list = uploadPack.get(pack_info.voucher_id);
		list.push(packList_un[index_un]);
	}
	var li = $('<li class="mui-table-view-cell packLi" style=" line-height:  45px; height:  45px;">' + pack_info.label_id +
		' <span>' +
		'<button type="button"class="mui-btn mui-btn-danger packbutton" data="' + pack_info.pack_id + '" onclick=' +
		'deleteLi(this,"' + pack_info.pack_id + '","10")' + '>删除</button>' +
		'<button type="button"class="mui-btn mui-btn-primary packbutton2" data="' + pack_info.pack_id + '" onclick=' +
		'GetPackDetail("' + pack_info.pack_id + '","' + pack_info.product_id + '")' + '>详情</button>' +
		'</span> </li>');
	$("#pack_Ul").append(li);
	//将未扫捆包list的捆包信息移除
	packList_un.splice(index_un, 1);
	console.log("after addPack2Upload : ");
	console.log(JSON.stringify(unUploadPack));
	console.log(JSON.stringify(uploadPack));
	var putin_weight = 0,
		putin_qty = 0,
		pack_count = 0;
	var voucherIdList = uploadPack.keySet();
	$.each(voucherIdList, function(i, item) {
		pack_count += uploadPack.get(item).length;
		$.each(uploadPack.get(item), function(i, valitem) {
			putin_weight += valitem.putin_weight;
			putin_qty += valitem.putin_qty;
		});
	});
	$("#packCount").html(pack_count);
	$("#packNumber").html(putin_qty);
	$("#packWeight").html(putin_weight.toFixed(6));
	//更新合计框内信息
	//refreshSum(pack_info);
}
//扫描捆包时校验捆包号相关信息(是否在捆包列表中、是否在已扫捆包列表中、是否在未扫捆包列表中)
function checkPackInfo(pack_id) {
	//捆包信息中是否有该捆包信息
	var pack_info = packInfo.get(pack_id);
	if (pack_info == null) {
		mui.alert("该" + pack_id + "不在本次出库捆包清单中", "提示", "确定", function() {}, "div");
		return false;
	}
	//捆包是否重复扫描
	var voucher_id = pack_info.voucher_id;

	//有可能已上传捆包map为null所以先校验是否拿得到单据对应的捆包列表
	if (uploadPack.get(voucher_id) != null) {
		var index1 = getIndexById(pack_id, uploadPack.get(voucher_id));
		if (index1 != -1) {
			mui.alert("该" + pack_id + "捆包已经扫描,不可重复扫描", "提示", "确定", function() {}, "div");
			return false;
		}
	}

	//捆包是否在未扫捆包信息中
	var index2 = getIndexById(pack_id, unUploadPack.get(voucher_id));
	if (index2 == -1) {
		mui.alert("该" + pack_id + "捆包不在未扫捆包列表中", "提示", "确定", function() {}, "div");
		return false;
	}
	//所有校验通过
	return true;
}

/**
 * 扫描捆包的时候，判断是否是形式提单，且判断是否超发
 * @param {Object} pack_info
 */
function checkOutDeliverCheck(pack_info) {
	console.log("pack_info" + JSON.stringify(pack_info));
	if (pack_info.advice_style == "20") { //捆包属于形式提单
		//根据订单子项号获取订单子项发货信息
		var voucherSubInfo = contractSubInfo.get(pack_info.voucher_subid);
		console.log("contractSubInfo before:" + JSON.stringify(contractSubInfo));
		//按重量发货
		if (pack_info.price_style == "10") {
			//发货通知重量
			var advice_weight = parseFloat(voucherSubInfo.advice_weight);
			//已出库量
			var act_weight = parseFloat(voucherSubInfo.act_weight);
			//当前捆包重量
			var putin_weight = parseFloat(pack_info.putin_weight);
			//超发量
			var super_weight = (act_weight + putin_weight) - advice_weight;
			console.log("aaaa" + advice_weight + ",    " + act_weight + ",     " + putin_weight + ",   " + super_weight);
			if (super_weight > 0) {
				mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_weight, "提示", "确认", function() {}, "div");
				return false;
			} else {
				voucherSubInfo.act_weight = parseFloat(act_weight + putin_weight);
				contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
				console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
				return true;
			}
			//数量
		} else if (pack_info.price_style == "20") { ////按数量发货
			//发货通知数量
			var advice_qty = parseFloat(voucherSubInfo.advice_qty);
			//已出库数量
			var act_qty = parseFloat(voucherSubInfo.act_qty);
			//当前捆包数量
			var putin_qty = parseFloat(pack_info.putin_qty);
			//超发量
			var super_qty = (act_qty + putin_qty) - advice_qty;
			if (super_qty > 0) {
				mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_qty, "提示", "确认", function() {}, "div");
				return false;
			} else {
				voucherSubInfo.act_qty = parseFloat(act_qty + putin_qty);
				contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
				console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
				return true;
			}
		}
	} else {
		return true;
	}
}

//出库列表中添加记录putoutVoucherList
function addPutoutVoucherList(record, vouchercount) {
	var index = getIndexByVoucherId(record.voucher_id, putoutVoucherList);
	if (index != -1) {
		vouchercount = vouchercount - 1;
	} else {
		putoutVoucherList.push(record);
		//出库单已经下载完成开始下载捆包
		console.log(vouchercount);
		console.log(putoutVoucherList.length);
		//if(putoutVoucherList.length==vouchercount){
		$.each(putoutVoucherList, function(i, voucher) {
			//下载捆包信息
			putoutPackDownLoad(voucher);
		});
		//}
	}
}

//扫描捆包后面点击删除，从已扫描捆包集合中删除，列表中删除，未扫描捆包集合中添加捆包
function deleteLi(ele, packId, pptype) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '提示', btnArray, function(e) {
		if (e.index == 0) {
			li.parentNode.removeChild(li); //删除DOM节点，
			//但是同时也要删除数据容器
			var pack_info = packInfo.get(packId);
			if (pptype == "10") { //出库
				delPackFromUpload(pack_info);
			} else {
				//删除入库已扫描捆包
				delputinPackUpload(packId);
			}
		}
	}, 'div');
}

function delputinPackUpload(pack_id) {
	console.log("delPackFromUpload pack_info: " + JSON.stringify(pack_id));
	var index_up = getIndexById(pack_id, putinPackList);
	//将已扫捆包list的捆包信息移除
	putinPackList.splice(index_up, 1);
	console.log("putinPackList:" + JSON.stringify(putinPackList));
	var putin_weight = 0,
		putin_qty = 0;
	$.each(putinPackList, function(i, item) {
		putin_weight += item.putin_weight;
		putin_qty += item.putin_qty;
	});
	$("#packCount").html(putinPackList.length);
	$("#packNumber").html(putin_qty);
	$("#packWeight").html(putin_weight.toFixed(6));
}
//将捆包信息从未扫捆包列表中移动到已扫捆包列表中
function delPackFromUpload(pack_info) {
	console.log("delPackFromUpload pack_info: " + JSON.stringify(pack_info));
	var packList_up = uploadPack.get(pack_info.voucher_id);
	var index_up = getIndexById(pack_info.pack_id, packList_up);

	//未上传捆包信息中肯定有该单据信息
	//直接将捆包信息添加到对应单据下的捆包列表
	var list = unUploadPack.get(pack_info.voucher_id);
	list.push(packList_up[index_up]);

	//将已扫捆包list的捆包信息移除
	packList_up.splice(index_up, 1);
	console.log("after delPackFromUpload : ");
	console.log("unUploadPack:" + JSON.stringify(unUploadPack));
	console.log("uploadPack" + JSON.stringify(uploadPack));
	var putin_weight = 0,
		putin_qty = 0;
	$.each(uploadPack.get(pack_info.voucher_id), function(i, item) {
		putin_weight += item.putin_weight;
		putin_qty += item.putin_qty;
	});
	$("#packCount").html(uploadPack.get(pack_info.voucher_id).length);
	$("#packNumber").html(putin_qty);
	$("#packWeight").html(putin_weight.toFixed(6));
	//修改超发相关信息
	modiOutDeliverInfo(pack_info);
}

//修改超发相关信息
function modiOutDeliverInfo(pack_info) {
	if (pack_info.advice_style == "20") { //捆包属于形式提单
		//根据订单子项号获取订单子项发货信息
		var voucherSubInfo = contractSubInfo.get(pack_info.voucher_subid);
		console.log("contractSubInfo before:" + JSON.stringify(contractSubInfo));
		//按重量发货
		if (pack_info.price_style == "10") {
			//已出库量
			var act_weight = parseFloat(voucherSubInfo.act_weight);
			//当前捆包重量
			var putin_weight = parseFloat(pack_info.putin_weight);

			voucherSubInfo.act_weight = parseFloat(act_weight - putin_weight);
			contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
			console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
		} else if (pack_info.price_style == "20") { ////按数量发货
			//已出库数量
			var act_qty = parseFloat(voucherSubInfo.act_qty);
			//当前捆包数量
			var putin_qty = parseFloat(pack_info.putin_qty);

			voucherSubInfo.act_qty = parseFloat(act_qty - putin_qty);
			contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
			console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
		}
	} else {;
	}
}

//出库捆包上传post方法
function putoutPackUpload(buttontt) { //出库捆包上传
	/**
	 * 出库之前，先判断是否需要输入验证码
	 * 
	 */
	var result = true;
	$.each(putoutVoucherList, function(j, jtem) {
		if (inputVerify(jtem)) {
			result = true;
		} else {
			result = false;
		}
	});
	if (result) {
		//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
		if (buttontt == "zhuang") {
			mui("#zhuang").button('loading');
		} else if (buttontt == "xiehuo") {
			mui("#xiehuo").button('loading');
		} else {
			mui("#jieshu").button('loading');
		}
		$("#overlay").addClass("overlay");
		//如果出现异常或者超时设置半分钟后可以再点一次
		setTimeout(function() {
			if (buttontt == "zhuang") {
				mui("#zhuang").button('reset');
			} else if (buttontt == "xiehuo") {
				mui("#xiehuo").button('reset');
			} else {
				mui("#jieshu").button('reset');
			}
			$("#overlay").removeClass("overlay");
		}.bind(this), 5000);

		var outUri = domainName + "webService_test.jsp";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		//拼接参数信息
		var params = initPutoutPackUploadParams();
		if (params.voucherList.length > 0) {
			params = JSON.stringify(params);
			console.log(params);
			//params=encodeURI(params,'utf-8');
			var method = "exeNewPutoutPackUpload";
			$.ajax({
				type: "post",
				async: true,
				url: outUri,
				data: {
					innerUri: innerUri,
					params: params,
					method: method
				},
				dataType: "json",
				success: function(result) {
					console.log(JSON.stringify(result))
					if (result != null) {
						//console.log("data:"+JSON.stringify(data));
						if (buttontt == "zhuang") {
							mui("#zhuang").button('reset');
						} else if (buttontt == "xiehuo") {
							mui("#xiehuo").button('reset');
						} else {
							mui("#jieshu").button('reset');
						}
						$("#overlay").removeClass("overlay");
						if (result.returnStatus == "1") {
							mui.alert("" + result.returnDesc, "提示", "确定", function() {
								clearListOrMap();
								successput = 0;
								if (buttontt == "zhuang") {
									//清理之后，点击继续装货之后。重新根据配车单查询该配车单下所有捆包
									queryAllocateVehicle(20);
								}
								if (buttontt == "jieshu") {
									$("#pop_car").toggleClass('show');
									$("#pop_car_info").toggleClass('show');
									$("#hid").val("");
									//查询装卸点信息
									queryzhuangxiedNo(10);
								}
							}, "div");
						} else {
							mui.alert("出库失败：" + result.returnDesc, "提示", "确定", function() {}, "div");
						}
					} else {
						if (buttontt == "zhuang") {
							mui("#zhuang").button('reset');
						} else if (buttontt == "xiehuo") {
							mui("#xiehuo").button('reset');
						} else {
							mui("#jieshu").button('reset');
						}
						$("#overlay").removeClass("overlay");
						mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
					}
				},
				error: function() {
					if (buttontt == "zhuang") {
						mui("#zhuang").button('reset');
					} else if (buttontt == "xiehuo") {
						mui("#xiehuo").button('reset');
					} else {
						mui("#jieshu").button('reset');
					}
					$("#overlay").removeClass("overlay");
					mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
				}
			});
		} else {
			$("#overlay").removeClass("overlay");
			if (buttontt == "zhuang") {
				mui("#zhuang").button('reset');
			} else if (buttontt == "xiehuo") {
				mui("#xiehuo").button('reset');
			} else {
				mui("#jieshu").button('reset');
			}
			$("#pop_car").toggleClass('show');
			$("#pop_car_info").toggleClass('show');
			$("#hid").val("");
			//查询装卸点信息
			queryzhuangxiedNo(10);
		}
	}
}

//初始化上传出库捆包参数
function initPutoutPackUploadParams() {
	var params = {};
	params['seg_no'] = segNo;
	params['user_id'] = user_id;
	params['team_id'] = team_id;
	params['work_shift'] = work_shift;
	params['operatorType'] = operatorType;
	params['vechile_id'] = vehicle_id;
	var voucherList = new Array();
	/* var voucherList = new Array();
	let keys = uploadPack.keys();
	let arrs = Array.from(keys);
	for(let key of arrs) {
	//for(var key of uploadPack.keys()){
		var voucherIdList = uploadPack.get(key);
		$.each(voucherIdList, function(i) {
		if(uploadPack.get(voucherIdList[i]).length > 0) { //该单据下有捆包要出库
			console.log("voucherIdList：" + JSON.stringify(voucherIdList));
			var voucher = {};
			//从出库单据列表中取单据验证码
			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i], putoutVoucherList)]).identify_code;
			console.info("identify_codeidentify_codeidentify_code::" + identify_code);
			console.log("identify_code:" + identify_code);
			voucher['identify_code'] = identify_code;
			voucher['voucher_id'] = voucherIdList[i];
			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
			//往单据列表中添加信息
			voucherList.push(voucher);
		}
	});
	} */

	var voucherIdList = uploadPack.keySet();
	$.each(voucherIdList, function(i) {
		if (uploadPack.get(voucherIdList[i]).length > 0) { //该单据下有捆包要出库
			console.log("voucherIdList：" + JSON.stringify(voucherIdList));
			var voucher = {};
			//从出库单据列表中取单据验证码
			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i], putoutVoucherList)]).identify_code;
			console.info("identify_codeidentify_codeidentify_code::" + identify_code);
			console.log("identify_code:" + identify_code);
			voucher['identify_code'] = identify_code;
			voucher['voucher_id'] = voucherIdList[i];
			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
			//往单据列表中添加信息
			voucherList.push(voucher);
		}
	});
	params['voucherList'] = voucherList;
	return params;
}

//--------装卸结束的下一个
//下个目标
mui(document.body).on('tap', '#morebut', function() {
	console.log("2222");
	hhtype = "20"; //更多
	queryzhuangxiedNo(hhtype);
});

function queryzhuangxiedNo(hhtype) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var openType = ''; //openType hand_point_end表示下个装卸点查询 ，其他则按选择装卸点管理的逻辑
	if (hhtype == "10") {
		openType = 'hand_point_end';
	} else {
		openType = '';
	}
	var params = '{"seg_no":"' + segNo + '","factory_area":"' + factory_area_id + '","car_trace_no":"' + car_trace_no +
		'","openType":"' + openType + '"}';
	var method = "exeQueryHandPoint";
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var lihtml = '<li class="mui-table-view-cell">' +
		'<a class="mui-navigate-right">' +
		'<div style="width: 48%; float: left; text-align: left;" >' +
		'<label><span class="vehicle_num">离厂</span>' +
		'<span class="point_name"  hidden="hidden">离厂</span>' +
		'</label>' +
		'</div>' +
		'</a>' +
		'</li>';
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			console.log("data:" + JSON.stringify(data));
			$.each(data.resultList, function(i, item) {
				lihtml = lihtml +
					'<li class="mui-table-view-cell">' +
					'<a class="mui-navigate-right">' +
					'<div style="width: 48%; float: left; text-align: left;" >' +
					'<label><span class="vehicle_num" hidden="hidden">' + item.hand_point_id + '</span>' +
					'<span class="point_name">' + item.hand_point_name + '</span>' +
					'</label>' +
					'</div>' +
					'</a>' +
					'</li>';

			});
			$("#carList").html("");
			$("#carList").append(lihtml);
		} else {
			$("#carList").html("");
			$("#carList").append(lihtml);
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}

	});

}
//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	carnum = el_J.find(".vehicle_num").text();
	carname = el_J.find(".point_name").text();
	$("#hid").val(carnum);
	$("#hidname").val(carname);
});
//搜索
mui(document.body).on('tap', '#query_button', function() {
	hhtype = "10";
	queryzhuangxiedNo(hhtype);
});
//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	carnum = $("#hid").val();
	carname = $("#hidname").val();
	if (carnum == "" || carnum == null || carnum == "undefine") {
		mui.alert("请选择下个目标离厂或到下一个", "提示", "确认", function() {}, "div");
		return false;
	} else {
		/*$("#next_hand_point").val(carname);
						$("#next_hand_point_id").val(carnum);*/

		if (carname == "离厂") {
			next_target_value = "20";
		} else {
			next_target_value = "10";
		}
		mui("#confirm").button('reset');
		$("#overlay").addClass("overlay");
		exeConfigVeicleFinishHandPoint();
	}
});
//结束装卸货
function exeConfigVeicleFinishHandPoint() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var sspointid = "";
	if ($("#hid").val() == "离厂") {
		sspointid = "";
	} else {
		sspointid = $("#hid").val();
	}
	var params = '{"seg_no":"' + segNo + '","user_name":"' + user_id + '","car_trace_no":"' + car_trace_no +
		'","hand_point_id":"' + sspointid + '","next_target":"' + next_target_value + '"}';
	var method = "exeVeicleFinishHandPoint";
	console.log(params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if (data != null) {
			console.log(JSON.stringify(data));
			if (data.resultStatus == "1") {
				if ($("#hid").val() != "离厂") {
					//屏蔽原因 工厂基本上一个作业点一个pda 
					//localStorage.setItem("hand_point_id",next_hand_point_id);
					//localStorage.setItem("hand_point_name",next_hand_point);
				} else {
					localStorage.removeItem('vehicle_id');
					localStorage.removeItem('car_trace_no');
				}
				$("#overlay").removeClass("overlay");
				$("#pop_car").toggleClass('show');
				$("#pop_car_info").toggleClass('show');
				mui.openWindow({
					url: 'vehicle_load_manage.html',
					id: 'vehicle_load_manage',
					createNew: true
				});

			} else {
				mui.alert("失败！原因：" + data.codeList, "提示", "确定", function() {}, 'div');
				$("#overlay").removeClass("overlay");
				//$("#pop_car").toggleClass('show');
				//$("#pop_car_info").toggleClass('show');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			$("#overlay").removeClass("overlay");
			//$("#pop_car").toggleClass('show');
			//$("#pop_car_info").toggleClass('show');
			return;
		}
	});
}

/* //库位下拉框查询		
function getLocationId() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	console.log(wprovider_id)
	var location_name = "2号";
	var params = JSON.stringify({
		seg_no: segNo,
		wprovider_id: wprovider_id,
		location_name : location_name
	});
	var method = "exeQueryLocationId";
	var selectHtml = "<option value='0' selected='true'>--请选择--</option>";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if(null != data) {
			$.each(data.returnList, function(i, item) {
				//console.log(item.location_name + "1111111111111111111" + item.location_id);
				selectHtml = selectHtml + "<option value='" + item.location_id + "'>" + item.location_name + "  —  " + item.location_id + "</option>";
			});
		}
		$("#pack_location").html(selectHtml);
		var location_id = $.trim($("#pack_location option:selected").val());
	});
} */

/* //下拉框变更触发事件
function location_id_change(location_id_value) {
	$("#pack_id")[0].focus();
} */
