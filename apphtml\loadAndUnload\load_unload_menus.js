var segNo = localStorage.getItem("segNo");
mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});
//如果是高强钢直接按原来的界面
$(function() {
	if ('00138' == segNo) {
		mui.openWindow({
			url: 'putout_list_new.html',
			id: 'putout_list_new',
			createNew: true
		});
	}

	//20210707天津宝钢天津宝井开通强制叫号
	if ('00138' != segNo && '00126' != segNo && '00145' != segNo && '00129' != segNo && '00181' != segNo) {
		$("#call_number_force").css("display", "none");
	}
	if ('000126' != segNo) {
		$("#hand_point_change").css("display", "none");
	}
});
//厂内装卸点管理
mui(document.body).on('click', '#factory_hand_point', function() {
	mui.openWindow({
		//url:'quality_seal.html',
		url: 'hand_point_manage.html',
		id: "hand_point_manage",
		createNew: true
	});
});
//车辆装卸货管理
mui(document.body).on('click', '#vehicle_hand_point', function() {
	if (localStorage.getItem("hand_point_id") == null || localStorage.getItem("hand_point_id") == "" ||
		localStorage.getItem("hand_point_name") == null || localStorage.getItem("hand_point_name") == "") {
		mui.openWindow({
			url: 'select_hand_point.html',
			id: 'select_hand_point',
			createNew: true
		});
	} else {
		mui.openWindow({
			//url:'quality_commute.html',
			url: 'vehicle_load_manage.html',
			id: 'vehicle_load_manage',
			createNew: true
		});
	}

});
mui(document.body).on('tap', '#back', function() {
	mui.back();
});
mui.back = function() {
	mui.openWindow({
		id: "index",
		url: "../public/index.html",
		createNew: true
	});
}

//强制叫号
mui(document.body).on('click', '#call_number_force', function() {
	if ('00138' == segNo || '00145' != segNo || '00129' != segNo) {
		mui.openWindow({
			url: 'call_number_force.html',
			id: "call_number_force",
			createNew: true
		});
	} else if ('00126' == segNo) {
		mui.openWindow({
			url: 'call_number_force_fsbg.html',
			id: "call_number_force_fsbg",
			createNew: true
		});
	} else {
		alert("系统错误，请联系管理员");
	}

});
//装卸点变更
mui(document.body).on('click', '#hand_point_change', function() {
	mui.openWindow({
		url: 'hand_point_change.html',
		id: "hand_point_change",
		createNew: true
	});
});

//add by gll 补充电子签名
mui(document.body).on('click', '#btn_signature', function() {
	mui.openWindow({
		url: 'supplement_signature.html',
		id: "supplement_signature",
		createNew: true
	});
});

// add by Global
$(function() {
	if ('00138' == segNo ) {
		$("#btn_qianming").show();
		//console.log("55555");
		}
		if (segNo != "00181") {
			$("#fenpei").css("display", "none");
			}
		})
		
//add by gll 上海不锈出厂物流
mui(document.body).on("tap", "#fenpei", function() { 
mui.openWindow({
		url: '../buxiu/loading_distribution.html',
		id: 'loading_distribution',
		extras: {
			openType: 'vehicle_load_manage',
		},
		createNew: true  
	});
});