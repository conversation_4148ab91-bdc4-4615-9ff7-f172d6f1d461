/**
 * 初始化变量信息 
 */
var scanSteelSupportList= new Array();//选中之后要綁定的列表
var steelSupportObj = null; 		  //当前扫描的铁托架	

$(function(){
	mui.init({
		swipeBack:true //启用右滑关闭功能
	});
});

mui.ready(function(){
	$("#pack_id").focus();
/*	$("#pack_id").val("P141204012");*/
});

//捆包号
$("#pack_id").keypress(function(e){
	if(e.keyCode == 13){
		var pack_id = $("#pack_id").val();
		 if(pack_id==null || pack_id==""){
		 	mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
		 	$("#pack_id").focus();
		 	return;
		 }else{
		 /**
		  * 调用查询该捆包号是否存在的方法
		  */
		 	mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
					&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
					queryUnbind(pack_id,"pack_id");
				  }else{
				  	$("#steel_support_id").focus();
				  }
		  });
		 }
	}
});

 //铁托架号
$("#steel_support_id").keypress(function(e){
	if(e.keyCode == 13){
		var steel_support_id = $("#steel_support_id").val();
		 if(steel_support_id==null || steel_support_id==""){
		 	mui.alert("请扫描或者输入铁托架号","提示","确定",null,'div');
		 	$("#steel_support_id").focus();
		 	return;
		 }else{
		 	queryUnbind(steel_support_id,"steel_support_id");
		 }

	}
});

//按钮确定事件
mui(document.body).on('tap','#comfir_button',function(){
	//校验
	var pack_id = $("#pack_id").val();
	 if(pack_id==null || pack_id==""){
	 	mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
	 	$("#pack_id").focus();
	 	return;
	}
	
	var steel_support_id = $("#steel_support_id").val();
 	if(steel_support_id==null || steel_support_id==""){
	 	mui.alert("请扫描或者输入铁托架号","提示","确定",null,'div');
	 	$("#steel_support_id").focus();
	 	return;
	}
	//校验当前扫描捆包号是否重复
	if(getIndexByPackId(steelSupportObj.pack_id,scanSteelSupportList) > -1){
		mui.alert("捆包"+steelSupportObj.pack_id+"不能重复上传", "提示", "确定", function() {}, 'div');		
		return;
	}else{
		//将当前扫描的记录增加到集合中
		scanSteelSupportList.push(steelSupportObj);
		//将捆包记录显示在页面上
		loadingContentList();
		
		$("#pack_id").val("");
		$("#steel_support_id").val("");
	}
});

//按钮解绑事件
mui(document.body).on('tap','#unbind_button',function(){
	if(scanSteelSupportList.length == 0){
		mui.alert("已扫描列表无记录,请检查", "提示", "确定",function(){},'div');
		return;
	}else{
		mui("#unbind_button").button('loading');
		$("#overlay").addClass("overlay");
		
		/**
		 * 调用绑定接口方法
		 */
		exeSteelSupportUnBind();
	}	
});



/*方法*/

/**
 * 捆包与铁托架  解绑扫描方法
 * idValue type=pack_id: 捆包号;type=steel_support_id: 铁托架号或者铁托架编号
 * type  pack_id:扫描捆包号带出铁托架信息 ;steel_support_id:扫描铁托架带出捆包号信息
 */
function queryUnbind(idValue,type) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"'+segNo+'","user_id":"'+account+'","idValue":"'+idValue+'","type":"'+type+'"}';
	console.log(params);
	params=encodeURI(params,'utf-8');
	var method = "queryUnbind";
	
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:2000,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
				if(data.resultStatus == "1"){
					$.each(data.resultList, function(i, item) {
						var pack_id ="";
						if(type == "pack_id"){
							$("#steel_support_id").val(item.steel_support_id);
							pack_id = item.pack_id;
						}else{
							$("#pack_id").val(item.lock_pack_id);
							pack_id = item.lock_pack_id;
						}
						steelSupportObj = {
											pack_id: pack_id,
											steel_support_id: item.steel_support_id,
											steel_support_name: item.steel_support_name,
											steel_support_status:'20'
						};
					});
				}else{
					if(type == "pack_id"){
						mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
						$("#pack_id").val("");
						$("#pack_id").focus();
						return;
					}else{
						mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
						$("#steel_support_id").val("");
						$("#steel_support_id").focus();
						return;
					}

				}
			} else {//连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
	  	 }
		}
	});
}

//执行铁托架解绑
function exeSteelSupportUnBind() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","steel_support_list":' + JSON.stringify(scanSteelSupportList) + '}';
	var method = "exeUnbindSteelSupport";

	$.ajax({
		type:"post",
		async:true,
		timeout:30000,
		url:outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data != null) {
				mui("#unbind_button").button('reset');
				$("#overlay").removeClass("overlay");
				if(data.resultStatus == "1") {
					  mui.alert("解绑成功", "提示", "确定", null, 'div');
					      $("#pack_id").val("");
						  $("#steel_support_id").val("");
					      $("#pack_id").focus();
					      $("#companyList").html("");
					      scanSteelSupportList = [];
					return ;
				} else {
					var errorinfo = data.resultDesc;
					   mui.alert("解绑失败！原因："+errorinfo, "提示", "确定", function() {}, 'div');
					 	return ;
				}
			} else { //连接失败
				mui("#unbind_button").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("工贸服务器处理异常", "提示", "确定",null, 'div');
				return ;
			}
		},
		error: function(xhr, type, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if(type == "timeout") {
					xhr.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				}else{
					mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
				}
				mui("#unbind_button").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}

function loadingContentList(){
	var lihtml = "";
	$.each(scanSteelSupportList, function(i, item) {
	 lihtml = lihtml +'<li class="mui-table-view-cell">'+
					'<div class="mui-slider-right mui-disabled">'+
						'<a class="mui-btn mui-btn-red mui-icon"  onclick='+'deleteLi(this,"'+item.pack_id+'")'+'>删除</a>'+
					'</div>'+
					'<div class="mui-slider-handle">'+
						'<div>'+
							'<div id="steel_support_id_1" data="'+item.pack_id+'">'+item.pack_id+'</div>'+
								'<div>'+
									'<div id="spec" class="left"><span>号</span><label>'+item.steel_support_id+'</label></div>'+
									'<div id="steel_support_name"><span>名</span><label>'+item.steel_support_name+'</label></div>'+
							'	</div>'+
						'</div>'+
					'</div>'+
				'</li>';
		});
		$("#companyList").append(lihtml);
}

//删除
function deleteLi(ele, pack_id) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '警告', btnArray, function(e) {
		if(e.index == 0) {
			//console.log("pack_id>>>>>>>>>>>>>>>>>>>>>>>>>" + pack_id);
			var indexd = getIndexByPackId(pack_id, scanSteelSupportList);
			console.log("删除：" + pack_id + "，index:" + indexd);
			if(indexd > -1) {
				scanSteelSupportList.splice(indexd, 1);
			}
				//删除捆包
			li.parentNode.removeChild(li);
		} else {
			setTimeout(function() {
				$.swipeoutClose(li);
			}, 0);
		}
	});
}

//获取当前捆包的下标

function getIndexByPackId(pack_id,scanSteelSupportList){
	var index = -1;
	$.each(scanSteelSupportList, function(i,value) {
		if(value.pack_id == pack_id){
			index = i;
			return index;
		}
	});
	return index;
}