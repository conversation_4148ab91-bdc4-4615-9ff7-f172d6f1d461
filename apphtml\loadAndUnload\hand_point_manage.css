.mui-table-view-radio .mui-table-view-cell>a:not(.mui-btn) {
	margin-right: -75px;
}

mui(document.body).on('tap', '.mui-switch-handle', function() {
	$(this).parent().toggleClass('mui-active');
}

);
mui(document.body).on('tap', '.mui-switch', function() {
	$(this).toggleClass('mui-active');
}

);
.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
}

.icon-setting {
	position: absolute;
	left: 290px;
	top: 10px;
	z-index: 5;
	background-image: url(../resource/setting.gif);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}