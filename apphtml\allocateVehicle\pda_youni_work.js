//滚动效果
(function($) {
	$(".mui-scroll-wrapper").scroll({
		bounce: false, //滚动条是否有弹力默认是true
		indicators: true, //是否显示滚动条,默认是true
	});
})(mui);

var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var user_id = localStorage.getItem("account"); //采用localStorage存储数据
var allocate_vehicle_id; //预发货清单主项号
var allocate_vehicle_subid; //预发货清单子项号
var AllocateInfo = new HashMap(); //预发货清单主项信息 <allocate_vehicle_id,AllocateInfo>
var AllocateInfoSub = new HashMap(); //预发货清单子项信息 <allocate_vehicle_id,AllocateInfo>
var AllocateList = new Array(); //选中预发货清单主项信息
var AllocateListSub = new Array(); //选中预发货清单子项信息

/*
 * 页面初始化
 */
window.onload = function onload() {
	mui.plusReady(function() {
		$("#btn_query").hide();
		$("#btn_cancel").hide();
		mui('.mui-slider').slider().setStopped(true); //禁止tab滑动左右切换
		exeQueryAllocateVehicleId(); //页面加载时查询
	});
}
//查询子项
mui(document.body).on('tap', '#query_button', function() {
	if ("" == allocate_vehicle_id || null == allocate_vehicle_id || undefined == allocate_vehicle_id) {
		mui.confirm('请选择预发货清单号！', '提示', ['确认'], function(e) {
		}, 'div');
		return false;
	}
	$("#btn_save").hide();
	$("#btn_cancel").show();
	$("#btn_query").show();
	exeQueryAllocateVehicleIdSub();
});

//返回主项
mui(document.body).on('tap', '#youni_button', function() {
	$("#btn_save").show();
	$("#btn_cancel").hide();
	$("#btn_query").hide();
});

/**
 * 查询预发货清单
 */
function exeQueryAllocateVehicleId() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeQueryYouniWorkMData";
	var params = '{"seg_no":"' + seg_no + '"}';
	var AllocateHtml = "";
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(data) {
			//console.log(JSON.stringify(data))
			if (data != null) {
				$.each(data.resultDesc, function(i, item) {
					if (item.status == '20') {
						item.status = "确认";
					} else {
						item.status = "新增";
					}
					/* if (item.status == '20') {
						item.status = '<span style="color: red;">确认</span></div>';
					} else {
						item.status = '<span style="color: green;">新增</span></div>';
					} */
					AllocateInfo.put(item.pre_list_id, item);
					AllocateHtml = AllocateHtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<div class="row"><span id="allocate_vehicle_id">' + item.pre_list_id + '</span>' +
						'<span style="color: red;">' + item.status + '</span></div>' +
						//item.status +
						'<div class="row"><span class="icon">客</span>' + item.customer_name +
						'<span class="icon" style="margin-left: 30px;">单</span>' + item.cust_order_id + '</div>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#allocate_list").html(AllocateHtml);
			} else {
				mui.toast("没有查询到相应预发货清单，请联系管理员！");
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}

//确认按钮
mui(document.body).on('click', '#btn_save', function() {
	if (AllocateList == "" || AllocateList == null) {
		mui.confirm('请勾选要确认的预发货清单！', '提示', ['确认'], function(e) {}, 'div');
	} else if (AllocateList.status == '确认') {
		mui.confirm('只有新增状态的预发货清单才可确认！', '提示', ['确认'], function(e) {
		}, 'div');
	} else {
		// 是否执行确认方法
		mui.confirm('是否确认该记录？', '提示', ['确认', '取消'], function(e) {
			if (e.index == 0) {
				mui("#btn_save").button('loading');
				exeConfimAllocateVehicleId();
			}
		}, 'div');
	}
});

//新增子项
mui(document.body).on('click', '#btn_query', function() {
	if (AllocateList.status == '确认') {
		mui.confirm('只有新增状态的预发货清单才可修改！', '提示', ['确认'], function(e) {
		}, 'div');
	} else {
		mui.openWindow({
			url: 'pda_add_youni_sub.html',
			id: "pda_add_youni_sub",
			createNew: true,
			extras: {
				AllocateList: AllocateList
			}
		});
	}
});

//撤销子项
mui(document.body).on('click', '#btn_cancel', function() {
	if (AllocateList.status == '确认') {
		mui.confirm('只有新增状态的预发货清单才可修改！', '提示', ['确认'], function(e) {
		}, 'div');
	} else {
		var rdsObj = document.getElementsByClassName('checkboxes');
		for (i = 0; i < rdsObj.length; i++) {
			if (rdsObj[i].checked) {
				AllocateListSub.push(AllocateInfoSub.get(rdsObj[i].value));
			}
		}
		if (AllocateListSub == "") {
			mui.confirm('请勾选要撤销的子项信息！', '提示', ['确认'], function(e) {
			}, 'div');
		} else {
			mui.confirm('是否撤销选中记录？', '提示', ['确认', '取消'], function(e) {
				if (e.index == 0) {
					var outUri = domainName + "webService.jsp?callback=?";
					var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
					var method = "exeCancelYouniWorkDDataPack";
					var params = JSON.stringify({
						seg_no: seg_no,
						user_id: user_id,
						AllocateListSub: AllocateListSub
					});
					//console.log(params)
					params = encodeURIComponent(params);
					$.ajax({
						type: "post",
						url: outUri,
						data: {
							innerUri: innerUri,
							params: params,
							method: method
						},
						dataType: "json",
						success: function(data) {
							console.log(JSON.stringify(data))
							if (data.status == "1") {
								mui.confirm(data.desc, '提示', ['确认'], function(e) {
									if (e.index == 0) {
										mui.openWindow({
											url: 'pda_youni_work.html',
											id: "pda_youni_work",
											createNew: true
										});
									}
								}, 'div');
							} else {
								mui.alert(data.desc);
							}
						},
						error: function() {
							mui.toast("网络超时，请稍后再试！");
						}
					});
				}
			}, 'div');
		}
	}
});


//选中预发货清单信息获取
var list = document.querySelector('.mui-table-view.mui-table-view-radio');
list.addEventListener('selected', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	allocate_vehicle_id = el_J.find("#allocate_vehicle_id").text();
	AllocateList = AllocateInfo.get(allocate_vehicle_id);
});

/**
 * 查询预发货清单子项信息
 */
function exeQueryAllocateVehicleIdSub() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeQueryYouniWorkDData";
	var params = '{"seg_no":"' + seg_no + '","pre_list_id":"' + allocate_vehicle_id + '"}';
	var AllocateHtmlSub = "";
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(data) {
			if (data != null) {
				console.log(JSON.stringify(data.resultDesc));
				$.each(data.resultDesc, function(i, item) {
					AllocateInfoSub.put(item.pre_list_subid, item);
					var subid = item.pre_list_subid.substring(20, 25);
					AllocateHtmlSub = AllocateHtmlSub +
						'<div class="mui-input-row mui-checkbox"><li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div><input name="checkbox1" class="checkboxes" value="' + item.pre_list_subid + '" type="checkbox">' +
						'<div class="row"><span class="icon">子</span><span id="allocate_vehicle_subid">' + subid +
						'<div class="row"><span class="icon">捆</span>' + item.pack_id +
						'<span class="icon" style="margin-left: 20px;">牌</span>' + item.shopgin + '</div>' +
						'<div class="row"><span class="icon">重</span>' + item.putin_weight +
						'<span class="icon" style="margin-left: 20px;">库位</span>' + item.location +
						'<span class="icon" style="margin-left: 20px;">规</span>' + item.spec + '</div>' +
						'<div class="row"><span class="icon">库码</span>' + item.wp_id +
						'<span class="icon" style="margin-left: 10px;">名</span>' + item.wp_name + '</div>' +
						'<div class="row"><span class="icon">零</span>' + item.cust_part_id +
						'<span class="icon" style="margin-left: 20px;">品</span>' + item.type_code + '</div>' +
						'</div>' +
						'</a>' +
						'</li></div>';
				});
				$("#shtml").html(AllocateHtmlSub);
			} else {
				mui.toast("没有查询到相应预发货清单，请联系管理员！");
			}
		},
		error: function() {
			mui.toast("网络超时，请稍后再试！");
		}
	});
}


/**
 * 确认预发货清单
 */
function exeConfimAllocateVehicleId() {
	var pre_list_id = AllocateList.pre_list_id;
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var method = "exeConfimYouniWorkData";
	var params = JSON.stringify({
		seg_no: seg_no,
		user_id : user_id,
		pre_list_id : pre_list_id
	});
	params = encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 10000,
		success: function(data) {
			console.log(JSON.stringify(data))
			if (data.out_result == "0") {
				mui("#btn_save").button('reset');
				mui.confirm(data.out_result_desc, '提示', ['确认'], function(e) {
					if (e.index == 0) {
						window.location.reload();
					}
				}, 'div');
			} else {
				mui("#btn_save").button('reset');
				mui.confirm(data.out_result_desc, '提示', ['确认'], function(e) {
					if (e.index == 0) {
						window.location.reload();
					}
				}, 'div');
			}
		},
		error: function() {
			mui("#btn_save").button('reset');
			mui.toast("网络超时，请稍后再试！");
		}
	});
}
