/** 明细样式 */

.detail_row {
	height: 49px;
}

.detail_textarea {
	height: 100px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.quality_desc {
	float: left;
	width: 70%;
	font-size: 22px;
	padding: 0px 0px;
	text-align: left;
}

.quality_unlock_desc {
	float: left;
	width: 70%;
	font-size: 22px;
	padding: 5px 0px;
	text-align: left;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 30%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#pack_id,
#pack_block_id {
	width: 70%;
	padding: 0px 5px;
	font-size: 20px;
}

#quality_desc,
#pack_block_id {
	background: #CCCCCC;
}

.pack_list {
	height: 0;
	overflow: auto;
	border: 0px solid #AAAAAA;
}

.storage_pack {
	width: 50%;
	margin-top: 12px;
}

.li-text {
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 78px;
}

.li-text p span font {
	text-align: center;
	font-size: 22px;
	color: #000000;
}

.li-height {
	margin-top: 4px;
}


/** 列表样式 */

.pack_location_target {
	color: red;
	font-size: 22px;
}

.pack_location_now {
	color: blue;
	font-size: 22px;
	margin-top: 8px;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}