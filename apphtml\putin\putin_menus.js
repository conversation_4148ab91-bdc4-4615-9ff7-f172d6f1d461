var segNo=localStorage.getItem("segNo");
mui.init({
	//不启用右滑关闭功能
	swipeBack:false
});

mui(document.body).on('tap','#back',function(){
	mui.back();
});
mui.back = function(){
	mui.openWindow({
		id:"index",
    	url:"../public/index.html",
	    createNew: false
	});
}

//进入转库捆包入库扫描页面
mui(document.body).on('click','#transfer_putin',function(){
	mui.openWindow({
		url:'putin_scan_transfer.html',
		id:"putin_scan_transfer",
		createNew:true
	});
});
		
//进入普通捆包入库扫描页面
mui(document.body).on('click','#normal_putin',function(){
	mui.openWindow({
		url:'putin_scan.html',
		id:'putin_scan',
		createNew:true
	});
});