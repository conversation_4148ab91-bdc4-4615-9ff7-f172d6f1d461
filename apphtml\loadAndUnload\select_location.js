/**
 * 变量定义
 */

var seg_no = localStorage.getItem("segNo");
var wprovider_id = localStorage.getItem("wprovider_id"); //仓库代码
var webServiceUrl = localStorage.getItem("webServiceUrl");

mui.init({
	swipeBack: false //启用右滑关闭功能
});

$(function(){
	QueryKW();
});

//输入库位改变时间
function QueryKW() {
	var location_name = $("#location").val();
	//库位下拉框查询
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var params = JSON.stringify({
		seg_no: seg_no,
		wprovider_id: wprovider_id,
		location_name: location_name
	});
	var method = "exeQueryLocationId";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		var chtml = "";
		$.each(data.returnList, function(i, item) {
			chtml = chtml + '<li class="mui-table-view-cell">' +
				'<a class="mui-navigate-right">' +
				'<div>' +
				'<label id="location_name" style="color: #0000FF;">' + item.location_name +'-'+item.location_id+'</label>' +
				'</div>' +'</a>' +'</li>';
		});
		$("#companyList").html(chtml);
	});
}


//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	location_name = el_J.find("#location_name").text();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	console.log(location_name)
	mui.openWindow({
		url: 'EDS_putin_putout.html',
		id: 'EDS_putin_putout',
		createNew: true,
		extras: {
			location_name: location_name,
		},
	});
});
