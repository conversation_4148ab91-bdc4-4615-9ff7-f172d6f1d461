
var flag = 0;//定义开关值

mui.ready(function(){
	//查询开关值
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	flag = getSwitchValue(segNo,"IF_STEEL_SUPPORT_INVENTORY");
	
	//判断当前账套是否存在盘库开关
	if(flag == 1){
		//显示盘库开关
		console.log("111");
		$("#inventory").removeClass('mui-hidden');
	}
});

mui(document.body).on('click', '#putout', function() {
	mui.openWindow({
		url: 'bracket_putout.html',
		createNew: true
	});
});

mui(document.body).on('click', '#putin', function() {
	mui.openWindow({
		url: 'bracket_putin.html',
		createNew: true
	});
});
mui(document.body).on('click', '#bind', function() {
	mui.openWindow({
		url: 'bracket_bind.html',
		createNew: true
	});
});
mui(document.body).on('click', '#unbind', function() {
	mui.openWindow({
		url: 'bracket_unbind.html',
		createNew: true
	});
});
mui(document.body).on('click', '#inventory', function() {
	mui.openWindow({
		url: 'bracket_Inventory.html',
		createNew: true
	});
});