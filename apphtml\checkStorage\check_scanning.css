/** 按钮样式 */

#edit {
	width: 49%;
	font-size: 22px;
	line-height: 1.6;
	margin: 2px 4px 0px 0px;
}

#upload {
	width: 49%;
	font-size: 22px;
	line-height: 1.6;
	margin: 2px 0px 0px 2px;
}


/** 明细样式 */

.detail_row {
	height: 43px;
}

.mui-input-row span {
	top: 10px!important;
	padding: 5px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#packLocation,
#packId,
#spec,
#weight,
#qty {
	width: 78%;
	padding: 0px 5px;
	font-size: 20px;
}

#qualityInfo {
	width: 78%;
	padding: 0px 5px;
	font-size: 20px;
}

.quality_dissent {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 8px 0px;
	text-align: center;
}


/** 合计框样式 */

.sum {
	border: solid 1px #CBCBCB;
	border-radius: 5px;
	font-size: 20px;
}


/** 合计显示样式 */

.sum_title {
	margin: 8px;
}

.active {
	background-color: red;
	color: white;
}

.active2 {
	background-color: #2AC845;
	color: black;
}

.dissent_input {
	border: 1px solid #2AC845;
}

.dissent_input2 {
	border: 1px solid red;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
}