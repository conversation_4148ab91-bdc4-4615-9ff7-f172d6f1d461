var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var ylList = new Array(); //原料捆包信息
var gdList = new Array(); //工单信息
var tlList = new Array(); //投料信息
var ccList = new Array(); //产出信息
var webServiceUrl = localStorage.getItem("webServiceUrl");
var productionOrderCode = ""; //工单号
$(function() {
	//load();
	//a();
	//获取传递的参数
	mui.plusReady(function() {
		var self = plus.webview.currentWebview();
		productionOrderCode = self.productionOrderCode;
		$("#gdnumber").html(productionOrderCode)
		review(productionOrderCode);
	});
})

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

function tz(val) {
	console.log(val)
	var spec = val.split(",")[0];
	var partId = val.split(",")[1];
	mui.openWindow({
		url: 'record_entry2.html',
		createNew: true,
		extras: {
			ylList : ylList,
			ccList : ccList,
			gdList : gdList,
			tlList : tlList,
			spec : spec,
			partId : partId
		}
	});
}

/*function load() {
	ajax 发送参数 unit 判断 返回参数data 如果是一个 正常进入实绩录入页面,
	如果多个或者没有要跳转工单启动页面
	//var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	//var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var seg_no = "00118";
	var user_id = "Z1";
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		machineId: user_id
	};
	params = JSON.stringify(params);
	var method = "queryOrderByMachineId";
	console.log("params" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log(JSON.stringify(result));
			if(result.orderTotal != 1) {
				$.each(result.ordertList, function(i, e) {
					production_order_code = e.productionOrderCode;
					//review(e.productionOrderCode)
					if(i == 1){
						$("#gdnumber").html(e.productionOrderCode)
						review(e.productionOrderCode)
					}
				});
			} else {
				var machineId = "";
				$.each(result.ordertList, function(i, e) {
					machineId = e.machineId;
				});
				if(machineId != "") {
					mui.openWindow({
						url: 'workorderoperation.html',
						createNew: true,
						extras: {
							machineId: machineId
						}
					});
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
		}
	})
}*/


function review(val){
	//var segNo = "00118";
	var umcLoginName = "admin";
	//var productionOrderCode = "34Z200311001";
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: segNo,
		umcLoginName: umcLoginName,
		productionOrderCode : val
	};
	params = JSON.stringify(params);
	var method = "queryDetail";
	console.log("params" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			var phtml = "";
			console.log(JSON.stringify(result.rmPartPackList));
			ylList = result.rmPartPackList;
			tlList = result.rmPartList;
			gdList = result.orderProcessList;
			ccList = result.fmPartList;
			/*$.each(result.rmPartPackList, function(i, e) {
				console.log(e.packId)
			});*/
			$.each(result.fmPartList, function(i, e) {
					//console.log(e.spec)
					if( i = 0){
						phtml = phtml + '<li style="margin-top: 60px;">' +
						'<a onclick="tz(\'' + e.spec +","+e.partId + '\')"><span style="color: #000000; font-size: 25px;">'+e.spec+'</span>'+
						'<button style="float: right;width: 50px;">》</button></a></li>';
					}else{
						//console.log(e.spec+","+e.createPerson)
						phtml = phtml + '<li style="margin-top: 10px;">' +
						'<a onclick="tz(\'' + e.spec +","+e.partId + '\')"><span style="color: #000000; font-size: 25px;">'+e.spec+'</span>'+
						'<button style="float: right;width: 50px;">》</button></a></li>';
					}
			});
		$("#phtml").html(phtml);
			
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
		}
	})
}
