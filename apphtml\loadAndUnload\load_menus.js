$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
	/*$(".mui-content a").click(function() {
		console.log(this.index);
	});*/
});

function tojude(type) {
	//入厂确认
	if(type == "1") {
		mui.openWindow({
			url: 'enter_factory_confirm.html',
			createNew: true
		});
		//开始装车
	} else if(type == "2") {
		mui.openWindow({
			url: 'start_load.html',
			createNew: true
		});
		//出厂扫描
	/*else if(type == "3") {
		mui.openWindow({
			url: 'sale_out.html',
			createNew: true
		});
	}*/
		//出厂扫描
	}else if(type == "3") {
		mui.openWindow({
			url: 'sale_out_bill.html',
			createNew: true
		});
	}
}