mui.plusReady(function() {
	queryVehicleTraceInfo();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	if(car_trace_no == "") {
		mui.alert("请选择车牌号", "提示", "确定", function() {}, "div");
		return;
	};
	mui("#confirm").button("loading");
	$("#overlay").addClass("overlay");
	exeVehicleBeginEntruck(car_trace_no);
});

//绑定列表选中事件
var  car_trace_no = "";
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var  el = e.detail.el;
	//console.log("el============="+el);
	var  el_J = $(el);
	car_trace_no = el_J.find("#car_trace_no").text();
	//console.log("car_trace_no============="+car_trace_no);
});

//车辆跟踪信息查询方法
function queryVehicleTraceInfo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var  segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var  webServiceUrl = localStorage.getItem("webServiceUrl");
	//				var  segNo = "00112";
	//				var  webServiceUrl = "10.30.184.231:7001";
	var  outUri = domainName + "webService.jsp?callback=?";
	var  innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //PDABoardVehicleService
	var  params = '{"seg_no":"' + segNo + '"}';
	var  method = "queryVehicleTraceInfo";

	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			//先清空当前列表中的牌号信息
			var  lihtml = "";
			$("#companyList").html(lihtml);
			//填充最新的牌号信息
			if(data.returnList.length > 0) {
				$.each(data.returnList, function(i, item) {
					lihtml = lihtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<label id="vehicle_no">' + item.vehicle_no + '</label>' +
						'</div>' +
						'<div style="display:none">' +
						'<label id="car_trace_no">' + item.car_trace_no + '</label>' +
						'</div>' +
						'<div>' +
						'<label id="enter_factory">' + item.enter_factory + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#companyList").html(lihtml);
			} else {
				mui.alert("未查询到对应的车牌号", "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//开始装车
function exeVehicleBeginEntruck(car_trace_no) {
	var  segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var  account = localStorage.getItem("account"); //采用localStorage存储数据
	var  webServiceUrl = localStorage.getItem("webServiceUrl");
	//				var  segNo = "00112";
	//				var  account = "dev";
	//				var  webServiceUrl = "10.30.184.231:7001";
	var  outUri = domainName + "webService.jsp?callback=?";
	var  innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var  params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","car_trace_no":"' + car_trace_no + '"}';
	var  method = "exeVehicleBeginEntruck";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			mui("#confirm").button("reset");
			$("#overlay").removeClass("overlay");
			if(data.returnStatus == "1") {
				mui.alert("操作成功", "提示", "确定", function() {
					queryVehicleTraceInfo();
				}, 'div');
				return;
			} else {
				mui.alert("操作失败!原因：" + data.returnDesc, "提示", "确定", function() {}, 'div');
				return;
			}
		} else { //连接失败
			mui("#confirm").button("reset");
			$("#overlay").removeClass("overlay");
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}