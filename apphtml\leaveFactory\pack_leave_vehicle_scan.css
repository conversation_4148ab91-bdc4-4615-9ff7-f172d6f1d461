#cancel {
	width: 49%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 4px 0px 0px;
}

#confirm {
	width: 49%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 0px 0px 2px;
}

.detail_row {
	height: 46px;
}

.mui-input-row span {
	top: 10px!important;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.pack_text{
	float: left;
	width: 40%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
display: none;
top: 0px;
left: 0px;*/
position: absolute;
width: 100%;
height: 100%;
z-index: 99999;
/* 此处的图层要大于页面 */
/*display:none;*/
}

#driver_id,
#driver_name,
#pack_id,
#id_card,
#vehicle_id,
#provider_name{
	width: 78%;
	padding: 0px 5px;
	font-size: 20px;
}

#fact_pack_count,
#own_pack_count{
	width: 60%;
	padding: 0px 5px;
	font-size: 20px;
	float:right;
}
.mui-input-row select {
    font-size: 17px;
    height: 37px;
    width: 60%;
    padding: 0;
    padding-left: 19px;
}