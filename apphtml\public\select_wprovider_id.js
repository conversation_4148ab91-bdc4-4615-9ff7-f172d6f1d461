/**
 * 变量定义
 */
var wprovider_id = "";
var wprovider_name = "";
var location_type = "";
var auto_loc_flag = "";
var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var startfrom = "";
var putin_method_switch = "";

var transfer_flag = "";
mui.init({
	swipeBack: true //启用右滑关闭功能
});

$(function() {
	//调用webservices接口
	toWebServices();

});

window.onload = function onload() {
	mui.plusReady(function() {
		startfrom = plus.webview.currentWebview().startfrom;
		console.log("startfrom>>>>>>>>>>>>>." + startfrom);
		putin_method_switch = getSwitchValue(seg_no, 'PDA_PUTIN_METHOD_CHOICE');
		transfer_flag = plus.webview.currentWebview().transfer_flag;
		console.log("开关值：" + putin_method_switch);
	});
}

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	wprovider_id = el_J.find("#wprovider_id").text();
	wprovider_name = el_J.find("#wprovider_name").text();
	location_type = el_J.find("#location_type").text();
	auto_loc_flag = el_J.find("#auto_loc_flag").text();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	//判断是否选择仓库
	if(null == wprovider_id || "" == wprovider_id) {
		mui.alert("请选择仓库", " ", "确定", function() {}, 'div');
		return false;
	}
	localStorage.setItem("wprovider_id", wprovider_id);
	localStorage.setItem("wprovider_name", wprovider_name);
	localStorage.setItem("location_type", location_type);
	localStorage.setItem("auto_loc_flag", auto_loc_flag);
	localStorage.getItem("name");
	localStorage.getItem("segNo");
	var self = plus.webview.currentWebview();

	var popwrovider = self.openType;
	console.log(popwrovider);
	if("popwrovider" == popwrovider) {
		mui.openWindow({
			url: 'index.html',
			id: 'index',
			createNew: true
		});
	} else if("popHandPoint" == popwrovider) {
		mui.openWindow({
			url: '../loadAndUnload/select_hand_point.html',
			id: 'select_hand_point',
			createNew: true
		});
	} else {
		//智慧仓库，入库前必须 选择车牌号
		console.log("startfrom>>>>>>>>>>>>" + startfrom);
		if(startfrom == 'menu') {
			console.log("putin_scan>>>>>>>>>>>>>>>>>>>>" + startfrom);
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，将进入入库选择页面
			if(putin_method_switch == "1" && seg_no == '00113') {
				mui.openWindow({
					url: '../putin/putin_method_menus.html',
					id: 'putin_method_menus',
					createNew: true
				});
			} else if(auto_loc_flag == '1' && location_type > '0') {
				mui.openWindow({
					//url: '../loadAndUnload/select_vehicle_no.html',
					url: 'select_vehicle_no.html',
					id: 'select_vehicle_no',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					//extras:{
					//wprovider_id: wprovider_id,  //扩展参数
					//wprovider_name: wprovider_name,  //扩展参数
					//},
					createNew: true
				});
			}
		} else if(startfrom == 'synergy') {
			console.log("synergy>>>>>>>>>>>>>>>>>>>>" + startfrom);
			mui.openWindow({
				url: 'select_vehicle_no.html',
				id: 'select_vehicle_no',
				extras: {
					open: 'synergy', //扩展参数
				},
				createNew: true
			});
		} else if(startfrom == 'sgm_gf_putin'){
			console.log("putin_scan>>>>>>>>>>>>>>>>>>>>" + startfrom);
			//add by lx 佛山宝钢智慧仓库SGM改造
			if(auto_loc_flag == '1' && location_type > '0') {
					mui.openWindow({
						url: 'select_vehicle_no.html',
						id: 'select_vehicle_no',
						extras: {
							open: 'sgm_gf_putin'
						},
						createNew: true
					});
			} else {
				mui.openWindow({
					url: '../sgmGFPutin/sgm_cch.html',
					id: 'sgm_cch',
					createNew: true
				});
			}
		}else {
			//add by Luo Yinghui 青岛宝井PDA改造专项
			//如果当前青岛宝井入库方式选择开关打开，进入转库扫描页面
			if(putin_method_switch == "1" && seg_no == '00113' && transfer_flag == 1) {
				mui.openWindow({
					url: '../putin/putin_scan_transfer.html',
					id: 'putin_scan_transfer',
					createNew: true
				});
			} else {
				mui.openWindow({
					url: '../putin/putin_scan.html',
					id: 'putin_scan',
					createNew: true
				});
			}
		}
	}
});

function toWebServices() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + seg_no + '"}';
	var method = "exeQueryWproviderInfo";
	console.log(outUri);	
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
		if(null != data) { //连接成功
			console.log("选择仓库返回：" + JSON.stringify(data));
			if(data.resultList.length > 0) {
				var chtml = "";
				$.each(data.resultList, function(i, item) {
					chtml = chtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<label id="wprovider_id">' + item.wprovider_id + '</label>' +
						'</div>' +
						'<div>' +
						'<label id="wprovider_name">' + item.provider_name + '</label>' +
						'</div>' +
						'<div style= "display:none">' +
						'<label id="location_type">' + item.location_type + '</label>' +
						'</div>' +
						'<div style= "display:none">' +
						'<label id="auto_loc_flag">' + item.auto_loc_flag + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				//需求：ERP_59498 xsp 20200409 上海高强钢要能选择上海不绣的欧冶上海不锈仓库（是委外库）
				if("00138" == seg_no) {
					chtml = chtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<label id="wprovider_id">261941001</label>' +
						'</div>' +
						'<div>' +
						'<label id="wprovider_name">欧冶上海不锈</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				}
				if("00137" == seg_no) {
					chtml = chtml + '<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<label id="wprovider_id">P95116</label>' +
						'</div>' +
						'<div>' +
						'<label id="wprovider_name">欧冶上海不锈</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				}
				$("#companyList").html(chtml);
			}
		} else { //连接失败
			alert("服务器连接异常");
		}
	});
}