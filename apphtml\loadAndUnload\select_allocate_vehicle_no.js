/**
 * 变量定义
 */
var allocate_vehicle_id = "";
var tprovider_name = "";
var tprovider_id = "";
var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var vehicle_id = localStorage.getItem("vehicle_id");
var queryVoucherList = new Array(); //查询出库的单据信息

var opt_type = ""; //业务类型
var restrict_date = ""; //时间范围
var customer_id = ""; //客户代码
var putoutVoucherList = new Array(); //出库单据信息
var voucher_count_max = "";
mui.init({
	swipeBack: true //启用右滑关闭功能
});

$(function() {
	//调用webservices接口
	queryallocateVehicleId();
});
window.onload = function onload() {
	mui.plusReady(function() {
		opt_type = plus.webview.currentWebview().opt_type;
		voucher_count_max = plus.webview.currentWebview().voucher_count_max;
		putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
		console.log(" opt_type:" + opt_type +
			+" voucher_count_max:" + voucher_count_max +
			" putoutVoucherList:" + JSON.stringify(putoutVoucherList)
			//+ " packInfo:" + JSON.stringify(packInfo)
		);
		//					if(opt_type=='CC'){
		//						$('#btn_cc').addClass('active').siblings().removeClass('active');
		//					}else if(opt_type=='ZK'){
		//						$('#btn_zk').addClass('active').siblings().removeClass('active');
		//					}else if(opt_type=='XS'){
		//						$('#btn_xs').addClass('active').siblings().removeClass('active');
		//					}else{
		//						;
		//					}	
	});
}
//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	allocate_vehicle_id = el_J.find("#allocate_vehicle_id").text();
	tprovider_id = el_J.find("#tprovider_id").text();
	tprovider_name = el_J.find("#tprovider_name").text();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	//判断是否选择仓库
	if(null == allocate_vehicle_id || "" == allocate_vehicle_id) {
		mui.alert("请选择配车单号", " ", "确定", function() {}, 'div');
		return false;
	}
	var aa = localStorage.getItem("allocate_vehicle_id")
	console.info(putoutVoucherList.length);
	console.info("aaaabb:" + allocate_vehicle_id);
	console.info("aaaabb:" + aa);
	if(putoutVoucherList.length != 0) {
		//					console.info("aaaaaaaaaaaaaaaaaaaabbbbbbbbbbbbaaa" + JSON.stringify(packInfo).length);
		//					if(JSON.stringify(packInfo).length!=2){
		if(allocate_vehicle_id == localStorage.getItem("allocate_vehicle_id")) {
			console.info("aaaaaaaaaaaaaaaaaaaaaaa");
			mui.alert(localStorage.getItem("allocate_vehicle_id") + "该配车单已经在已扫单据列表中,请出库之后再对" + allocate_vehicle_id + "配车单操作", "提示", "确认", function() {}, "div");
			return false;
		} else {
			//console.log( JSON.stringify(putoutVoucherList));
			mui.confirm("确认替换为" + localStorage.getItem("allocate_vehicle_id") + "配车单", '警告', ['确认', '取消'], function(e) {
				if(e.index == 0) {
					putoutVoucherList = [];
					queryAllocateVehicleD();
				} else {
					console.log(1111);
				}
			}, 'div');
			//console.log( JSON.stringify(putoutVoucherList));
		}
		//					}else{
		//					putoutVoucherList.cleanData();
		//						console.info("aaaaaaaaaaaaaaaaaaaabbbbbbbbbbbbaaa"+putoutVoucherList.length);
		//						queryAllocateVehicleD();
		//					}
		//					else{
		//						var index = getIndexByAllocateVehicleId(allocate_vehicle_id,putoutVoucherList);
		//						if(index != -1){
		//							mui.alert(allocate_vehicle_id + "该配车单已经在已扫单据列表中","提示","确认",function() {}, "div");
		//							return false;
		//						}
		//					}
	} else {
		//查询提单
		queryAllocateVehicleD();
	}

});
//查询putoutVoucherList记录 返回index
function getIndexByAllocateVehicleId(voucher_id, voucherList) {
	var index = -1;
	$.each(voucherList, function(i, item) {
		if(item.allocate_vehicle_id == voucher_id) {
			index = i;
			return false;
		}
	});
	return index;
}
//查询配车单
function queryallocateVehicleId() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + seg_no + '","vehicle_id":"' + vehicle_id + '"}';
	var method = "exeQueryAllocateVehicleM";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.codeList.length == 0) {
				mui.alert("未查询到对应的配车单信息", "提示", "确定", function() {}, 'div');
				return;
			} else {
				var chtml = "";
				$.each(data.codeList, function(i, item) {
					chtml = chtml + '<li class="mui-table-view-cell"  >' +
						'<a class="mui-navigate-right">' +
						'<div>' +
						'<label id="allocate_vehicle_id">' + item.allocate_vehicle_id + '</label>' +
						'</div>' +
						'<div>' +
						'<label id="tprovider_name">' + item.tprovider_name + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#companyList").html(chtml);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
//查询提单
function queryAllocateVehicleD() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + seg_no + '","allocate_vehicle_id":"' + allocate_vehicle_id + '"}';
	var method = "exeQueryAllocateVehicleD";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.resultStatus == "1") {
				if(data.resultDesc.length > 0) {
					queryVoucherList = data.resultDesc;
					localStorage.setItem("allocate_vehicle_id", allocate_vehicle_id);
					console.info("跳转之前：" + queryVoucherList.length);
					var page = plus.webview.getWebviewById('hand_point_list');
					mui.fire(page, 'addFromSelect', {
						//optType : opt_type,
						voucherList: queryVoucherList
					});
					//跳转回出库单据页面
					mui.openWindow({
						id: 'hand_point_list'
					});

					//进入单据选择页面
					/* mui.openWindow({
									url:'putout_voucher_select.html',
									id:"putout_voucher_select",
									createNew:true,
									extras:{
										open_from_url:"select_allocate_vehicle_no.html",
					        			open_from_id:"select_allocate_vehicle_no",
					        			opt_type:"1",
					        			queryVoucherList:queryVoucherList,
					        			putoutVoucherList:putoutVoucherList,
										voucher_count_max:voucher_count_max
									}
								});*/
				} else {
					mui.alert('', "没有查询到单据信息");
					return false;
				}
			} else {
				mui.alert(data.resultDesc, "提示", "确认", function() {}, "div");
				return false;
			}

			//						console.info(data.resultStatus.length);
			//						if(data.resultStatus.length == 0){
			//							 mui.alert("没有查询到单据信息", "提示", "确定", function() {
			//							}, 'div');
			//							return ;
			//						}else{
			//						   queryVoucherList = data.resultDesc;
			//						   var page = plus.webview.getWebviewById('hand_point_list');
			//							mui.fire(page,'addFromSelect',{
			//								voucherList : queryVoucherList
			//							});
			//							//跳转回出库单据页面
			//							mui.openWindow({
			//								id:'hand_point_list'	
			//							});
			//						}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}