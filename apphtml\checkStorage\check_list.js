//变量定义
var scanPackList = new Array();
var prePackLocation = "";

$(function(){
	mui.init({
		swipeBack:true, //启用右滑关闭功能
	});
});

//获取从上一个页面传过来的库位。返回时再带回去
window.onload = function onload(){
	//alert("onload11111111111111");
	mui.plusReady(function(){
		prePackLocation = plus.webview.currentWebview().prePackLocation;
		scanPackList = plus.webview.currentWebview().preScanPackList;
		//alert(JSON.stringify(scanPackList));
		initData();
	});
}

/*$(document).ready(function(){
	//swipeBack(true);
	//initParams();
	initData();
});*/

function initParams(){
	scanPackList = localStorage.getItem("scanPackList");
	var type = typeof(scanPackList);
	if(type =="string" && scanPackList!=""){
		scanPackList = JSON.parse(scanPackList);
	} 
}
/**
 * 这里加载已扫描的数据，
 */
function initData(){
	if(scanPackList != null && scanPackList.length > 0){
		$("#checkList li").remove(); 
		var checkList = $("#checkList");
		for(var i= scanPackList.length-1; i >= 0; i--){ 
			var spec = "";
			if(scanPackList[i].spec.length>20){
				spec = scanPackList[i].spec.substr(0,20)+"...";
			}else{
				spec = scanPackList[i].spec;
			}
			var li = $('<li class="mui-table-view-cell">'
						+'<div class="mui-slider-right mui-disabled">'
							+'<a class="mui-btn mui-btn-blue mui-icon" onclick='+'modifyLi("'+scanPackList[i].product_id+'","'+scanPackList[i].pack_id+'")'+'>修改</a>'
							+'<a class="mui-btn mui-btn-red mui-icon" onclick='+'deleteLi(this,"'+scanPackList[i].product_id+'","'+scanPackList[i].pack_id+'")'+'>删除</a>'
						+'</div>'
						+'<div class="mui-slider-handle">'
							+'<div>'
								+'<div id="pack_id">'+scanPackList[i].pack_id+'</div>'
								+'<div><div id="spec"><span>规格</span><label>'+spec+'</label></div></div>'
								+'<div><div id="labelId"><span>标签</span><label>'+scanPackList[i].label_id+'</label></div></div>'
							+'</div>'
							+'<div>'
								+'<div style="clear: both;"></div>'
								+'<div class="left" id="old"><span>原库</span><label>'+scanPackList[i].pack_location+'</label></div>'
								+'<div id="new"><span>新库</span><label>'+scanPackList[i].new_pack_location+'</label></div>'
							+'</div>'
							+'<div>'
								+'<div><div id="quality"><span>异议</span><label>'+scanPackList[i].quality_info+'</label></div></div>'
							+'</div>'
							+'<div>'
								+'<div style="clear: both;"></div>'
								+'<div class="left" id="weight"><span>重量</span><label>'+scanPackList[i].check_weight+'</label></div>'
								+'<div id="quantity"><span>数量</span><label>'+scanPackList[i].check_qty+'</label></div>'
							+'</div>'
						+'</div>'
					+'</li>');
			checkList.append(li);
		}
	}
}


//删除
function deleteLi(ele,productId,packId){
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '盘点列表', btnArray, function(e) {
		if (e.index == 0) {
			li.parentNode.removeChild(li);//删除DOM节点，
			//但是同时也要删除数据容器
			if(scanPackList!=null && scanPackList.length>0){
				for(var i=0;i<scanPackList.length;i++){
					if(scanPackList[i].product_id==null || scanPackList[i].product_id ==""){
						if(scanPackList[i].pack_id== packId){
							scanPackList.splice(i,1); 
							//alert(scanPackList.length);
							if(scanPackList==null || scanPackList.length==0){//内存数组为[]表示无数据
							   localStorage.setItem("recentPackId","");
							   deleteScanedPackTxt();//删除本地已扫文件
							   localStorage.setItem("scanPackList","");
							}else{
								localStorage.setItem("recentPackId",scanPackList[scanPackList.length-1].pack_id);
								createScanedPackTxt(scanPackList);
								localStorage.setItem("scanPackList",JSON.stringify(scanPackList));
							}
							localStorage.setItem("uploadingCount",parseInt(localStorage.getItem("uploadingCount"))-1);
							
							writeUploadLogTxt(packId);
							break;
						}
					}else if(scanPackList[i].product_id == productId && scanPackList[i].pack_id== packId){
						scanPackList.splice(i,1); 
						if(scanPackList==null || scanPackList.length==0){//内存数组为[]表示无数据
							localStorage.setItem("recentPackId","");
							deleteScanedPackTxt();//删除本地已扫文件
							localStorage.setItem("scanPackList","");
						}else{
							//alert(scanPackList.length-1);
							localStorage.setItem("recentPackId",scanPackList[scanPackList.length-1].pack_id);
							createScanedPackTxt(scanPackList);
							localStorage.setItem("scanPackList",JSON.stringify(scanPackList));
						}
						localStorage.setItem("uploadingCount",parseInt(localStorage.getItem("uploadingCount"))-1);
						writeUploadLogTxt(packId);
						break;
					}
				}
				
			}
		}
	});
}

function modifyLi(productId,packId){
	/*mui.openWindow({ //打开登录页面
	url: 'check_scanning.html',
	extras: {
		productId: productId,
		packId: packId,
		qty: localStorage.getItem("qty"),
		weight: localStorage.getItem("weight")
	},
		createNew: true
	});*/

	var page = plus.webview.getWebviewById('check_scanning');
	mui.fire(page, 'edit', {
		productId: productId,
		packId: packId
	});
	mui.openWindow({
		id: 'check_scanning'
	});
}

//删除捆包后更新本地文件
function createScanedPackTxt(data) {
	if(data != "[object Event]") {
		// 打开doc根目录
		plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
			fs.root.getDirectory("PDA", {
				create: true
			}, function(entry) {
				dir = entry;
				dir.getFile("upload.txt", {
					create: true
				}, function(fentry) {
					fentry.createWriter(function(writer) {
						writer.onerror = function() {
							mui.alert("捆包保存失败","系统提示","确定",function() {},'div');
						}
						//writer.seek(writer.length); //从文件默认追加新的内容
						var josnscanPackList = JSON.stringify(data);
						if(josnscanPackList==null || josnscanPackList==""){
							writer.write("");
						}else{
							writer.write(josnscanPackList.substring(1,josnscanPackList.length-1)+",");
						}
					}, function(e) {
						mui.alert("加载已盘捆包文件失败","系统提示","确定",function() {},'div');
					});
				}, function(e) {
					mui.alert("已盘捆包本地文件不存在","系统提示","确定",function() {},'div');
				});
			}, function(e) {
				mui.alert("已盘捆包本地文件路径不存在","系统提示","确定",function() {},'div');
			});
		}, function(e) {
			mui.alert("系统异常","系统提示","确定",function() {},'div');
		});
	}
}

function deleteScanedPackTxt(){
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
	// fs.root是根目录操作对象DirectoryEntry
	fs.root.getFile('PDA/upload.txt', {
		create: false
	}, function(fentry) {
		fentry.remove( function ( fentry ) {
			console.log( "Remove succeeded" );
		}, function ( e ) {
				mui.alert("删除本地文件失败"+e.message,"系统提示","确定",function() {},'div');
			} );
		});
	});
	
}
//删除捆包写入盘库日志文件
function writeUploadLogTxt(packId){
	plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
	// fs.root是根目录操作对象DirectoryEntry
	fs.root.getFile('PDA/pklog.txt', {
		create: true
	}, function(fileEntry) {
		fileEntry.createWriter(function(writer) {
			writer.onerror = function() {
				mui.alert("保存上传日志文件失败", "系统提示", "确定", function() {}, 'div');
			} 
			writer.seek(writer.length); //从文件默认追加新的内容
			var data = localStorage.getItem("segNo")+"-"+localStorage.getItem("account")+"时间"+getCurrentTime()+"删除捆包："+packId;
			if(writer.length == 0) {
				writer.write(data);
			} else {
				writer.write("\n" + data);
			}
		}, function(e) {
			mui.alert("加载上传日志文件对象失败", "系统提示", "确定", function() {}, 'div');
			});
		});
	});
}

mui(document.body).on('tap','#back',function(){
	mui.back();
});


mui.back = function () {  
	var page = plus.webview.getWebviewById('check_scanning');
	mui.fire(page,'preback',{
		 prePackLocation: prePackLocation,
		 newScanedPackList:scanPackList
	});
	
	mui.openWindow({
		id:'check_scanning'
	}); 
	var ws = plus.webview.currentWebview();
	plus.webview.close(ws);
}

// H5 plus事件处理
function plusReady(){
}
if(window.plus){
	plusReady();
}else{
	document.addEventListener('plusready', plusReady, false);
}