//机组id
var machineId;
var paramWorkOrderCode = null;
//工单列表
var workOrderList;
//所选工单
var pickedWorkOrderId;
var pickedWorkOrderCode;
var pickedWorkOrderStatus;
//打开的界面
var packType;

//返回按钮
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.init({
	//不启用右滑关闭功能
	swipeBack: false,
});

//获取传递的参数
mui.plusReady(function() {
	var self = plus.webview.currentWebview();
	machineId = self.machineId;
	packType = self.packType;

	if (self.workOrderCode != null) {
		paramWorkOrderCode = self.workOrderCode;
		console.log("传入paramWorkOrderCode：" + paramWorkOrderCode);
	}
	console.log("传入机组id：" + machineId);
	var listHeight = $(window).height() - 224;
	$('#work-order-ul').height(listHeight);
	
	$('#gongdan-input').bind('input propertychange', function() {
		if ($(this).val() && workOrderList != null) {
			showWorkOrderList();
		}
	});

	queryWorkerorder();
});

//工单号筛选事件
mui(document.body).on('tap', '#gongdan-search', function(e) {
	console.log("工单号筛选" + workOrderList == null);
	if (workOrderList == null) {
		queryWorkerorder();
	} 
});

//工单选中事件
mui(document.body).on('selected', '#work-order-ul', function(e) {
	//状态 00撤销，05新增，10确认，20已转工单（待执行），30启动，35暂停，40完成
	var el = e.detail.el;
	var el_J = $(el);
	pickedWorkOrderId = el_J.find("#work-order-id").text().trim();
	pickedWorkOrderCode = el_J.find("#work-order-code").text().trim();
	pickedWorkOrderStatus = el_J.find("#work-order-status").text().trim();
	console.log("选中工单：" + pickedWorkOrderCode + "   状态：" + pickedWorkOrderStatus + "    id：" + pickedWorkOrderId);
});

//启动按钮
mui(document.body).on('tap', '#start_btn', function(e) {
	console.log("启动按钮点击");
	if (pickedWorkOrderCode == null || pickedWorkOrderCode == '') {
		mui.alert("请选择要启动的工单");
	} else if (pickedWorkOrderStatus == '20' || pickedWorkOrderStatus == '35') {
		//可以执行启动
		workerOrderStart();
	} else {
		mui.alert("当前工单不能执行启动操作");
	}
});

//暂停按钮
mui(document.body).on('tap', '#stop_btn', function(e) {
	console.log("暂停按钮点击");
	if (pickedWorkOrderCode == null || pickedWorkOrderCode == '') {
		mui.alert("请选择要暂停的工单");
	} else if (pickedWorkOrderStatus == '30') {
		//可以执行暂停
		workerOrderStop();
	} else {
		mui.alert("当前工单不能执行暂停操作");
	}
});

//实绩录入按钮
mui(document.body).on('tap', '#input_btn', function(e) {
	console.log("实际录入按钮点击");
	if (pickedWorkOrderCode == null || pickedWorkOrderCode == '') {
		mui.alert("请选择要录入的工单");
	} else if (pickedWorkOrderStatus == '30') {

		if (packType == null || packType == '') {
			packType = 'record_entry.html';
		}

		var openId = packType.replace('.html', '');

		//可以执行录入
		mui.openWindow({
			id: openId,
			url: packType,
			createNew: true,
			extras: {
				productionOrderCode: pickedWorkOrderCode
			}
		});
	} else {
		mui.alert("当前工单不能执行录入操作");
	}
});



function queryWorkerorder() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	// var seg_no = '00118';
	// var user_id = 'admin';
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = '';
	if (paramWorkOrderCode == null) {
		params = {
			segNo: seg_no,
			umcLoginName: user_id,
			processOrderFlag: 1,
			machineId: machineId
		};
	} else {
		params = {
			segNo: seg_no,
			umcLoginName: user_id,
			machineId: machineId,
			productionOrderCode: paramWorkOrderCode
		};
	}
	params = JSON.stringify(params);
	var method = "queryProcessOrderInfo";
	console.log("outUri：" + outUri);
	console.log("innerUri：" + innerUri);
	console.log("params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log("queryProcessOrderInfo result：" + JSON.stringify(result));
			if (result != null) {
				if (result.returnValue == 1 &&
					getJsonLength(result.pdaProcessOrderInfoList) > 0) {
					//查询到数据
					workOrderList = result.pdaProcessOrderInfoList;
					showWorkOrderList();
				} else {
					mui.alert("暂无工单数据");
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("工单查询失败");
		}
	})
};


//智慧仓库:加载首道加工工序
function showWorkOrderList() {

	var keywords = $('#gongdan-input').val();

	var showDataList;
	if (keywords == null || keywords.trim() == '') {
		showDataList = workOrderList;
	} else {
		showDataList = $.grep(workOrderList, function(e, i) {
			return e.productionOrderCode.search(keywords) > -1;
		});
	}
	
	console.log("过滤好的数据："+JSON.stringify(showDataList));

	var productProcessListHtml = '';
	for (var index in showDataList) {
		productProcessListHtml = productProcessListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<label id="work-order-code-show">工单：' + showDataList[index].productionOrderCode + ' </label>' +
			'<label id="work-order-status-name">状态：' + showDataList[index].statusName + '</label>' +
			'<label id="work-order-code">' + showDataList[index].productionOrderCode + ' </label>' +
			'<label id="work-order-status">' + showDataList[index].status + '</label>' +
			'<label id="work-order-id">' + showDataList[index].productionOrderId + '</label>' +
			'</a>' +
			'</li>';
	};
	$("#work-order-ul").html(productProcessListHtml);
}

/**
 * 执行启动
 */
function workerOrderStart() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var tean_id = localStorage.getItem("team_id");
	var class_id = localStorage.getItem("class_id");

	// var seg_no = '00118';
	// var user_id = 'admin';

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		machineId: machineId,
		productionOrderCode: pickedWorkOrderCode,
		productionOrderId: pickedWorkOrderId,
		workingShift: tean_id,
		teamId: class_id
	};
	params = JSON.stringify(params);
	var method = "startOrder";
	console.log("outUri：" + outUri);
	console.log("innerUri：" + innerUri);
	console.log("params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log("result：" + JSON.stringify(result));
			if (result != null) {
				if (result.returnValue == 1) {
					mui.alert('工单启动成功', '确定', function() {
						queryWorkerorder();
					});
				} else {
					mui.alert(result.errorDetail);
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("启动失败");
		}
	})

};

/**
 * 工单暂停
 */
function workerOrderStop() {
	//TODO 测试参数
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据

	// var seg_no = '00118';
	// var user_id = 'admin';

	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
	var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
	var outUri = domainName + "webService_imes.jsp";
	var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
	var params = {
		segNo: seg_no,
		umcLoginName: user_id,
		userId: '1',
		productionOrderCode: pickedWorkOrderCode,
		productionOrderId: pickedWorkOrderId
	};
	params = JSON.stringify(params);
	var method = "pauseOrder";
	console.log(method + "---params：" + params);
	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method,
			targetnamespace: targetnamespace
		},
		dataType: "json",
		success: function(result) {
			console.log(method + "---result：" + JSON.stringify(result));
			if (result != null) {
				if (result.returnValue == 1) {
					mui.alert(result.returnDesc, '确定', function() {
						queryWorkerorder();
					});
				} else {
					mui.alert(result.errorDetail);
				}
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log("请求失败：" + JSON.stringify(XMLHttpRequest));
			console.log(textStatus);
			console.log(errorThrown);
			mui.alert("启动失败");
		}
	})

};

function getJsonLength(jsonData) {
	var jsonLength = 0;
	for (var item in jsonData) {
		jsonLength++;
	}
	return jsonLength;
}
