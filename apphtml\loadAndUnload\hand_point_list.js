/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var allocate_vehicle_id = localStorage.getItem("allocate_vehicle_id");
var opt_type = ""; //业务类型
var voucher_num = ""; //出库单据号
var scanPackList = new Array(); //选中的单据号的捆包信息（待扫描）
var pda_switch = ""; //使用pda铁托架功能
var putoutVoucherList = new Array(); //出库单据列表
var voucher_count_max = 50; //扫描单据上限
var unUploadPack = new HashMap(); //未上传捆包信息  Map<voucher_id,packList>
var uploadPack = new HashMap(); //已上传捆包信息  Map<voucher_id,packList>
var packInfo = new HashMap(); //捆包信息  Map<pack_id,packInfo>
var contractSubInfo = new HashMap(); //形式提单子项信息  Map<contract_subid,weight_qty_info>
var confirm_flag = true;
var labelInfo = new HashMap(); //标签号、捆包号对照关系
//var putout_count = 0;
var voucherCount = ""; //提单数量

var open_from_url = ""; //通过哪个页面打开该页面
var open_from_id = ""; //通过哪个页面打开该页面
var queryVoucherList = new Array(); //已选出库单据

var allocate_scope = localStorage.getItem("allocate_scope"); //判断捆包

function getJQueryObject(elem) {
	return $(elem);
}
window.onload = function() {
	//从服务器获取数据  
	//....  
	//业务数据获取完毕，并已插入当前页面DOM；  
	//注意：若为ajax请求，则需将如下代码放在处理完ajax响应数据之后；  
	mui.plusReady(function() {
		var putoutScan = plus.webview.getWebviewById('putout_scan_new');
		var putoutPackListNew = plus.webview.getWebviewById('putout_pack_list_new');
		plus.webview.close(putoutScan);
		plus.webview.close(putoutPackListNew);
		open_from_url = plus.webview.currentWebview().open_from_url;
		open_from_id = plus.webview.currentWebview().open_from_id;
		queryVoucherList = plus.webview.currentWebview().queryVoucherList;
		//关闭等待框  
		plus.nativeUI.closeWaiting();
		//显示当前页面  
		mui.currentWebview.show();
	});
}

$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
	//$("#allocate_vehicle_id").val(allocate_vehicle_id);
	console.info(allocate_vehicle_id);
	if(allocate_vehicle_id != "" && allocate_vehicle_id != "null") {
		$("#allocate_vehicle_id").val(allocate_vehicle_id);
		//add by penglei by 2019年1月14日 
		if(allocate_scope == "20") {
			//查询提单 -- 按捆包查询提单
			queryAllocateExpectPack();
			console.log("按捆包查询提单按捆包查询提单queryAllocateExpectPack");
		} else {
			//查询提单
			queryAllocateVehicleD();
			console.log("查询提单查询提单查询提单queryAllocateVehicleD");
		}
	}

	(function($) {
		//删除已扫描单据号
		$('#table').on('tap', '.mui-btn.mui-btn-red', function(event) {
			var elem = this;
			var li = elem.parentNode.parentNode;
			var li_J = getJQueryObject(li);
			console.log(li_J.html());
			mui.confirm('确认删除该条记录？', '警告', ['确认', '取消'], function(e) {
				if(e.index == 0) {
					//TODO 删除出库单据
					//console.log(li_J.find("#voucher_num").text())
					delPutoutVoucherList(li_J.find("#voucher_num").text());
					li.parentNode.removeChild(li);
				} else {
					setTimeout(function() {
						$.swipeoutClose(li);
					}, 0);
				}
			}, 'div');
		});
	})(mui);
});

//点击搜索图标跳转页面
mui(document.body).on("tap", ".icon-search", function() {
	if(putoutVoucherList.length == voucher_count_max) {
		mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
		return false;
	}
	// 
	mui.openWindow({
		url: 'select_allocate_vehicle_no.html',
		id: 'select_allocate_vehicle_no',
		createNew: true,
		extras: {
			opt_type: opt_type,
			voucher_count_max: voucher_count_max,
			putoutVoucherList: putoutVoucherList,
		}
	});
});

function putoutPackDownLoad(voucher, putout_count) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","voucher_id":"' + voucher.voucher_id + '"}';
	console.log("exePutoutPackDownLoad调用参数："+params);
	var method = "exePutoutPackDownLoad";
	$.ajax({
		type: "post",
		async: false,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		//jsonp: "callback",//传递给请求处理程序或页面的，用以获得jsonp回调函数名的参数名(一般默认为:callback)
		//jsonpCallback:"flightHandler",//自定义的jsonp回调函数名称，默认为jQuery自动生成的随机函数名，也可以写"?"，jQuery会自动为你处理数据
		success: function(data) {
			//console.log("putout_count: " + putout_count);
			console.info("exePutoutPackDownLoad返回" + JSON.stringify(data));
			if(data != null) {
				console.info("exePutoutPackDownLoad:" + data.resultStatus);
				if(data.resultStatus == "1") {
					if(data.packList.length > 0) {
						//输出出库需要扫描的捆包信息
						handleDownloadPack(voucher, data.packList); //处理下载的捆包信息
					}
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				} else {
					//console.log(Object.keys(unUploadPack).length+ ">>>>>>>>>>>>>>>>>>");
					if(voucherCount == putout_count) {
						if(Object.keys(unUploadPack).length > 0) {
							//console.log("<<<<<<<<<<<<<<有捆包信息<<<<<<<<<<<");
							confirm_flag = true;
						} else {
							//console.log("<<<<<<<<<<<<<<无捆包信息<<<<<<<<<<<");
							console.log("error");
							confirm_flag = false;
							mui.alert("单据" + voucher.voucher_id + "下载信息：" + data.resultDesc, "提示", "确认", function() {}, "div");
						}
					} else {
						confirm_flag = true;
					}
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				}
			}
		},
		error: function() {
			confirm_flag = false;
			mui.alert("单据" + voucher.voucher_id + "下载信息失败", "提示", "确认", function() {}, "div");
			//当单据捆包信息下载完毕后 判断是否进行后续操作
			afterPackDownload(putout_count);
		}
	});

}

//出库列表中添加记录putoutVoucherList
function addPutoutVoucherList(record) {
	//校验扫描单据数目
	console.log("putoutVoucherList.length：" + putoutVoucherList.length);
	//if(voucherCount == voucher_count_max){
	if(putoutVoucherList.length == voucher_count_max) {
		mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
		return false;
	}
	var index = getIndexByVoucherId(record.voucher_num, putoutVoucherList);
	if(index != -1) {
		mui.alert("已存在该单据" + record.voucher_num + ",不能重复扫描", "提示", "确认", function() {}, "div");
		return false;
	} else {
		putoutVoucherList.push(record);
		return true;
	}
}

//出库列表中删除记录putoutVoucherList
function delPutoutVoucherList(voucher_id) {
	var index = getIndexByVoucherId(voucher_id, putoutVoucherList);
	if(index != -1) {
		//从index处删除几个
		putoutVoucherList.splice(index, 1);
		return true;
	} else {
		return false;
	}
}

//单据选择页面触发新增单据事件
window.addEventListener('addFromSelect', function(e) {
	//获得事件参数
	//var voucherList = e.detail.voucherList;//已选择单据列表
	putoutVoucherList = e.detail.voucherList; //已选择单据列表
	var chtml = $("#table").html();
	console.info("aaaa:::" + JSON.stringify(putoutVoucherList));
	//console.info("::::"+voucherList.length);
	$("#allocate_vehicle_id").val(localStorage.getItem("allocate_vehicle_id"));
	chtml = "";
	$.each(putoutVoucherList, function(i, item) {
		//console.info("aaaabbbbbbbbbbbbbbbbb"+i);
		//console.info("item.allocate_vehicle_id::::"+item.advice_style);
		if(item.advice_style == '10' || item.advice_style == '20') {
			opt_type = 'EL';
		} else if(item.advice_style == '30') {
			opt_type = 'DK';
		} else if(item.advice_style == '40') {
			opt_type = 'CA';
		}

		//添加单据
		if(!addPutoutVoucherList(item)) {
			return true;
		} else {
			//console.info("item.allocate_vehicle_id::::"+item.advice_style);

			chtml = chtml + '<li class="mui-table-view-cell">' +
				'<input id="identify_expirydate" value="' + item.allocate_vehicle_id + '" hidden="hidden"/>' +
				'<input id="identify_code" value="' + item.allocate_vehicle_subid + '" hidden="hidden"/>' +
				'<input id="send_to_fetch" value="' + item.logistics_id + '" hidden="hidden"/>' +
				'<div class="mui-slider-right mui-disabled">' +
				'<a class="mui-btn mui-btn-red mui-icon">删除</a>' +
				'</div>' +
				'<div class="mui-slider-handle">' +
				'<div>' +
				'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span>' + //<span class="icon">量</span>'+item.voucher_id+'</div>
				'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
				'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
				'</div>' +
				'</div>' +
				'</li>';
		}
	});
	$("#table").html(chtml);
});

//确认按钮事件绑定			
mui(document.body).on("tap", "#confirm", function() {
	//openPutoutScan();
	//mui(this).button('loading');
	//如果出现异常或者超时设置一分钟后可以再点一次
	setTimeout(function() {
		mui(this).button('reset');
	}.bind(this), 60000);
	//莫名其妙会把单据号输入框获得焦点 先干掉
	document.activeElement.blur();
	//校验已扫单据数量
	if(putoutVoucherList.length == 0) {
		mui.alert("请先查询单据信息", "提示", "确认", function() {}, "div");
		mui(this).button('reset');
		return false;
	}
	//如果非销售单据类型的,直接初始化信息;
	//销售单据类型如果不同形式提单包含相同订单子项不允许一起进行出库

	//初始化信息
	confirm_flag = true; //下载是否有报错
	//清空捆包下载信息
	unUploadPack.removeAll(); //未上传捆包信息
	uploadPack.removeAll(); //已上传捆包信息
	packInfo.removeAll(); //捆包信息
	contractSubInfo.removeAll(); //形式提单子项信息
	var putout_count = 0; //来统计遍历数量 标记是否是最后一次下载
	//循环遍历出库单据
	$.each(putoutVoucherList, function(i, item) {
		putout_count++;
		//TODO add by penglei 2019-1-15 判断根据捆包或者提单
		if(allocate_scope == "20") {
			//查询提单 -- 按捆包查询提单
			putoutExpectPackDownLoad(item, putout_count);
			console.log("按捆包查询提单按捆包查询提单putoutExpectPackDownLoad");
		} else {
			//查询提单
			putoutPackDownLoad(item, putout_count);
			console.log("查询提单查询提单查询提单putoutPackDownLoad");
		}
	});
});

function handleDownloadPack(voucher, downloadPackList) {
	if(downloadPackList.length > 0) {
		if(voucher.advice_style == "20") { //形式提单先校验订单子项号是否重复
			var flag = true; //标记校验是否通过
			$.each(downloadPackList, function(i, item) {
				if(contractSubInfo.get(item.voucher_subid) != null) {
					mui.alert("形式提单:" + voucher.voucher_id + "与其他形式提单的订单子项号重复,不能在同一批次出库,已经自动移除该单据,请后续重新扫描", "提示", "确认", function() {}, "div");
					flag = false;
					return false;
				}
			});
			if(flag) {
				var packList = new Array(); //未扫捆包信息集合
				$.each(downloadPackList, function(i, item) {
					//形式提单信息处理
					var info = {};
					info['price_style'] = item.price_style; //计价方式 按数量 按重量
					info['act_weight'] = item.act_weight; //act_weight 已出库重量
					info['act_qty'] = item.act_qty; //act_qty  已出库数量
					info['advice_weight'] = item.advice_weight; //advice_weight 发货通知重量
					info['advice_qty'] = item.advice_qty; //advice_qty  发货通知数量
					contractSubInfo.put(item.voucher_subid, info);

					//捆包信息
					var pack = {};
					pack["pack_id"] = item.pack_id;
					pack["label_id"] = item.label_id;
					pack["product_id"] = item.product_id;
					pack["scan_time"] = '';
					pack["steel_support_id"] = '';
					packList.push(pack); //添加捆包
					packInfo.put(item.pack_id, item); //添加捆包
				});
				unUploadPack.put(voucher.voucher_id, packList);
				//console.log("packList.length:" + packList.length + JSON.stringify(packList));
				//console.log("packInfo:" + JSON.stringify(packInfo['map']));
				//console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
				//console.log("contractSubInfo:" + JSON.stringify(contractSubInfo['map']));
				//console.log('advice_style:' + (packInfo.valueSet())[0].advice_style);
			}
		} else {
			var packList = new Array();
			$.each(downloadPackList, function(i, item) {
				var pack = {};
				pack["pack_id"] = item.pack_id;
				pack["label_id"] = item.label_id;
				pack["product_id"] = item.product_id;
				pack["scan_time"] = '';
				pack["steel_support_id"] = '';
				packList.push(pack); //添加捆包
				packInfo.put(item.pack_id, item); //添加捆包
				labelInfo.put(item.label_id, item.pack_id); //添加标签号、捆包对照关系
			});
			unUploadPack.put(voucher.voucher_id, packList);
			//console.log("packList.length:" + packList.length + JSON.stringify(packList));
			//console.log("packInfo:" + JSON.stringify(packInfo['map']));
			//console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
			//console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
			//console.log('advice_style:' + (packInfo.valueSet())[0].advice_style);
		}
	} else {
		console.log(voucher.voucher_id + "下载失败");
	}
}

//每反馈一次单据捆包下载信息调用一次
function afterPackDownload(putout_count) {
	console.info("aaaaaaaaaaaaa");
	//当单据信息下载完毕后 判断是否进行后续操作
	if(putout_count == putoutVoucherList.length) {
		//confirm_flag 校验在下载捆包信息过程中是否有报错
		if(!confirm_flag) {
			//清空捆包下载信息
			unUploadPack.removeAll(); //未上传捆包信息
			uploadPack.removeAll(); //已上传捆包信息
			packInfo.removeAll(); //捆包信息
			contractSubInfo.removeAll(); //形式提单子项信息

			mui($('#confirm')).button('reset');
			mui.alert("有单据捆包信息未下载成功,请删除单据或重试", "提示", "确认", function() {}, "div");
		} else {
			//console.log('下载完成');
			//打开出库捆包扫描页面
			openPutoutScan();
			localStorage.setItem("allocate_vehicle_id", $("#allocate_vehicle_id").val());
		}
	}
}
//下载捆包信息完成后打开出库捆包扫描页面
function openPutoutScan() {
	//console.info("--------------------------------------");
	//	console.info("opt_type::"+opt_type+"    pda_switch:::"+pda_switch);
	//  console.info("putoutVoucherList::"+JSON.stringify(putoutVoucherList)+"    unUploadPack:::"+JSON.stringify(unUploadPack['map']));
	//	console.info("uploadPack::"+JSON.stringify(uploadPack['map'])+"    packInfo:::"+JSON.stringify(packInfo['map']));
	//	console.info("labelInfo::"+JSON.stringify(labelInfo['map'])   );
	mui.openWindow({
		id: "putout_scan_new",
		url: "../putout/putout_scan_new.html",
		extras: {
			opt_type: opt_type,
			pda_switch: pda_switch,
			putoutVoucherList: putoutVoucherList,
			unUploadPack: unUploadPack,
			uploadPack: uploadPack,
			packInfo: packInfo,
			contractSubInfo: contractSubInfo,
			labelInfo: labelInfo,
			openType: 'hand_point_list'
		},
		createNew: true
	});

	//var ws = plus.webview.getWebviewById('putout_list_new');  
	//plus.webview.close(ws);
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	var vouchersize = putoutVoucherList.length;
	var aa = localStorage.getItem("allocate_vehicle_id");
	//	console.info(vouchersize+"    "+aa);
	if(vouchersize != 0 || localStorage.getItem("allocate_vehicle_id") != null) {
		if(!confirm("已有配车单确认，确定要返回吗？")) {
			return false;
		}
	}
	localStorage.removeItem("allocate_vehicle_id");
	mui.openWindow({
		url: 'hand_point_end.html',
		id: 'hand_point_end',
		createNew: true
	});
	//				var ws=plus.webview.currentWebview();
	//				plus.webview.close(ws);
	//				var w = plus.webview.getWebviewById('hand_manage');
	//				if(w != null){
	//					plus.webview.show(w);
	//				}else{
	//					w = plus.webview.create('hand_manage.html','hand_manage');
	//					plus.webview.show(w);
	//				}

	/* 直接修改登陆页面的 loacation.href 显示的菜单页面 这边当前窗口关闭后就直接显示index页面了
	 * mui.openWindow({
		id:"index"
	});*/

}
//查询提单
function queryAllocateVehicleD() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","allocate_vehicle_id":"' + allocate_vehicle_id + '"}';
	var method = "exeQueryAllocateVehicleD";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.resultStatus == "1") {
				if(data.resultDesc.length > 0) {
					//console.log(data.resultDesc.length + ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
					voucherCount = data.resultDesc.length;
					queryVoucherList = data.resultDesc;
					localStorage.setItem("allocate_vehicle_id", allocate_vehicle_id);
					console.info("跳转之前：" + queryVoucherList.length);
					/*var page = plus.webview.getWebviewById('hand_point_list');
								mui.fire(page,'addFromSelect',{
									//optType : opt_type,
									voucherList : queryVoucherList
								});*/
					//获得事件参数
					//var voucherList = e.detail.voucherList;//已选择单据列表
					//putoutVoucherList = e.detail.voucherList;//已选择单据列表
					//add by penglei TODO 遍历方式不用监听方法
					var chtml = $("#table").html();
					console.info("aaaa:::" + JSON.stringify(queryVoucherList));
					//console.info("::::"+voucherList.length);
					$("#allocate_vehicle_id").val(localStorage.getItem("allocate_vehicle_id"));
					chtml = "";
					$.each(queryVoucherList, function(i, item) {
						//console.info("aaaabbbbbbbbbbbbbbbbb"+i);
						//console.info("item.allocate_vehicle_id::::"+item.advice_style);
						if(item.advice_style == '10' || item.advice_style == '20') {
							opt_type = 'EL';
						} else if(item.advice_style == '30') {
							opt_type = 'DK';
						} else if(item.advice_style == '40') {
							opt_type = 'CA';
						}

						//添加单据
						if(!addPutoutVoucherList(item)) {
							return true;
						} else {
							//console.info("item.allocate_vehicle_id::::"+item.advice_style);

							chtml = chtml + '<li class="mui-table-view-cell">' +
								'<input id="identify_expirydate" value="' + item.allocate_vehicle_id + '" hidden="hidden"/>' +
								'<input id="identify_code" value="' + item.allocate_vehicle_subid + '" hidden="hidden"/>' +
								'<input id="send_to_fetch" value="' + item.logistics_id + '" hidden="hidden"/>' +
								'<div class="mui-slider-right mui-disabled">' +
								'<a class="mui-btn mui-btn-red mui-icon">删除</a>' +
								'</div>' +
								'<div class="mui-slider-handle">' +
								'<div>' +
								'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span>' + //<span class="icon">量</span>'+item.voucher_id+'</div>
								'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
								'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
								'</div>' +
								'</div>' +
								'</li>';
						}
					});
					$("#table").html(chtml);
					//跳转回出库单据页面
					/*mui.openWindow({
						id:'hand_point_list'	
					});*/

					//进入单据选择页面
					/* mui.openWindow({
									url:'putout_voucher_select.html',
									id:"putout_voucher_select",
									createNew:true,
									extras:{
										open_from_url:"select_allocate_vehicle_no.html",
					        			open_from_id:"select_allocate_vehicle_no",
					        			opt_type:"1",
					        			queryVoucherList:queryVoucherList,
					        			putoutVoucherList:putoutVoucherList,
										voucher_count_max:voucher_count_max
									}
								});*/
				} else {
					mui.alert('', "没有查询到单据信息");
					return false;
				}
			} else {
				mui.alert(data.resultDesc, "提示", "确认", function() {}, "div");
				return false;
			}

			//						console.info(data.resultStatus.length);
			//						if(data.resultStatus.length == 0){
			//							 mui.alert("没有查询到单据信息", "提示", "确定", function() {
			//							}, 'div');
			//							return ;
			//						}else{
			//						   queryVoucherList = data.resultDesc;
			//						   var page = plus.webview.getWebviewById('hand_point_list');
			//							mui.fire(page,'addFromSelect',{
			//								voucherList : queryVoucherList
			//							});
			//							//跳转回出库单据页面
			//							mui.openWindow({
			//								id:'hand_point_list'	
			//							});
			//						}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//查询提单 -- 按捆包查询提单 add by penglei
function queryAllocateExpectPack() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","allocate_vehicle_id":"' + allocate_vehicle_id + '"}';
	var method = "exeQueryAllocateExpectPack";
	params = encodeURI(params, 'utf-8');
	console.log(outUri);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.resultStatus == "1") {
				if(data.resultDesc.length > 0) {
					voucherCount = data.resultDesc.length;
					queryVoucherList = data.resultDesc;
					localStorage.setItem("allocate_vehicle_id", allocate_vehicle_id);
					var chtml = $("#table").html();
					$("#allocate_vehicle_id").val(localStorage.getItem("allocate_vehicle_id"));
					chtml = "";
					$.each(queryVoucherList, function(i, item) {
						if(item.advice_style == '10' || item.advice_style == '20') {
							opt_type = 'EL';
						} else if(item.advice_style == '30') {
							opt_type = 'DK';
						} else if(item.advice_style == '40') {
							opt_type = 'CA';
						}
						//添加单据
						if(!addPutoutVoucherList(item)) {
							return true;
						} else {
							chtml = chtml + '<li class="mui-table-view-cell">' +
								'<input id="identify_expirydate" value="' + item.allocate_vehicle_id + '" hidden="hidden"/>' +
								'<input id="identify_code" value="' + item.allocate_vehicle_subid + '" hidden="hidden"/>' +
								'<input id="send_to_fetch" value="' + item.logistics_id + '" hidden="hidden"/>' +
								'<div class="mui-slider-right mui-disabled">' +
								'<a class="mui-btn mui-btn-red mui-icon">删除</a>' +
								'</div>' +
								'<div class="mui-slider-handle">' +
								'<div>' +
								'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span>' + //<span class="icon">量</span>'+item.voucher_id+'</div>
								'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
								'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
								'</div>' +
								'</div>' +
								'</li>';
						}
					});
					$("#table").html(chtml);
				} else {
					mui.alert('', "没有查询到单据信息");
					return false;
				}
			} else {
				mui.alert(data.resultDesc, "提示", "确认", function() {}, "div");
				return false;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

//按捆包配车单下载出库捆包信息
function putoutExpectPackDownLoad(voucher, putout_count) {
	var outUri = domainName + "webService_test.jsp";
	//var innerUri = 'http://'+webServiceUrl+'/sm/ws/PDAPutoutService';
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/IPDAHandPointService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","allocate_vehicle_id":"' + allocate_vehicle_id + '","voucher_id":"' + voucher.voucher_id + '"}';
	console.log(JSON.stringify(params) + "11111111111" + params);
	var method = "exePutoutExpectPackDownLoad";
	$.ajax({
		type: "post",
		async: false,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		//jsonp: "callback",//传递给请求处理程序或页面的，用以获得jsonp回调函数名的参数名(一般默认为:callback)
		//jsonpCallback:"flightHandler",//自定义的jsonp回调函数名称，默认为jQuery自动生成的随机函数名，也可以写"?"，jQuery会自动为你处理数据
		success: function(data) {
			//console.log("putout_count: " + putout_count);
			if(data != null) {
				console.info("aaaaaaaaaaaaaaaaaa::::" + data.resultStatus);
				if(data.resultStatus == "1") {
					if(data.packList.length > 0) {
						//输出出库需要扫描的捆包信息
						handleDownloadPack(voucher, data.packList); //处理下载的捆包信息
					}
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				} else {
					//console.log(Object.keys(unUploadPack).length+ ">>>>>>>>>>>>>>>>>>");
					if(voucherCount == putout_count) {
						if(Object.keys(unUploadPack).length > 0) {
							//console.log("<<<<<<<<<<<<<<有捆包信息<<<<<<<<<<<");
							confirm_flag = true;
						} else {
							//console.log("<<<<<<<<<<<<<<无捆包信息<<<<<<<<<<<");
							console.log("error");
							confirm_flag = false;
							mui.alert("单据" + voucher.voucher_id + "下载信息：" + data.resultDesc, "提示", "确认", function() {}, "div");
						}
					} else {
						confirm_flag = true;
					}
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				}
			}
		},
		error: function() {
			confirm_flag = false;
			mui.alert("单据" + voucher.voucher_id + "下载信息失败", "提示", "确认", function() {}, "div");
			//当单据捆包信息下载完毕后 判断是否进行后续操作
			afterPackDownload(putout_count);
		}
	});

}