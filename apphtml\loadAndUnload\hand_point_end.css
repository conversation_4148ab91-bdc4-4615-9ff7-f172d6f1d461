/** 明细样式 */

.detail_row {
	height: 49px;
}

.detail_textarea {
	height: 100px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 64%;
	font-size: 22px;
	padding: 10px 0px;
}

#next_target {
	width: 66%;
	padding: 0px 5px;
	font-size: 20px;
}

#next_hand_point {
	width: 99%;
	padding: 0px 5px;
	font-size: 20px;
}

.pack_list {
	height: 0;
	overflow: auto;
	border: 0px solid #AAAAAA;
}

.storage_pack {
	width: 50%;
	margin-top: 12px;
}

.li-text {
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 78px;
}

.li-text p span font {
	text-align: center;
	font-size: 22px;
	color: #000000;
}

.li-height {
	margin-top: 4px;
}


/** 列表样式 */

.pack_location_target {
	color: red;
	font-size: 22px;
}

.pack_location_now {
	color: blue;
	font-size: 22px;
	margin-top: 8px;
}


/*	#query_next_target{
				font-size: 30px;
				position: absolute;
				z-index: 10;
				top: 120px;
				right: 0;
				width: 45px;
				height: 38px;
			}*/

#query_next_target {
	font-size: 30px;
	position: absolute;
	z-index: 10;
	top: 305px;
	right: 0;
	width: 55px;
	height: 38px;
}

.icon-search {
	position: absolute;
	left: 320px;
	z-index: 5;
	background-image: url(../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 140px;
	/*图片显示的高*/
	right: 5px;
}

ul {
	width: 100%;
	float: right;
	margin-right: 9px;
}

li {
	/*  text-align: center;*/
}

#next_target,
#next_hand_point {
	background: #CCCCCC;
}


/** 弹出图层设置 */

#pop_car_info {
	position: absolute;
	z-index: 999;
	width: 280px;
	height: 350px;
	left: 38%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_car_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_car_info>.title {
	/*text-align: center;*/
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
	margin-left: 10px;
}

#pop_car {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_car.show {
	visibility: visible;
	opacity: 1;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

.next_hand_point {
	color: blue;
	font-size: 22px;
}


/*更多按钮*/

.morebut_s {
	margin-left: 25%;
	margin-top: -6px;
	background-color: green;
	color: white;
}

.divnone {
	display: none;
}

.point_name {
	color: blue;
	margin-left: 10px;
}