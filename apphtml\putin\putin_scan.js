/**
 * 初始化变量信息 
 */
var putinPackList = new Array(); //入库捆包列表（已扫描列表）

var wprovider_id = ""; //仓库代码
var wprovider_name = ""; //仓库名称
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");

//定义全局变量(、、)，便于在PDA扫描捆包之后进行数据比对，判断是否生成修改标记 (罗应辉)
var global_pack_id = "";
var global_spec = "";
var global_product_process_id = "";
var global_inner_diameter = "";
var global_factory_procudt_id = "";
var global_weight = "";
var global_qty = "";
var global_edit_flag = null;
// end
var flag = 0; //定义开关值
var mean_name = localStorage.getItem("name"); //判断是从菜单进来的还是出厂物流进来的

var factory_wprovider = localStorage.getItem("factory_wprovider"); //厂区对应的仓库
var team_id = localStorage.getItem("team_id"); //班组代码
//1为扫描捆包信息查询出来的捆包信息修改。 2 没有找到捆包号对应信息，手工录入的
var type = "2"; //2表示捆包未匹配到入库资源。从已扫描列表修改捆包回传到入库扫描页面后type=1 
//var vehicle_id=localStorage.getItem("vehicle_id");//出厂物流进厂车牌
//智慧仓库 add by wangshengbo171211
var inner_diameter = ''; //卷内径
var innerDiameterListHtml = ""; //卷内径下拉列表html
var auto_loc_flag = ""; // 库位自动分配开关 1表示开关打开，其他表示开关关闭
var location_type = ""; //库位精细化管理类型 1 条状库位、2点状库位
var forLocationPack = {}; //当前扫描捆包对象
var handLocationFlag = "0"; //自动分配未检索到合适库位时需手工输入库位。 1表示需要手工输入，用于设置输入框读写效果
var selectVehicleNo = localStorage.getItem("putin_vehicle_no") == null ? "" : localStorage.getItem("putin_vehicle_no"); //自动分配开关打开；库位精细化管理。入库钱必须选择车牌号
//var packNotFindFlag = "0";//1表示捆包未匹配到入库资源
var if_judge_pack_location = "0"; //库位开关
var vehicle_id = localStorage.getItem("putin_vehicle_no") == null ? "" : localStorage.getItem("putin_vehicle_no"); //自动分配开关打开；库位精细化管理。入库钱必须选择车牌号

// add by lal 
var productProcessListHtml = ""; //首道加工工序下拉列表
var product_process_id = "";
var productProcessAry = new Array();
var material_shape = "";

//add by yangzemin
var damageTypeListHtml = ""; //货损类型下拉列表
//货损类型查询出来的数据
var damageTypedata = {};

//add by tangli 20191203 湛江现货流程 自动获取用户对应库位
var pack_location_zj = "";
//add by liuxu 20200916  佛山宝钢智慧仓库传入厂区和装卸点
var hand_point_id = "";
var factory_area_id = "";
var pack_id_lx = "";

window.onload = function onload() {
	mui.plusReady(function() {
		queryConfigPDAVoucherCountMax();
		wprovider_id = localStorage.getItem("wprovider_id");
		wprovider_name = localStorage.getItem("wprovider_name");
		auto_loc_flag = localStorage.getItem("auto_loc_flag");
		location_type = localStorage.getItem("location_type");
		if (segNo == '00126') {
			hand_point_id = localStorage.getItem("hand_point_id");
			factory_area_id = localStorage.getItem("factory_area_id");
			$("#unload").css("display", "block");
		}
		if (mean_name == "load_menus" && segNo == "00138") {
			$("#next_step").show();
			$(".icon-car").css("display", "none");
		} else {
			$("#next_step").hide();
		};
		if (segNo == "00138" || segNo == "00181" ) {
			$(".gqg").css("display", "block");
		};
		setDefaultInputStyle(); //智慧仓库add by wangshengbo171211
		queryProductProcessId(); // add by lal
		mui('.mui-input-row textarea').input();
		console.log("值是："+auto_loc_flag+"====="+location_type+"======"+segNo+"===");
	});

	//change by yangzemin 20201020
	// if(wprovider_id == "1GQ01") {
	if (wprovider_id == "037413005") {
		factory_wprovider = wprovider_id;
	}
	$("#wprovider_name_span").html("仓库：" + localStorage.getItem("wprovider_name"));
	if (mean_name != "putin_scan") {
		if (selectVehicleNo != "") {
			$("#vehicle_no_span").html("车牌：" + selectVehicleNo);
		}
		if (vehicle_id != "") {
			$("#vehicle_no_span").html("&nbsp;车牌：" + vehicle_id);
		}
	} else {
		if (selectVehicleNo != "") {
			$("#vehicle_no_span").html("车牌：" + selectVehicleNo);
		}
	}
	if ($("#vehicle_no_span").html() != "") {
		var vvv = $("#vehicle_no_span").html();
		vehicle_id = vvv.substr(9, vvv.length);
	}

	//捆包输入框添加监听事件
	//add by yangzm 一汽宝友二维码扫描  20201210新增佛宝
	if (segNo == '00127' || segNo == '00126') {
		$('#pack_id').bind('input propertychange', function() {
			if ($(this).val()) {
				//有值
				var qrCode = $(this).val();
				// console.log("parseQrCode -> " + qrCode);
				if (qrCode.length < 50) {
					return;
				}
				if (segNo == '00127') {
					parseQrCode(qrCode);
				} else if (segNo = '00126') {
					//佛宝二维码解析
					parseFBQrCode(qrCode);
				}
			}
		});
	}

	//add by tangli 20191126 ERP_58339 湛江物流入库查询账号对应外库库区
	if (segNo == '00166') {
		getPacklocation_zj();
	}
	//捆包输入框添加监听事件
	//add by yangzm 郑州宝钢添加货损类型
	//20211116 开封剪配开通货损类型
	if (segNo == '00115' || segNo == '00126' || segNo == '00170') {
		$(".damage").css("display", "block");
		//查询货损类型
		queryDamageType(false);
	}
	//一汽宝友测试代码
	if (segNo == '00127') {
		// parseQrCode("鞍钢股份;订单号 :20457840202;子项号:; 外销合同号:FAWV85G06202;收货单位 :德邻陆港（鞍山）有限责任公司代长春一汽宝友钢材加工配送有限公司; 品名:冷卷;最终用户:中国第一汽车股份有限公司采购中心;起运港 & 作业区: ;目的港 & 作业区 : ;包装日期 :; 检查员:章屹;熔炼号 :20DD8782;毛重:6130;净重 :6060; 规格:1.5mm*960mm*C;材质:250P1;卷号:M0A18193010B00;卷数:1;出口内经:610;公差类型:负公差:表面处理:800:表面质量等级:FB");
		// parseQrCode(
		// 	"http://m.steel-n.com.ZC88/ZC88010/zc880108230.jsp?pO=01S4043159010&pP=CSZ6073B&pS=0.9X1650XC    &pQ=8210&pG=8307")
		// parseQrCode(
		// 	"Brand:SHOUGANG GROUP;product:Cold rolled coil;STEEL GRADE:St14;CONTRACT No:E2000493/EC20002727;Coil No.:TB120110805003;Net Weight:9.000;Size:0.90x1972xC;Coating Weight:;Date:2020-01-05"
		// )
		//马钢
		// parseQrCode(
		// 	"冷轧总厂;冷 轧 钢 带;A20B005801;P170;0.9×1070(mm×mm);MTS 8233-2017-01;FB;6.75 Ton;6.71 Ton;徐春明;20201109;20308036;5C;819244-90;MADE IN MASTEEL.255422502962265824575@1"
		// );
		// parseQrCode(
		// 	"冷轧总厂;冷 轧 钢 带;A20B005802;P170;0.9×1070(mm×mm);MTS 8233-2017-01;FB;6.75 Ton;6.71 Ton;徐春明;20201109;20308036;5C;819244-90;MADE IN MASTEEL.225129572659225428615@1"
		// );
		// parseQrCode("首钢集团;品名:冷轧板卷;牌号:St14;合同号/合约号:E2000493/EC20002727;材料号:TL112001001343;规格:1.0*1480*C;净重:7.51T;毛重:7.603T");
		// parseQrCode("http://m.steel-n.com/ZC88/ZC88010/zc880108230.jsp?pO=01S4600407010&pP=CSL1780A   &pS=0.9X910XC                       &pQ=7260&pG=7344");
	}
	//佛宝测试代码
	if (segNo == '00126') {
		// parseFBQrCode(
		// 	"*卡号:KM909C7420X00\n*牌号:SUH409L\n*规格:1.20×1260×1520\n*批次:\n*标准:JIS G 4312:201\n*毛重:16635\n*净重:16526\n*边部:不切边\n*炉号:B3905482\n*参厚:1.131\n*表面:2B\n*缺陷扣重:0\n*张数:1\n*销售库存标识:\n*下一产线:9A\n*生产日期:2019/09/23$$"
		// );
		// parseFBQrCode("福建甬金\n钢卷号:FJ1909111919-04\n钢种/用途:304/304\n规格:0.38(0.376)*1247*C\n重量:6971KG");
	}

}

mui.init({
	swipeBack: true, //关闭右滑关闭功能
	gestureConfig: {
		longtap: true,
		doubletap: true
	}
});

/**
 * add by yzm 一汽宝友二维码解析
 * @param {Object} code
 */
function parseQrCode(code) {
	var packId = "";
	var spec = "";
	//钢厂资源号
	var factoryId = "";
	var replaceFactoryId = "";
	//净重
	var weight = "";
	//毛重
	var grossWeight = "";
	var count = "1";
	//卷内径
	var packR = "";
	var labelArray = [];
	console.log("解析二维码：" + code);
	if (code.indexOf("鞍钢") != -1) {
		//鞍钢标签二维码
		labelArray = code.split(";");
		var factoryStr = labelArray[1].substr(labelArray[1].indexOf(":") + 1);
		console.log("factoryStr = " + factoryStr);
		if (factoryStr.charAt(factoryStr.length - 3) == '-' &&
			factoryStr.charAt(factoryStr.length - 2) == '0') {
			//第2位如果是0，就替换成-符号
			factoryId = factoryStr.substring(0, factoryStr.length - 2) + factoryStr.charAt(factoryStr.length - 1);
		} else if (factoryStr.charAt(factoryStr.length - 2) == '0') {
			//第2位如果是0，就替换成-符号
			factoryId = factoryStr.substring(0, factoryStr.length - 2) +
				"-" + factoryStr.charAt(factoryStr.length - 1);
		} else {
			factoryId = factoryStr;
		}
		console.log("factoryId = " + factoryId);

		grossWeightStr = labelArray[12].substr(labelArray[12].indexOf(":") + 1);
		weightStr = labelArray[13].substr(labelArray[13].indexOf(":") + 1);
		//转化成吨
		grossWeight = parseInt(grossWeightStr) / 1000;
		weight = parseInt(weightStr) / 1000;

		spec = labelArray[14].substr(labelArray[14].indexOf(":") + 1);
		//去掉规格中单位mm
		var reg = new RegExp("mm", "g");
		spec = spec.replace(reg, "");
		packId = labelArray[16].substr(labelArray[16].indexOf(":") + 1);
		if (labelArray.length > 17) {
			count = labelArray[17].substr(labelArray[17].indexOf(":") + 1);
		}
		if (labelArray.length > 18) {
			packR = labelArray[18].substr(labelArray[18].indexOf(":") + 1);
		}

	} else if (code.indexOf("http://m.steel-n.com") != -1) {
		//韩国浦项
		code = code.substr(code.indexOf("?"));
		console.log("浦项二维码截取后：" + code);
		labelArray = code.split("&");
		factoryId = labelArray[0].substr(labelArray[0].indexOf("=") + 1);
		packId = labelArray[1].substr(labelArray[1].indexOf("=") + 1);
		spec = labelArray[2].substr(labelArray[2].indexOf("=") + 1);
		weightStr = labelArray[3].substr(labelArray[3].indexOf("=") + 1);
		grossWeightStr = labelArray[4].substr(labelArray[4].indexOf("=") + 1);

		//转化成吨
		grossWeight = parseInt(grossWeightStr) / 1000;
		weight = parseInt(weightStr) / 1000;

	} else if (code.indexOf("MADE IN MASTEEL") != -1) {
		//马鞍山钢铁
		labelArray = code.split(";");

		packId = labelArray[2];
		spec = labelArray[4];
		spec = spec.replace("(mmxmm)", "");
		grossWeight = labelArray[7].split(" ")[0];
		weight = labelArray[8].split(" ")[0];
		factoryId = labelArray[13];

	} else if (code.indexOf("SHOUGANG") != -1) {
		//首钢英文二维码标签
		labelArray = code.split(";");
		factoryId = labelArray[3].substr(labelArray[3].indexOf(":") + 1);
		if (factoryId.indexOf("/") != -1) {
			factoryId = factoryId.substr(factoryId.indexOf("/") + 1);
			replaceFactoryId = factoryId.replace("/", "-");
		}
		packId = labelArray[4].substr(labelArray[4].indexOf(":") + 1);
		weight = labelArray[5].substr(labelArray[5].indexOf(":") + 1);
		//首钢没有毛重，给成净重
		grossWeight = parseFloat(weight) + 0.080;
		spec = labelArray[6].substr(labelArray[6].indexOf(":") + 1);
	} else if (code.indexOf("首钢") != -1) {
		//首钢中文二维码标签
		labelArray = code.split(";");
		var length = labelArray.length;
		for (var i = 1; i < length; i++) {
			var labelItem = labelArray[i];
			console.log("首钢->labelItem：" + labelItem);
			if (labelItem.indexOf("合同") != -1) {
				if (labelItem.indexOf(":") != -1) {
					factoryId = labelItem.substr(labelItem.indexOf(":") + 1);
				} else {
					factoryId = labelItem.substr(labelItem.indexOf("合同") + "合同".length);
				}
				if (factoryId.indexOf("/") != -1) {
					replaceFactoryId = factoryId.replace("/", "-");
					factoryId = factoryId.substr(factoryId.indexOf("/") + 1);
				}
			} else if (labelItem.indexOf("材料") != -1) {
				if (labelItem.indexOf(":") != -1) {
					packId = labelItem.substr(labelItem.indexOf(":") + 1);
				} else {
					packId = labelItem.substr(labelItem.indexOf("材料") + "材料".length);
				}
			} else if (labelItem.indexOf("规格") != -1) {
				if (labelItem.indexOf(":") != -1) {
					spec = labelItem.substr(labelItem.indexOf(":") + 1);
				} else {
					spec = labelItem.substr(labelItem.indexOf("规格") + "规格".length);
				}
			} else if (labelItem.indexOf("净重:") != -1) {
				weight = labelItem.substr(labelItem.indexOf(":") + 1);
			} else if (labelItem.indexOf("毛重:") != -1) {
				grossWeight = labelItem.substr(labelItem.indexOf(":") + 1);
				if (grossWeight.indexOf("T") != -1) {
					grossWeight = grossWeight.replace("T", "");
				}
			} else if (labelItem.indexOf("重") != -1) {
				weight = labelItem.substr(labelItem.indexOf("重") + "重".length);
			}
		}
		if (weight.indexOf("T") != -1) {
			weight = weight.replace("T", "");
		}
		if (grossWeight == "") {
			//首钢没有毛重，给成净重
			grossWeight = parseFloat(weight) + 0.080;
		}
	} else {
		return;
	}

	//去空格
	packId = packId.trim();
	spec = spec.trim();
	packR = packR.trim();
	factoryId = factoryId.trim();
	console.log("二维码解析结果=========");
	console.log("packId：" + packId);
	console.log("spec：" + spec);
	console.log("weight：" + weight);
	console.log("grossWeight：" + grossWeight);
	console.log("count：" + count);
	console.log("packR：" + packR);
	console.log("factoryId：" + factoryId);
	console.log("replaceFactoryId：" + replaceFactoryId);
	//写入数据
	$("#pack_id").val(packId);
	$("#spec").val(spec);
	$("#factory_product_id").val(factoryId);
	$("#weight").val(weight);
	$("#qty").val(1);
	$("#inner_diameter").val(packR);
	// $("#product_process_id").val();

	//解析成功后调用查询捆包
	var pda_type = 2;
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + packId +
		'","wprovider_id":"' + wprovider_id + '","vehicle_no":"' + vehicle_id +
		'","factory_product_id":"' + factoryId + '","spec":"' + spec + '","pda_type":"' + pda_type +
		'","putin_weight":"' + weight + '","gross_weight":"' + grossWeight + '"}';

	//	 mui.toast("扫描到二维码");
	queryPackForQrCode(params, factoryId, replaceFactoryId);
}

/**
 * add by yzm 佛宝二维码解析
 */
function parseFBQrCode(code) {
	var packId = "";
	var spec = "";
	//钢厂资源号
	var factoryId = "";
	//净重
	var weight = "";
	//毛重
	var grossWeight = "";
	var count = "1";
	//卷内径
	var packR = "";
	var labelArray = [];
	console.log("解析二维码：" + code);
	code = code.replace(/([\n\r]+)/g, " ");
	console.log("替换后二维码：" + code);
	if (code.indexOf("福建甬金") != -1) {
		//福建甬金二维码
		labelArray = code.split(" ");

		packId = labelArray[1].substr(labelArray[1].indexOf(":") + 1);
		spec = labelArray[3].substr(labelArray[3].indexOf(":") + 1);
		weightStr = labelArray[4].substr(labelArray[4].indexOf(":") + 1).trim();
		//转化成吨
		weight = parseInt(weightStr) / 1000;

	} else if (code.indexOf("*卡号") != -1) {
		//太钢标签
		labelArray = code.split("*");

		packId = labelArray[1].substr(labelArray[1].indexOf(":") + 1);
		spec = labelArray[3].substr(labelArray[3].indexOf(":") + 1);
		grossWeightStr = labelArray[6].substr(labelArray[6].indexOf(":") + 1).trim();
		weightStr = labelArray[7].substr(labelArray[7].indexOf(":") + 1).trim();

		//转化成吨
		grossWeight = parseInt(grossWeightStr) / 1000;
		weight = parseInt(weightStr) / 1000;

	} else if (code.indexOf("MADE IN MASTEEL") != -1) {
		//马鞍山钢铁
		labelArray = code.split(";");

		packId = labelArray[2];
		spec = labelArray[4];
		spec = spec.replace("(mmxmm)", "");
		grossWeight = labelArray[7].split(" ")[0];
		weight = labelArray[8].split(" ")[0];
		factoryId = labelArray[13];

	} else {
		return;
	}

	//去空格
	packId = packId.trim();
	spec = spec.trim();
	packR = packR.trim();
	factoryId = factoryId.trim();
	console.log("佛宝二维码解析结果=========");
	console.log("packId：" + packId);
	console.log("spec：" + spec);
	console.log("weight：" + weight);
	console.log("grossWeight：" + grossWeight);
	console.log("count：" + count);
	console.log("packR：" + packR);
	console.log("factoryId：" + factoryId);
	//写入数据
	$("#pack_id").val(packId);
	$("#spec").val(spec);
	$("#factory_product_id").val(factoryId);
	$("#weight").val(weight);
	$("#qty").val(1);
	$("#inner_diameter").val(packR);
	// $("#product_process_id").val();
	initData();
}

$(function() {
	//库位输入框获得焦点
	$("#pack_location")[0].focus();
});
//智慧仓库add by wangshengbo171211
function setDefaultInputStyle() {
	//console.log("setDefaultInputStyle"+isAutoAllocLocation());
	if (isAutoAllocLocation()) {
		$("#pack_id")[0].focus(); //自动库位分配光标默认聚焦到捆包号输入框
		setInputReadOnly("#pack_location", true);
		console.log("近的true");
	} else {
		//库位输入框获得焦点
		$("#pack_location")[0].focus();
		setInputReadOnly("#pack_location", false);
		console.log("近的false");
	}
};
/*
 点状库位，清空上一个捆包的库位
 * */
function clearPreviousLocation() {
	if (location_type == '2') {
		$("#pack_location").val("");
		$("#pack_id").focus();
	}
	if (location_type == '1') {
		$("#pack_id").focus();
	}

	//add by tangli 20191203 湛江现货入库查询账号对应外库库区
	if (segNo == '00166' && pack_location_zj != "") {
		$("#pack_location").val(pack_location_zj);
	}
}

//智慧仓库add by wangshengbo171211
function setInputReadOnly(inputName, isReadOnly) {
	//上海宝井坑，只先上一个库区，库位放开可以扫描或者输入
	if (segNo == '00137' && inputName == '#pack_location' && isReadOnly == true || segNo == '00129') {
		isReadOnly = false;
	}
	console.log(inputName+"ddfdf");
	$(inputName).attr("readOnly", isReadOnly);
	if (isReadOnly) {
		$(inputName).css("background-color", "#E0E0E0");
		//白色$(inputName).css("background-color", "#FFFFFF");
	} else {
		$(inputName).css("background-color", "#FFFFFF");
		//灰色$(inputName).css("background-color", "#E0E0E0");
	}
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});
mui.back = function() {
	if (!checkBeforeback()) {
		return false;
	}
	var self = plus.webview.currentWebview();
	var popHandPoint = self.openType;
	//console.info("popHandPointpopHandPoint::" + popHandPoint);
	localStorage.setItem("wprovider_id", wprovider_id);
	localStorage.setItem("wprovider_name", wprovider_name);
	localStorage.setItem("auto_loc_flag", auto_loc_flag);
	if ("vehicle_load_manage" == popHandPoint) {
		mui.openWindow({
			url: '../loadAndUnload/hand_point_end.html',
			id: 'hand_point_end',
			createNew: true
		});
	} else if (putinPackList.length == 0 || putinPackList == null || putinPackList == []) {
		mui.openWindow({
			id: "index",
			url: "../public/index.html",
			createNew: true
		});
	} else {
		var btnArray = ['退出', '取消'];
		mui.confirm('存在已扫描未上传的数据,是否退出?', '提示', btnArray, function(e) {
			if (e.index == 0) {
				setTimeout(function() {
					var ws = plus.webview.currentWebview();
					plus.webview.close(ws);
					mui.openWindow({
						url: '../public/index.html',
						id: 'index',
						createNew: false
					});
				}, 0);
			}
		}, 'div');
	}
}

mui(document.body).on("tap", ".icon-setting", function() {
	mui.openWindow({
		url: '../public/select_wprovider_id.html',
		id: 'select_wprovider_id',
		createNew: true
	});
});

mui(document.body).on("tap", "#next_step", function() {
	if (putinPackList.length > 0) {
		if (!confirm("有捆包已扫描，确定要返回吗？")) {
			return false;
		}
	}
	mui.openWindow({
		url: '../loadAndUnload/hand_point_end.html',
		id: 'hand_point_end',
		createNew: true
	});
});

mui(document.body).on("tap", ".icon-car", function() {
	mui.openWindow({
		url: '../public/select_vehicle_no.html',
		id: 'select_vehicle_no',
		createNew: true
	});
});

//增加keypress监听
/*
 modify by wangshengbo 171211 自动库位分配
                  扫描捆包后自动获取库位失败，手工输入库位后将当前捆包添加到已扫描列表以及后续处理
 * */
$("#pack_location").keypress(function(e) {
	if (e.keyCode == 13) {
		document.activeElement.blur();
		var pack_id = $("#pack_id").val();
		if ($("#pack_location").val() == "") {
			mui.alert("请扫描或者输入库位", "提示", "确定", null, "div");
			$("#pack_location")[0].focus();
			return;
		}

		if (!isAutoAllocLocation()) {
			console.log("库位开关：" + if_judge_pack_location);
			if (if_judge_pack_location == "1") { //库位开关打开时，判断库位是否存在
				judgeLocationExist();
			} else {
				if (pack_id != "" && pack_id != null && pack_id != undefined) {
					if (segNo == '00166') {
						//湛江物流会出现先扫捆包再扫库位的情况，这时候捆包还未添加进扫描列表，所以库位按回车的时候应该把光标再设置到捆包上
						console.log("湛江物流焦点到捆包");
						$("#pack_id")[0].focus();
					} else {
						afterGetPackObj();
					}
				} else {
					$("#pack_id")[0].focus();
				}
			}
			/*//捆包输入框获得焦点
	        		  if(pack_id == ""){
	        		  	$("#pack_id")[0].focus();
	        		  }else{
	        			afterGetPackObj();
	        		  }*/
		} else {
			if ($("#pack_location").val() != "") {
				$("#pack_id")[0].focus();
			}
		}
		console.log("库位回车提示：" + isAutoAllocLocation())
		if (isAutoAllocLocation() && pack_id != "") {
			/*未匹配到资源号的捆包，输入库位后需要点【修改按钮】添加到已扫描列表*/
			if (JSON.stringify(forLocationPack) != "{}") {
				setLocationInfo($("#pack_location").val(), 0);
				afterGetPackObj(); //智慧仓库modify by wangshengbo171211
			} else {
				mui.alert("请确定捆包信息后，点击【修改】按钮将捆包添加到已扫描列表", "提示", "确定", function() {}, "div");
				return false;
			}
		}
	}
});
//捆包号扫描
$("#pack_id").keypress(function(e) {
	if (e.keyCode == 13) {
		//add by wanghshengbo 180416 智慧仓库车牌号必填项控制
		//add by liuxu 佛宝不需要
		if (segNo != '00126') {
			if (isAutoAllocLocation()) {
				var currentVeicleNo = localStorage.getItem("putin_vehicle_no") == null ? "" : localStorage.getItem(
					"putin_vehicle_no");
				if (!currentVeicleNo) {
					mui.alert("先选择车牌号", "提示", "确定", function() {}, "div");
					$("#pack_id").val("");
					$("#pack_id").focus();
					return false;
				}
			}
		}

		//add by xuhuaijun 20161229 格式化捆包号
		var pack_id = $("#pack_id").val();
		// add by liuxu 佛山宝钢智慧仓库捆包上传后，需自动跳转到卸货确认页面且根据上传捆包号查询入库信息
		pack_id_lx = pack_id;
		if (pack_id == "") {
			mui.alert("捆包号不能为空", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		//add by tangli 20191227 湛江现货增加库位校验不能为空
		if (segNo == '00166') {
			if ($("#pack_location").val() == "") {
				mui.alert("请扫描或者输入库位", "提示", "确定", null, "div");
				$("#pack_location")[0].focus();
				return false;
			}
		}
		//end by tangli 20191227 湛江现货增加库位校验不能为空

		//modify by wangshengbo 20171011 非股份钢卷标签也带杠，暂时先屏蔽掉后台去杠处理
		//pack_id = formatPackId(pack_id);
		//$("#pack_id").val(pack_id);
		//add by xuhuaijun 20180925 武钢拼焊
		pack_id = trimPackId(pack_id);
		//console.log($('#pack_id').val()+","+JSON.stringify(putinPackList))
		//校验捆包号是否重复
		if (selectById(pack_id, putinPackList)) {
			mui.alert("不能扫描重复的捆包号", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		//获取捆包信息后隐藏软键盘
		document.activeElement.blur();
		//输入或扫描捆包号之后，调用捆包查询接口
		if (segNo == "00127") {
			console.log(pack_id.length)
			if (pack_id.length < 50) {
				initData();
			} else {
				//TODO 注释
				// parseQrCode(pack_id);	
			}
		} else if (segNo == "00126") {
			if (pack_id.length < 50) {
				initData();
			} else {
				parseQrCode(pack_id);
			}
		} else {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					//queryPackNetNotConnect();
					plus.nativeUI.toast("无网络连接，请检查", {
						'verticalAlign': 'center'
					});
				} else {
					initData();
				}
			});
		}
	}
});

/**
 * 修改 type :未找到扫描捆包明细type=1;从已扫描列表进入扫描页面 type=2
 */
mui(document.body).on('tap', '#edit', function() {
	var pack_qty = $("#qty").val();
	/*if(pack_qty=="undefined" || pack_qty=="" || pack_qty=="" ||pack_qty=="0" ){
		pack_qty="1";
	}*/
	//判断除“司机姓名”、“质量描述”、“身份证号”三个属性之外的属性是否被修改
	var pack_id = $("#pack_id").val();
	var spec = $("#spec").val();
	var inner_diameter = $("#inner_diameter").val();
	var factory_product_id = $("#factory_product_id").val();
	var weight = $("#weight").val();
	var qty = $("#qty").val();
	var product_process_id = $("#product_process_id").val();
	judgeWhetherEdit(pack_id, spec, inner_diameter, factory_product_id, weight, qty);
	if (type == "1") {
		var pack_id = $("#pack_id").val();
		//console.log(JSON.stringify(putinPackList));
		if (!selectById(pack_id, putinPackList)) {
			mui.alert("没有找到可以修改的捆包,请检查捆包号", "提 示", "确定", function() {}, 'div');
			return false;
		}
		var tempWeight = $("#weight").val();
		if (tempWeight == null || tempWeight == "") {
			mui.alert("请输入重量", "提示", "确认", function() {}, "div");
			return false;
		}
		var quality_desc = $("#quality_desc").val(); //质量描述不为空时
		if (quality_desc != null && quality_desc != "") {
			var driver_name = $("#driver_name").val(); //司机姓名
			var id_card = $("#id_card").val(); //身份证号
			if (driver_name == null || driver_name == "") { //司机姓名不能为空
				mui.alert("请输入司机姓名", "提示", "确认", function() {}, "div");
				return false;
			}
			if (id_card == null || id_card == "") { //身份证号不能为空
				mui.alert("请输入身份证号", "提示", "确认", function() {}, "div");
				return false;
			}
		}
		var index = getIndexById(pack_id, putinPackList);
		putinPackList[index].location_desc = $("#pack_location").val();
		putinPackList[index].spec = $("#spec").val();
		putinPackList[index].factory_product_id = $("#factory_product_id").val();
		putinPackList[index].putin_weight = $("#weight").val();
		putinPackList[index].putin_qty = pack_qty;
		putinPackList[index].edit_flag = global_edit_flag; //PDA修改标记
		putinPackList[index].quality_desc = $("#quality_desc").val(); //质量描述
		putinPackList[index].driver_name = $("#driver_name").val(); //司机姓名
		putinPackList[index].id_card = $("#id_card").val(); //身份证号
		console.log("121211111111111111:" + JSON.stringify(pack_info));
		reSum(putinPackList, pack_id);
		//add by wanshengbo 171211智慧仓库 
		//点状库位，清空上一个捆包的库位
		clearPreviousLocation();
		setDefaultInputStyle();
	} else if (type == "2") {
		if (vaildate()) {
			var pack_info = new Object();
			pack_info.pack_id = $("#pack_id").val();
			pack_info.location_desc = $("#pack_location").val();
			pack_info.spec = $("#spec").val();
			pack_info.factory_product_id = $("#factory_product_id").val();
			pack_info.putin_weight = $("#weight").val();
			pack_info.putin_qty = pack_qty;
			pack_info.product_id = ""; //			
			//花都如果毛重为空，把净重赋值给毛重
			if (segNo == '00118' || segNo == '00152') {
				pack_info.gross_weight = $("#weight").val();
			} else {
				pack_info.gross_weight = ""; //
			}
			pack_info.putin_voucher_num = ""; //
			pack_info.putin_type = ""; //
			pack_info.product_type_id = ""; //
			pack_info.shopsign = ""; //
			pack_info.product_process_id = $("#product_process_id").val();
			pack_info.inner_diameter = $("#inner_diameter").val();
			pack_info.edit_flag = global_edit_flag; //PDA修改标记
			pack_info.quality_desc = $("#quality_desc").val(); //质量描述
			pack_info.driver_name = $("#driver_name").val(); //司机姓名
			pack_info.id_card = $("#id_card").val(); //身份证号
			//pack_info.ref_width = "";//
			//未找到资源的捆包手工指定库位 add by wangshengbo171212 智慧仓库
			pack_info.auto_loc_pack = "0" //库位手工输入标记
			if (isAutoAllocLocation()) {
				pack_info.loc_view_id = getInputLocViewId($("#pack_location").val());
			}
			console.log("121211111111111111:" + JSON.stringify(pack_info));
			putinPackList.push(pack_info);
			// console.log(pack_info+","+JSON.stringify(putinPackList));
			//refreshSum(pack_info);
			reSum(putinPackList, $("#pack_id").val());
			//add by wanshengbo 171211智慧仓库 
			//点状库位，清空上一个捆包的库位
			clearPreviousLocation();
			setDefaultInputStyle();
		}
	}

	global_edit_flag = null;
});

function vaildate() {
	if (!isAutoAllocLocation()) {
		var location_desc = $("#pack_location").val();
		if (location_desc == null || location_desc == "") {
			mui.alert("请输入库位", "提示", "确定", function() {}, "div");
			$("#pack_location").focus();
			return false;
		}
	}
	var pack_id = $("#pack_id").val();
	if (pack_id == null || pack_id == "") {
		mui.alert("请输入捆包号", "提示", "确定", function() {}, "div");
		$("#pack_id").focus();
		return false;
	}
	var tempWeight = $("#weight").val();
	if (tempWeight == null || tempWeight == "") {
		mui.alert("请输入重量", "提示", "确定", function() {}, "div");
		return false;
	};
	var quality_desc = $("#quality_desc").val(); //质量描述不为空时
	if (quality_desc != null && quality_desc != "") {
		var driver_name = $("#driver_name").val(); //司机姓名
		var id_card = $("#id_card").val(); //身份证号
		if (driver_name == null || driver_name == "") { //司机姓名不能为空
			mui.alert("请输入司机姓名", "提示", "确认", function() {}, "div");
			return false;
		};
		if (id_card == null || id_card == "") { //身份证号不能为空
			mui.alert("请输入身份证号", "提示", "确认", function() {}, "div");
			return false;
		};
	};
	return true;
}

/**
 * 入库
 */
mui(document.body).on('tap', '#putin', function() {

	//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
	mui("#putin").button('loading');
	$("#overlay").addClass("overlay");
	//扫描判断入库仓库和厂区仓库是否一致，不对应则提示
	console.log(segNo + "," + mean_name + "。")
	var putin_flag = "0";
	if (segNo == "00138" && mean_name == "load_menus") {
		var tempParam = localStorage.getItem("depotHasChanged");
		//判断用户是否对仓库信息进行修改
		if (tempParam == "depotHasChanged") {
			console.log("**********")
			localStorage.setItem("depotHasChanged", "");
			console.log("***********");
			if (!provideIsNot(wprovider_id, factory_wprovider)) {
				if (confirm("入库仓库" + wprovider_id + "和厂区对应仓库" + factory_wprovider + "不一致，确定要入库吗？")) {
					putin_flag = "1";
				} else {
					putin_flag = "2";
				}
			} else {
				putin_flag = "0";
			}
		}
	}
	console.log(segNo + "," + mean_name + "。" + putin_flag);
	if (putin_flag == "1" || putin_flag == "0") {

		//调用入库接口
		if (putinPackList.length == 0) {
			//mui.alert("已扫描捆包列表无记录"," ","确定",function(){},'div');
			plus.nativeUI.toast("已扫描列表无捆包记录。请检查");
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			mui("#putin").button('reset');
			$("#overlay").removeClass("overlay");
			return false;
		} else {
			if ("00112" == segNo) {
				//add by yangzemin 先上传长春后货捆包，后进行入库
				var changchunPackList = new Array();
				$.each(putinPackList, function(i, value) {
					if (value.pack_type == 'changchun') {
						changchunPackList.push(value);
					}
				});

				console.log("长春转库的捆包列表：changchunPackList = " + JSON.stringify(changchunPackList));

				if (changchunPackList.length == 0) {
					// add xsp ERP_54958
					//上传捆包是否已上传入库中间表，如果已经上传中间表，则提示是否继续上传提示
					toPutinPackQuery();
				} else {
					var reallocParams = {};
					$.each(changchunPackList, function(i, item) {
						reallocParams.pack_id = item.pack_id;
						reallocParams.product_id = item.product_id;
						reallocParams.pack_location = item.new_location_desc; //传入参数
						reallocParams.old_pack_locaiton = item.location_desc
						reallocParams.loc_view_id = item.loc_view_id;
					});

					exeReallocLocation(changchunPackList);
				}
			} else {
				toPutinPackInfo();
			}
		}
	} else if (putin_flag == "2") {
		mui("#putin").button('reset');
		$("#overlay").removeClass("overlay");
		return false;
	}

});

//查看入库捆包清单
mui(document.body).on('tap', '#detail', function() {
	mui.openWindow({
		url: 'putin_pack_list.html',
		id: 'putin_pack_list',
		createNew: true,
		extras: {
			putinPackList: putinPackList,
			vehicle_id: vehicle_id,
			transfer_flag: 0
		}
	});
});
//绑定入库清单信息回写事件
window.addEventListener('back', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	//重算合计信息
	reSum(putinPackList, '');
	//从详情直接返回扫描页面
	setDefaultInputStyle();
	clearPreviousLocation(); //清空上一个捆包的库位
});

//绑定入库清单信息修改事件
window.addEventListener('edit', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	reSum(putinPackList, '');
	var edit_pack = e.detail.edit_pack;
	type = e.detail.type;
	console.log("type:" + type);

	//在捆包明细列表右滑选择删除之后，向当前页面全局变量添加捆包信息，便于之后修改标记的判断
	global_factory_procudt_id = edit_pack.factory_product_id;
	global_inner_diameter = edit_pack.inner_diameter;
	global_product_process_id = edit_pack.product_process_id;
	global_pack_id = edit_pack.pack_id;
	global_qty = edit_pack.putin_qty;
	global_spec = edit_pack.spec;
	global_weight = edit_pack.putin_weight;
	refreshPackInfo(edit_pack);
	//clearPreviousLocation();//清空上一个捆包的库位
});

function refreshSum(pack_info) {
	//最近捆包
	$("#recent_pack_id").text(pack_info.pack_id);
	//统计合计信息
	var qty = $("#sum_qty > #sum_number").text();
	if (parseInt(qty) < 1) {
		qty = parseInt(qty) + 1;
	} else {
		qty = parseInt(qty);
	}
	var weight = $("#sum_weight > #sum_number").text();
	weight = parseFloat(weight) + parseFloat(pack_info.putin_weight * 1000);
	//取3位小数
	weight = weight.toFixed(3);
	$("#sum_qty > #sum_number").text(qty);
	$("#sum_weight > #sum_number").text(weight);
	//clearInput();
}

function reSum(packlist, pack_id) {
	var recent_pack_id = $("#recent_pack_id").text();
	var recent = 0;
	var sum_qty = 0;
	var sum_weight = 0;
	var ppid = "";
	$.each(packlist, function(i, value) {
		if (recent_pack_id == value.pack_id) {
			recent = recent + 1;
		}
		sum_qty = sum_qty + 1;
		ppid = value.pack_id
		sum_weight = parseFloat(sum_weight) + parseFloat(value.putin_weight * 1000);
	});
	if (pack_id == "" || pack_id == "undefine" || pack_id == null) {
		if (ppid != "" && ppid != "undefine" && ppid != null) {
			$("#recent_pack_id").text(ppid);
		} else {
			$("#recent_pack_id").text("无");
		}
	} else {
		$("#recent_pack_id").text(pack_id);
	}

	//保留三位小数
	sum_weight = sum_weight.toFixed(3);
	$("#sum_qty > #sum_number").text(sum_qty);
	$("#sum_weight > #sum_number").text(sum_weight);

	//清空信息焦点定位到捆包
	clearInput();
}

/**
 * 
 * @param {Object} pack Json格式字符串
 */
function refreshPackInfo(pack) {
	//alert(pack.pack_id+","+pack.location_desc)
	$("#pack_location").val(pack.location_desc);
	$("#pack_id").val(pack.pack_id);
	$("#spec").val(pack.spec);
	$("#factory_product_id").val(pack.factory_product_id);
	$("#weight").val(pack.putin_weight);
	$("#qty").val(pack.putin_qty);
	$("#inner_diameter").val(pack.inner_diameter);
	$("#product_process_id").val(pack.product_process_id);
	$("#spec")[0].focus();
	//自动分配库位的捆包，不容许修改卷内径和库位
	/*if(pack.auto_loc_pack == '1'){
		setInputReadOnly("#pack_location",true);
	}else{
		setInputReadOnly("#pack_location",false);
	}*/
	if (isAutoAllocLocation() && pack.material_shape == 'J') {
		setInputReadOnly("#pack_location", true);
	} else {
		setInputReadOnly("#pack_location", false);
	}
	//setInputReadOnly("#inner_diameter",true);
}
/*
 * 查询入库捆包事件
 */
function initData() {
	//var outUri = domainName+"webService.jsp?callback=?";
	//add by penglei 湛江 20191014 ERP_56908
	var zjPackId = $("#pack_id").val();
	var params = '';
	if (segNo == "00166") {
		if (zjPackId.substring(0, 1) == "s" || zjPackId.substring(0, 1) == "S") {
			zjPackId = zjPackId.substring(1);
		}
		zjPackId = zjPackId.replace(/^\s+|\s+$/g, "");
		params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + zjPackId + '","wprovider_id":"' +
			wprovider_id + '","vehicle_no":"' + vehicle_id + '"}';
	} else if (segNo == "00127") {
		params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + $("#pack_id").val() +
			'","pda_type":"' + 1 + '","wprovider_id":"' + wprovider_id + '","vehicle_no":"' + vehicle_id + '"}';
	} else {
		params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","pack_id":"' + $("#pack_id").val() +
			'","wprovider_id":"' + wprovider_id + '","vehicle_no":"' + vehicle_id + '"}';
	}
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	console.log(params);
	var method = "exeDownLoadPutinPackInfo";
	handLocationFlag = "0";
	forLocationPack = {};
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("exeDownLoadPutinPackInfo返回：" + JSON.stringify(data));
			if (data != null) {
				if (data.product_id == "" || data.product_id == null) {
					//add by yangzemin 成都宝钢查询长春收货
					if (segNo == "00112") {
						queryChangChunPack();
					} else {
						mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】", "提示", "确定", function() {}, "div");
						//add by wangshengbo 20171211  智慧仓库：自动分配库位时，库位输入框默认为只读状态
						if (isAutoAllocLocation()) {
							//mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】","提示","确定",function() {}, "div");
							setInputReadOnly("#pack_location", true);
						}

						$("#spec")[0].focus();
						type = "2";
					}
					return false;
				} else {
					//console.log(JSON.stringify(data));
					forLocationPack = data; //modify by wangshengbo171211 定义成全局变量
					$("#pack_id").val(data.pack_id);
					$("#spec").val(data.spec);
					$("#factory_product_id").val(data.factory_product_id);
					$("#weight").val(data.putin_weight);
					$("#qty").val(data.putin_qty);
					$("#inner_diameter").val(data.inner_diameter);
					$("#product_process_id").val(data.product_process_id);
					material_shape = data.material_shape;
					/*modify by wangshengbo 171207 库位自动分配-智慧仓库
					 * auto_loc_flag != 1手工录入库位
					 * */

					if (!isAutoAllocLocation()) {
						//浙江宝井原料立体库，卷内径必填
						if(segNo == '00182'){
							var InnerDiameterRequired =	getSwitchValue(segNo, 'INNER_DIAMETER_REQURIED');
							if (InnerDiameterRequired == "1" && data.material_shape == 'J') {
								if (data.inner_diameter == 0) {
									plus.nativeUI.toast("请选择或输入卷内径", {
										'verticalAlign': 'center'
									});
									setInputReadOnly("#inner_diameter", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
									setInnerDiameterListHtml();
									return false;
								}
							}else{
								setLocationInfo($("#pack_location").val(), 0);
							}
						}else{
							setLocationInfo($("#pack_location").val(), 0);
						}
						
					} else {
						//add by liuxu 天津宝钢、上海宝井、佛山宝钢不需要首道工序
						if (segNo != '00106' && segNo != '00126' && segNo != '00137' && segNo != '00129') {
							if (data.product_process_id == "" || data.product_process_id == null || data.product_process_id == undefined) {
								plus.nativeUI.toast("请选择或输入首道加工工序", {
									'verticalAlign': 'center'
								});
								setInputReadOnly("#product_process_id", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
								setProductProcessListHtml();
								return false;
							}
						}
						if (data.material_shape == 'J') {
							if (data.inner_diameter == 0) {
								plus.nativeUI.toast("请选择或输入卷内径", {
									'verticalAlign': 'center'
								});
								setInputReadOnly("#inner_diameter", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
								setInnerDiameterListHtml();
								return false;
							}
						} else {
							setInputReadOnly("#pack_location", false);
							$("#pack_location").focus();
							return false;
						}

					}
					
					afterGetPackObj(); //当前扫描捆包添加到已扫描列表以及后续处理
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				console.log("exeDownLoadPutinPackInfo处理异常" + JSON.stringify(data));
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if (textStatus == "timeout") {
				if (isAutoAllocLocation()) {
					mui.alert("网络请求超时,请手工补充库位以及捆包信息后，点击【修改】", "提示", "确定", null, 'div');
					setInputReadOnly("#pack_location", false);
					$("#pack_location").focus();
				} else {
					queryPackNetNotConnect(); //请求超时PDA只记录捆包号和手工输入的库位
				}
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
}

var pack_location_target = "";
var packObj = {};
/**
 * add by yangzemin ERP_54960
 * 捆包数据添加pack_type：putin是入库的捆包，changchun是长春收货捆包
 * 查询长春收货捆包
 */
function queryChangChunPack() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","wprovider_id":"' + wprovider_id + '","pack_id":"' + $("#pack_id").val() +
		'"}';
	console.log("queryChangChunPack->params: " + JSON.stringify(params));
	var method = "exeQueryCcSynergyPackInfoAutomatchProductProcessId";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 5000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if (data != null) {
				console.log("queryChangChunPack->result: " + JSON.stringify(data));
				if (data.out_result == 0 || data.packList.size == 0) {
					//长春收货没有找到捆包，当入库捆包处理

					mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】", "提示", "确定", function() {}, "div");
					//add by wangshengbo 20171211  智慧仓库：自动分配库位时，库位输入框默认为只读状态
					if (isAutoAllocLocation()) {
						//mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】","提示","确定",function() {}, "div");
						setInputReadOnly("#pack_location", true);
					}

					$("#spec")[0].focus();
					type = "2";
					return false;

				} else {
					console.log("queryChangChunPack->packList" + JSON.stringify(data));
					if (pack_location_target == null || pack_location_target == "") {
						$.each(data.packList, function(i, item) {
							//$("#pack_location_m").val(item.location_desc);
							packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								factory_product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: '待定',
								pack_location: $("#pack_location").val(),
								loc_view_id: item.loc_view_id,
								product_process_id: item.product_process_id,
								spec: item.spec,
								putin_weight: item.putin_weight,
								weight: item.putin_weight,
								pack_type: "changchun"
							};
							console.log(JSON.stringify(packObj));
							if (item.pack_status != "10") {
								var status_desc = "";
								if (item.pack_status == "20") {
									status_desc = "出库";
								} else if (item.pack_status == "30") {
									status_desc = "封锁";
								} else {
									status_desc = "转库";
								}
								mui.confirm('捆包为' + status_desc + '状态，不是自由在库。卸货确认，无法联动长春做转库，是否继续？', '提示', ['确认', '取消'], function(e) {
									if (e.index == 0) {
										if (packObj.product_process_id == "N") {
											setProductProcessListHtml();
											packObj.product_process_id = $("#product_process_id").val();
										} else {
											$("#product_process_id").val(packObj.product_process_id);
											putinPackList.push(packObj);
											refreshSum(packObj);
											packObj = {};
										}
									} else {
										packObj = {};
										$("#pack_id").val("");
										$("#pack_id").focus();
									}
								}, 'div');
							} else {
								if (packObj.product_process_id == "N") {
									setProductProcessListHtml();
									packObj.product_process_id = $("#product_process_id").val();
								} else {
									$("#product_process_id").val(packObj.product_process_id);
									putinPackList.push(packObj);
									refreshSum(packObj);
									packObj = {};
								}
								// 重制页面属性
								reSum(putinPackList, $("#pack_id").val());
								console.log("扫描捆包时获取的putinPackList" + JSON.stringify(putinPackList));
							}
							$("#pack_id").val("");
							$("#pack_id").focus();
						});
					} else {

						$.each(data.packList, function(i, item) {
							packObj = {
								pack_id: item.pack_id,
								product_id: item.product_id,
								factory_product_id: item.product_id,
								scan_time: getnowtime(),
								new_location_desc: pack_location_target,
								location_desc: item.location_desc,
								pack_location: $("#pack_location").val(),
								loc_view_id: item.loc_view_id,
								product_process_id: item.product_process_id,
								spec: item.spec,
								putin_weight: item.putin_weight,
								weight: item.putin_weight,
								pack_type: "changchun"
							};
							putinPackList.push(packObj);
							refreshSum(packObj);
							packObj = {};
						});
					}
				}
			} else {
				//连接失败
				// var btnArray = ['确认', '取消'];
				// mui.confirm('未找到捆包信息，请联系长春宝钢入库', '提示', btnArray, function(e) {
				// 	if (e.index == 0) {
				// 		$("#pack_id").val("");
				// 		$("#pack_id").focus();
				// 		return;
				// 	} else {
				// 		//$("#pack_id").val("");
				// 		$("#pack_id").focus();
				// 		return;
				// 	}
				// }, 'div');
				mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】", "提示", "确定", function() {}, "div");
				//add by wangshengbo 20171211  智慧仓库：自动分配库位时，库位输入框默认为只读状态
				if (isAutoAllocLocation()) {
					//mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】","提示","确定",function() {}, "div");
					setInputReadOnly("#pack_location", true);
				}

				$("#spec")[0].focus();
				type = "2";
				return false;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if (textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
};


/*
 * 一汽宝友：二维码解析后查询捆包
 */
function queryPackForQrCode(params, factoryId, replaceFactoryId) {
	var zjPackId = $("#pack_id").val();

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	console.log("queryPackForQrCode param=" + params);
	var method = "exeDownLoadPutinPackInfo";
	handLocationFlag = "0";
	forLocationPack = {};
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log(JSON.stringify(data));
			if (data != null) {
				if (data.product_id == "" || data.product_id == null) {
					//没查到对应的捆包信息，如果replaceFactoryId不为空，替换钢厂资源号后重试 ERP_63318
					if (replaceFactoryId == "") {
						mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】", "提示", "确定", function() {}, "div");
						//add by wangshengbo 20171211  智慧仓库：自动分配库位时，库位输入框默认为只读状态
						if (isAutoAllocLocation()) {
							//mui.alert("未找到待入库信息，请手工补充捆包信息后，点击【修改】","提示","确定",function() {}, "div");
							setInputReadOnly("#pack_location", true);
						}

						$("#spec")[0].focus();
						type = "2";
					} else {
						var srcStr = '"factory_product_id":"' + factoryId + '"';
						var toStr = '"factory_product_id":"' + replaceFactoryId + '"';
						var replaceParam = params.replace(srcStr, toStr);

						console.log("替换的String：" + srcStr);
						console.log("替换成String：" + toStr);
						console.log("替换前：" + params);
						console.log("替换后：" + replaceParam);
						queryPackForQrCode(replaceParam, replaceFactoryId, "");
					}

					return;
				} else {
					//console.log(JSON.stringify(data));
					forLocationPack = data; //modify by wangshengbo171211 定义成全局变量
					$("#pack_id").val(data.pack_id);
					$("#spec").val(data.spec);
					$("#factory_product_id").val(data.factory_product_id);
					$("#weight").val(data.putin_weight);
					$("#qty").val(data.putin_qty);
					$("#inner_diameter").val(data.inner_diameter);
					$("#product_process_id").val(data.product_process_id);
					material_shape = data.material_shape;
					/*modify by wangshengbo 171207 库位自动分配-智慧仓库
					 * auto_loc_flag != 1手工录入库位
					 * */

					if (!isAutoAllocLocation()) {
						setLocationInfo($("#pack_location").val(), 0);
					} else {
						//add by liuxu 天津宝钢、上海宝井、佛山宝钢不需要首道工序
						if (segNo != '00106' && segNo != '00126' && segNo != '00137' && segNo != '00129') {
							if (data.product_process_id == "" || data.product_process_id == null || data.product_process_id == undefined) {
								plus.nativeUI.toast("请选择或输入首道加工工序", {
									'verticalAlign': 'center'
								});
								setInputReadOnly("#product_process_id", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
								setProductProcessListHtml();
								return false;
							}
						}
						if (data.material_shape == 'J') {
							if (data.inner_diameter == 0) {
								plus.nativeUI.toast("请选择或输入卷内径", {
									'verticalAlign': 'center'
								});
								setInputReadOnly("#inner_diameter", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
								setInnerDiameterListHtml();
								return false;
							}
						} else {
							setInputReadOnly("#pack_location", false);
							$("#pack_location").focus();
							return false;
						}
					}
					mui.confirm('扫描成功！', '提示', ['确认'], function(e) {
						if (e.index == 0) {
							// afterGetPackObj();
						}
					}, 'div');
					afterGetPackObj(); //当前扫描捆包添加到已扫描列表以及后续处理
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				console.log("exeDownLoadPutinPackInfo处理异常" + JSON.stringify(data));
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if (textStatus == "timeout") {
				if (isAutoAllocLocation()) {
					mui.alert("网络请求超时,请手工补充库位以及捆包信息后，点击【修改】", "提示", "确定", null, 'div');
					setInputReadOnly("#pack_location", false);
					$("#pack_location").focus();
				} else {
					queryPackNetNotConnect(); //请求超时PDA只记录捆包号和手工输入的库位
				}
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
}
//上传捆包
function toPutinPackInfo() {
	//var outUri = domainName+"webService.jsp?callback=?";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	if (vehicle_id == null) {
		vehicle_id = "";
	}

	var params = "";
	//add by liuxu 佛山宝钢上传参数加上装卸点和库门
	if (segNo == '00126') {
		if (factory_area_id == null) {
			factory_area_id = "F1";
		}

		if (hand_point_id == null) {
			hand_point_id = "ZXDH1800001";
		}
		params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","wprovider_id":"' + wprovider_id +
			'","vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","hand_point_id":"' + hand_point_id +
			'","factory_area_id":"' + factory_area_id + '","pack_list":' + JSON.stringify(putinPackList) +
			'}';
	} else {
		params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","wprovider_id":"' + wprovider_id +
			'","vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","pack_list":' + JSON.stringify(putinPackList) +
			'}';
	}
	console.log("上传入库捆包：" + params);

	params = encodeURI(params, 'utf-8');
	var method = "exeUploadPutinPackInfo";
	var outtime = 300000; //默认超时未10秒 超过100条记录设置为30000
	if (putinPackList.length > 100 && putinPackList.length <= 200) {
		outtime = 900000; //30000
	} else if (putinPackList.length > 200) {
		outtime = 1800000; //60000
	}

	$.ajax({
		type: "post",
		async: true,
		timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			console.log(JSON.stringify(result));
			if (null != result) {
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");

				console.log(JSON.stringify(result));
				mui.alert(result.resultDesc, "提示", "确定", function() {
					//入库成功，提示是否跳转捆包打印页面 ERP_54959
					if (result.resultStatus == 1) {
						// toPutinPackInfo1(putinPackList1);
						//yangzemin 
						onPackUploadSuccess(result.seq, putinPackList1);
						// add by liuxu 佛山宝钢入库成功后，自动跳转卸货确认页面
						// if (segNo == "00126") {
						// 	// var pack_id_h ="";
						// 	// $.each(putinPackList, function(i, item) {
						// 	// 	pack_id_h = item.pack_id;
						// 	// });
						// 	//alert(pack_id_lx);
						// 	mui.openWindow({
						// 		url: '../unload/unload_pack.html',
						// 		id: 'unload',
						// 		createNew: true,
						// 		extras: {
						// 			pack_id: pack_id_lx,
						// 		}
						// 	});
						// }
					}
					/*if (mean_name == "load_menus") {
						if (segNo == "00138") {

						} else {
							mui.openWindow({
								url: 'hand_point_end.html',
								id: 'hand_point_end',
								createNew: true
							});
						}
					}*/
				}, "div");
				var putinPackList1 = new Array();
				putinPackList1 = putinPackList;
				putinPackList = [];

				console.log(putinPackList.length);
				$("#recent_pack_id").html("无");
				$("#sum_qty > #sum_number").text(0);
				$("#sum_weight > #sum_number").text(0);
				//清空信息焦点定位到捆包
				clearInput();
				//如果seg_no 00106  库区是 1J01 武汉宝钢原料库，那么库位清空且光标放在库位上
				/*console.log(segNo=='00106'+"ooooooooooooooooo"+wprovider_id=='1J01'+"pppppppppppppppppppp");
				if(segNo=='00106'&&wprovider_id=='1J01'){
				$("#pack_location").val("");
				$("#pack_location")[0].focus();
			    }else{
					$("#pack_id")[0].focus();
				}*/
				if (!isAutoAllocLocation()) {
					setInputReadOnly("#pack_location", false);
				} else {
					setInputReadOnly("#pack_location", true);
				}

				$("#pack_id")[0].focus();
				if (segNo != '00129' && segNo != '00145') {
					localStorage.setItem("putin_vehicle_no", "");
					$("#vehicle_no_span").html("车牌：");
				}
				/*
				if(location_type == 2 ){
					$("#pack_location").val("");
					$("#pack_location")[0].focus();
				}else{
					$("#pack_id")[0].focus();
				}*/
				//add by tangli 20191203 湛江现货入库查询账号对应外库库区
				if (segNo == '00166' && pack_location_zj != "") {
					$("#pack_location").val(pack_location_zj);
				}

			} else { //连接失败
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if (textStatus == "timeout") {
					//console.log("upload,textStatus>>>>>>>>>>>>>>>>>>"+textStatus);
					XMLHttpRequest.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}


//add by yangzemin 成都宝钢上传捆包
function doChengduPutinPackInfo(chengduPackList) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	if (vehicle_id == null) {
		vehicle_id = "";
	}

	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","wprovider_id":"' + wprovider_id +
		'","vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","pack_list":' + JSON.stringify(chengduPackList) +
		'}';
	console.log("成都上传入库捆包：" + params);

	params = encodeURI(params, 'utf-8');
	var method = "exeUploadPutinPackInfo";
	var outtime = 30000; //默认超时未10秒 超过100条记录设置为30000
	if (chengduPackList.length > 100 && chengduPackList.length <= 200) {
		outtime = 90000; //30000
	} else if (chengduPackList.length > 200) {
		outtime = 180000; //60000
	}

	$.ajax({
		type: "post",
		async: true,
		timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if (null != result) {
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");

				console.log("成都捆包" + JSON.stringify(result));
				mui.alert(result.resultDesc, "提示", "确定", function() {
					//入库成功，提示是否跳转捆包打印页面 ERP_54959
					if (result.resultStatus == 1) {
						toPutinPackInfo1(chengduPackList);
					}
					if (mean_name == "load_menus") {
						if (segNo == "00138") {

						} else {
							mui.openWindow({
								url: 'hand_point_end.html',
								id: 'hand_point_end',
								createNew: true
							});
						}
					}

				}, "div");
				var putinPackList1 = new Array();
				putinPackList1 = putinPackList;
				putinPackList = [];
				chengduPackList = new Array();

				console.log(putinPackList.length);
				$("#recent_pack_id").html("无");
				$("#sum_qty > #sum_number").text(0);
				$("#sum_weight > #sum_number").text(0);
				//清空信息焦点定位到捆包
				clearInput();

				if (!isAutoAllocLocation()) {
					setInputReadOnly("#pack_location", false);
				} else {
					setInputReadOnly("#pack_location", true);
				}

				$("#pack_id")[0].focus();
				localStorage.setItem("putin_vehicle_no", "");
				$("#vehicle_no_span").html("车牌：");

				//add by tangli 20191203 湛江现货入库查询账号对应外库库区
				if (segNo == '00166' && pack_location_zj != "") {
					$("#pack_location").val(pack_location_zj);
				}

			} else { //连接失败
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if (textStatus == "timeout") {
					//console.log("upload,textStatus>>>>>>>>>>>>>>>>>>"+textStatus);
					XMLHttpRequest.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}

/**
 * yangzemin 20210916 捆包上传成功增加pda远程打印报表逻辑
 * @param {Object} seqNo 处理批次号
 */
function onPackUploadSuccess(seqNo, packList) {
	var pdaPrint = getSwitchValue(segNo, 'PDA_PRINT_SERVER_PUTIN');
	console.log("入库pda自助打印开关：" + pdaPrint);
	if (pdaPrint == '1') {
		if (!seqNo) {
			mui.alert("未获取到入库批次号", "提示", "确定", function() {}, "div");
			return;
		}
		doPutinLabelPrint(seqNo);
	} else {
		toPutinPackInfo1(packList);
	}
}

//add 20200509 xsp 是否跳转捆包打印页面 ERP_54959
function toPutinPackInfo1(putinPackList1) {
	//捆包列表
	if (putinPackList1.length != 0) {
		//查询开关值
		flag = getSwitchValue(segNo, "IF_SCAN_STORAGE_PRINTING");

		//判断当前账套是否存在扫描入库打印开关
		if (flag == 1) {
			//显示扫描入库打印开关
			//成都宝钢 -xsp - 20200507 入库成功后提示是否跳转捆包打印页面，并把捆包信息传过去
			//localStorage.setItem("DaYin_putinPackList", putinPackList);
			// mui.alert("是否跳转捆包打印页面", "提示", "确认", function() {
			// 	mui.openWindow({
			// 		url: '../print/bluetooth.html',
			// 		id: "bluetooth",
			// 		createNew: true,
			// 		extras: {
			// 			putinPackList: putinPackList1,
			// 		}
			// 	});
			// }, "div");
			var btnArray = ['否', '是'];
			mui.confirm('是否跳转捆包打印页面？', '提示', btnArray, function(e) {
				if (e.index == 1) {
					mui.openWindow({
						url: '../print/bluetooth.html',
						id: "bluetooth",
						createNew: true,
						extras: {
							putinPackList: putinPackList1,
						}
					});
				}
			});
		}
	}
}
//add xiesenpeng 20200515 查询上传捆包是否已存在于入库中间表
function toPutinPackQuery() {

	//add by yangzemin 成都宝钢ERP_54960
	var chengduPutinPackList = new Array();
	$.each(putinPackList, function(i, value) {
		if (value.pack_type == 'putin') {
			chengduPutinPackList.push(value);
		}
	});
	//TODO 
	if (chengduPutinPackList.length < 1) {
		//TODO 没有待入���的捆包，调用长春收货
		return;
	}

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","pack_list":' + JSON.stringify(chengduPutinPackList) + '}';
	console.log("params：" + params);
	var method = "exeUploadPutinPackQuery";

	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if (null != result) {
				console.log(JSON.stringify(result));
				if (result.resultStatus == 0) {
					doChengduPutinPackInfo(chengduPutinPackList);
				} else {
					mui.confirm(result.resultDesc + "是否继续上传？", '警告', ['确认', '取消'], function(e) {
						if (e.index == 0) {
							//add xiesenpeng 20200515 查询上传捆包已存在于入库中间表,确定时，
							//先删除入库中间表中已存在的相同数据，重新把现在的捆包添加到入库中间表 00112
							toPutinPackDelete(result.PackList, chengduPutinPackList);
						} else {
							return false;
						}
					}, 'div');
				}
			} else { //连接失败
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(JSON.stringify(result));
			mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
		}
	});
}
//add xiesenpeng 20200515 查询上传捆包已存在于入库中间表,继续上传，先把原来的捆包数据撤销，在新增数据
function toPutinPackDelete(PackList, chengduPutinList) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","pack_list":' + JSON.stringify(PackList) + '}';
	console.log("exeUploadPutinPackDelete->params：" + params);
	var method = "exeUploadPutinPackDelete";

	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if (null != result) {
				console.log(JSON.stringify(result));
				doChengduPutinPackInfo(chengduPutinList);
			} else { //连接失败
				mui.alert("连接服务器异常", "提示", "���定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(JSON.stringify(result));
			mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
		}
	});
}

/**
 * 长春���货协同 
 * 1.先查询长春宝钢捆包数据，对数据分别落地协同接口表和pda上传接口表 
 * 2.对长春捆包进行捆包入库
 * 3.调用接口对长春捆包进行自动转库入库和转库出库
 */
function exeReallocLocation(changchunPackList) {
	mui("#storage_realloc").button("loading");
	$("#overlay").addClass("overlay");
	var seg_no = localStorage.getItem("segNo"); //采用localStorage存储数据
	var user_id = localStorage.getItem("account"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var pack_location = "";
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","scanPackList":' + JSON.stringify(
			changchunPackList) + ',"vehicle_no":"' + selectVehicleNo + '","team_id":"' + team_id + '","wprovider_id":"' +
		wprovider_id + '"}';
	console.log("长春收货协同 params:" + JSON.stringify(params));
	params = encodeURI(params, 'utf-8');
	var method = "exeSynCDReturnPutinInfoToCC";
	var outtime = 10000; //默认超时未10秒 超过100条记录设置为30000
	if (changchunPackList.length > 100 && changchunPackList.length <= 200) {
		outtime = 15000; //30000
	} else if (changchunPackList.length > 200) {
		outtime = 20000; //60000
	}
	$.ajax({
		type: "post",
		async: true,
		timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("data:" + JSON.stringify(data));
			if (data != null) {
				if ("1" == data.out_result) {
					//长春收货成功
					var chengduPutinPackList = new Array();
					$.each(putinPackList, function(i, value) {
						if (value.pack_type == 'putin') {
							chengduPutinPackList.push(value);
						}
					});
					//是否有待入库捆包
					if (chengduPutinPackList.length < 1) {
						//TODO 没有待入库的捆包
						mui.alert(data.out_result_desc, "提示", "确定", function() {
							mui("#putin").button('reset');
							$("#overlay").removeClass("overlay");
						}, "div");

						changchunPackList = [];
						putinPackList = [];

						$("#recent_pack_id").html("无");
						$("#sum_qty > #sum_number").text(0);
						$("#sum_weight > #sum_number").text(0);
						//清空信息焦点定位到捆包
						clearInput();

						if (!isAutoAllocLocation()) {
							setInputReadOnly("#pack_location", false);
						} else {
							setInputReadOnly("#pack_location", true);
						}

						$("#pack_id")[0].focus();
						localStorage.setItem("putin_vehicle_no", "");
						$("#vehicle_no_span").html("车牌：");

						return;
					} else {
						//调用入库
						toPutinPackQuery();
					}
					// mui.alert(data.out_result_desc, "提示", "确定", function() {
					// 	mui("#storage_realloc").button('reset');
					// }, "div");

					// changchunPackList = [];
					// clearData();
					// $("#recent_pack_id").html("无");
					// $("#sum_qty > #sum_number").text(0);
					// $("#sum_weight > #sum_number").text(0);
					// localStorage.setItem("putin_vehicle_no", "");
					// $("#vehicle_no_span").html("车牌：");
					// $("#overlay").removeClass("overlay");
					return true;
				} else {
					mui.alert(data.out_result_desc, "提示", "确定", function() {
						mui("#storage_realloc").button('reset');
					}, "div");
					$("#overlay").removeClass("overlay");
					return false;
				}
			} else {
				mui.alert("连接失败", "提示", "确定", function() {
					mui("#storage_realloc").button('reset');
				}, "div");
				$("#overlay").removeClass("overlay");
				return false;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {

			this; //调用本次ajax请求时传递的options参数*/
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if (textStatus == "timeout") {
					XMLHttpRequest.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				mui("#storage_realloc").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}


function clearInput() {
	//清空信息焦点定位到捆包

	$("#spec").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#quality_desc").val("");
	$("#driver_name").val("");
	$("#id_card").val("");
	//add by tangli 20191126 ERP_58339 湛江物流入库扫描焦点定义到捆包上
	//天津宝钢和天津宝井，扫描捆包之后焦点在捆包上 20210714 yangzemin
	if (segNo == "00166" || segNo == "00129" || segNo == "00145") {
		$("#pack_id")[0].focus();
	} else {
		$("#pack_id").val("");
		$("#pack_location")[0].focus();
	}
	//end by tangli 20191126 ERP_58339 湛江物流入库扫描焦点定义到捆包上
	$("#inner_diameter").val("");
	$("#product_process_id").val("");
}

//判断厂区仓库代码和捆包仓库代码是否匹配
function provideIsNot(wprovider_id, factory_wprovider) {
	if (wprovider_id == factory_wprovider) {
		console.log(wprovider_id + "," + factory_wprovider);
		return true;
	} else {
		console.log(wprovider_id + "," + factory_wprovider);
		return false;
	}
}

//无网络连接时，只记录扫描捆包号
function queryPackNetNotConnect() {
	var pack_id = $("#pack_id").val();
	if (pack_id != "") {
		var packObj = {};
		packObj.location_desc = $("#pack_location").val();
		packObj.pack_id = $("#pack_id").val();
		packObj.factory_product_id = "";
		packObj.putin_weight = "";
		packObj.gross_weight = "";
		packObj.putin_qty = "1";
		packObj.product_id = "";
		packObj.putin_type = "";
		packObj.putin_voucher_num = "";
		packObj.product_type_id = "";
		packObj.shopsign = "";
		packObj.spec = "";
		packObj.putin_voucher_num = "";
		packObj.loc_view_id = "";
		packObj.pack_type = "putin";
		packObj.damage_type = "";
		//packObj.ref_width = "";
		putinPackList.push(packObj);
		//更新合计框内信息
		refreshSum(packObj);
		//清空信息焦点定位到捆包
		$("#pack_id").val("");
		$("#spec").val("");
		$("#factory_product_id").val("");
		$("#weight").val("");
		$("#qty").val("");
		//add by wangshengbo171211 智慧仓库
		clearPreviousLocation();
	}
}

//货损类型，显示货损类型选择
mui(document.body).on('tap', '#damage_type_picker_btn', function() {
	document.activeElement.blur();
	if (JSON.stringify(forLocationPack) == "{}") {
		//没有扫码的捆包
		mui.toast("请先扫描捆包");
		return;
	}
	showDamageTypePicker();
});

function showDamageTypePicker() {
	var isEmpty = (JSON.stringify(damageTypedata) == "{}");
	if (isEmpty) {
		queryDamageType(true);
		return;
	}
	if (!damageTypeListHtml) {
		loadDamageType(); //需要的时候才加载下拉框列表
	}
	$("#damageTypePickerDiv").toggleClass('show');
}

function loadDamageType() {
	for (var idx in damageTypedata) {
		damageTypeListHtml = damageTypeListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label class ="innerDiameterValue">' + damageTypedata[idx].code_desc + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#damageTypePickerList").html(damageTypeListHtml);
}

//货损类型列表选中事件
var damageTypeIndex = -1;
mui(document.body).on('selected', '#damageTypePickerInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var isEmpty = (JSON.stringify(damageTypedata) == "{}");
	if (isEmpty) {
		return;
	}
	var el_J = $(el); //dom元素转化成jquery对象
	var index = el_J.index();

	damageTypeIndex = index;
	console.log("当前点击了：" + damageTypedata[index].code_value);
});

//货损类型确定按钮
mui(document.body).on('tap', '#damageTypePickerConfirm', function() {
	if (damageTypeIndex == -1) {
		mui.alert("请选择货损类型", "提示", "确认", function() {}, "div");
		return false;
	} else {
		//将货损类型添加到最近捆包		
		$("#damageTypePickerDiv").toggleClass('show');
		console.log("当前捆包号：" + forLocationPack.pack_id);
		var packIndex = getIndexById(forLocationPack.pack_id, putinPackList);
		if (putinPackList.length > 0 && putinPackList.length >= packIndex) {
			putinPackList[packIndex].damage_type = damageTypedata[damageTypeIndex].code_value;
			mui.alert("捆包：" + forLocationPack.pack_id + "添加货损类型为：" + damageTypedata[damageTypeIndex].code_desc,
				"提示", "确定",
				function() {}, "div");
		} else {
			mui.alert("货损类型添加失败，请重新扫描捆包", "提示", "确定", function() {}, "div");
		}
	}
});

//货损类型选择隐藏
mui(document.body).on('tap', '#damageTypePickerCancel', function() {
	damageTypeIndex = -1;
	$("#damageTypePickerDiv").toggleClass('show');
});
/**
 * 获取货损类型数据
 */
function queryDamageType(showDialog) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '"}';

	var method = "exeQueryPutinDamageType";
	console.log(method + "->params：" + params);

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 30000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if (null != result) {
				console.log(method + "->result：" + JSON.stringify(result));
				if (result.resultStatus == 1) {
					//获取成功
					damageTypedata = result.resultData;
					//					arr5.sort(function(a,b){
					//						return a.id - b.id
					//					});
					console.log("damageTypedata = " + JSON.stringify(damageTypedata));
					if (showDialog) {
						showDamageTypePicker();
					}
				}
			} else { //连接失败
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			console.log(JSON.stringify(result));
			mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
		}
	});
}

//智慧仓库:显示卷内径列表
mui(document.body).on('tap', '#innerDiameterQueryBtn', function() {
	document.activeElement.blur();
	setInnerDiameterListHtml();
});

//智慧仓库:
function setInnerDiameterListHtml() {
	var innerDiaObject = $("#inner_diameter");
	if (innerDiaObject.attr("readOnly") != "readonly") {
		if (!innerDiameterListHtml) {
			loadInnerDiameter(); //需要的时候才加载卷内径下拉框列表
		}
		$("#InnerDiameterDiv").toggleClass('show');
	}
}

//智慧仓库:加载卷内径列表信息
function loadInnerDiameter() {
	//var innerDiameterAry = [420,508,610,760,762];//常用卷内径值集
	var innerDiameterAry = [508, 610, 760, 420];
	for (var idx in innerDiameterAry) {
		innerDiameterListHtml = innerDiameterListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label class ="innerDiameterValue">' + innerDiameterAry[idx] + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#InnerDiameterList").html(innerDiameterListHtml);
}

//智慧仓库:卷内径确认按钮点击事件
mui(document.body).on('tap', '#innerDiameterConfirm', function() {
	if (!inner_diameter) {
		mui.alert("请选择卷内径", "提示", "确认", function() {}, "div");
		return false;
	} else {
		$("#InnerDiameterDiv").toggleClass('show');
		$("#inner_diameter").val(inner_diameter);
		forLocationPack.inner_diameter = inner_diameter;
		afterGetPackObj();
	}
});

//智慧仓库:卷内径取消按钮点击事件
mui(document.body).on('tap', '#cancel', function() {
	$("#InnerDiameterDiv").toggleClass('show');
});

//智慧仓库:绑定卷内径列表选中事件
mui(document.body).on('selected', '#innerDiameterInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el); //dom元素转化成jquery对象
	inner_diameter = el_J.find(".innerDiameterValue").text();
});

//智慧仓库:获取库位信息
function queryLocationInfo() {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var requestObj = {
		seg_no: segNo,
		user_id: user_id,
		wprovider_id: wprovider_id,
		product_id: forLocationPack.product_id,
		pack_id: forLocationPack.pack_id,
		putin_weight: forLocationPack.putin_weight,
		inner_diameter: $("#inner_diameter").val(),
		putin_type: forLocationPack.putin_type,
		putin_voucher_num: forLocationPack.putin_voucher_num,
		vehicle_no: vehicle_id
	}
	var params = JSON.stringify(requestObj);
	var method = "exeQueryLocationInfo";
	var outtime = 3000;

	$.ajax({
		type: "post",
		async: true,
		timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			//console.log("DATA>>>>>>>>>>>"+JSON.stringify(data));
			//mui("#putin").button('reset');
			$("#overlay").removeClass("overlay");
			if (null != data) {
				//系统未找到合适的库位
				if (data.loc_success_flag == "0") {
					mui.alert("自动分配库位出错：" + data.loc_fail_reason + "。请手工选择库位", "提示", "确定", null, 'div');
					$("#pack_location").focus();
					setInputReadOnly("#pack_location", false);
					return false;
				}

				if (data.loc_success_flag == "-1") {
					mui.alert("不满足库位自动分配条件:" + data.loc_fail_reason + "。请手工选择库位", "提示", "确定", null, 'div');
					$("#pack_location").focus();
					setInputReadOnly("#pack_location", false);
					return false;
				}

				plus.nativeUI.toast("自动分配库位成功：" + data.loc_result_desc + "。", {
					'verticalAlign': 'center'
				});
				setLocationInfo(data.loc_result_desc, 1);
				$("#pack_location").val(data.loc_result_desc); //先记录当前获取到的新库位
				setDefaultInputStyle();
				//setInputReadOnly("#inner_diameter",true);//下一个捆包的内径输入框默认为只读
				//setInputReadOnly("#pack_location",true);//下一个捆包的库位输入框默认为只读
				afterGetPackObj(); //添加当前捆包到已扫描列表以及合计处理
				//$("#pack_location").val("");//捆包添加成功后，清空上一个捆包的库位信息？？？
			} else { //连接失败
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if (curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后重试");
				} else if (textStatus == "timeout") {
					XMLHttpRequest.abort();
					mui.alert("请求超时,请输入卷内径后按回车键重新获取", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				//mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}

/*智慧仓库:
 * 如果当前捆包已经存在，则更新捆包信息。
 * 否则添加一条新纪录到已扫描捆包列表
 */
function afterGetPackObj() {
	var pack_id = $("#pack_id").val();
	if (!selectById(pack_id, putinPackList)) {
		// add by lal 
		forLocationPack.pack_id = pack_id;
		forLocationPack.spec = $("#spec").val();
		forLocationPack.factory_product_id = $("#factory_product_id").val();
		forLocationPack.putin_weight = $("#weight").val();
		forLocationPack.inner_diameter = $("#inner_diameter").val();
		//货损类型
		forLocationPack.damage_type = '';
		//捆包类型
		forLocationPack.pack_type = "putin";
		/*
		forLocationPack.product_id = "";
		forLocationPack.shopsign = "";
		forLocationPack.putin_type = "";
		forLocationPack.putin_voucher_num = "";
		forLocationPack.product_type_id = "";
		forLocationPack.gross_weight = "";
		*/
		// end

		forLocationPack.putin_qty = $("#qty").val();
		forLocationPack.location_desc = $("#pack_location").val();
		forLocationPack.product_process_id = $("#product_process_id").val();
		putinPackList.push(forLocationPack);
		//更新合计框内信息
		//refreshSum(forLocationPack);
		reSum(putinPackList, pack_id);
	} else {
		var index = getIndexById(pack_id, putinPackList);
		putinPackList[index].location_desc = $("#pack_location").val();
		putinPackList[index].spec = $("#spec").val();
		putinPackList[index].factory_product_id = $("#factory_product_id").val();
		putinPackList[index].putin_weight = $("#weight").val();
		putinPackList[index].putin_qty = $("#qty").val();
		putinPackList[index].inner_diameter = $("#inner_diameter").val();
		putinPackList[index].product_process_id = $("#product_process_id").val();
		//捆包类型
		putinPackList[index].pack_type = "putin";
		console.log(" orLocationPack：" + JSON.stringify(putinPackList[index]));
		reSum(putinPackList, pack_id);
	}

	//清空信息焦点定位到捆包
	$("#pack_id").val("");
	$("#spec").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#product_process_id").val("");
	$("#inner_diameter").val("");
	clearPreviousLocation();
}

//智慧仓库:卷内径回车事件自动获取库位信息
$("#inner_diameter").keypress(function(e) {
	if (e.keyCode == 13) {
		var inner_diameter = $("#inner_diameter").val();
		if (inner_diameter == 0 || inner_diameter == "") {
			plus.nativeUI.toast("请输入大于0的卷内径值", {
				'verticalAlign': 'center'
			});
			return false;
		} else {
			//forLocationPack.inner_diameter = inner_diameter;
			queryLocationInfo();
		}
	}
});

//是否自动分配库位信息
function isAutoAllocLocation() {
	//console.log("location_type>>>>"+location_type+ " , auto_loc_flag>>>>>"+auto_loc_flag);
	if (location_type > 0 && auto_loc_flag == '1') {
		return true;
	} else {
		return false;
	}
}

//获取点状库位，后2位库位精准码
//inputPackLoction手工输入的库位代码
function getInputLocViewId(inputPackLocation) {
	if (location_type == "2") {
		return inputPackLocation.substr(inputPackLocation.length - 2);
	} else {
		return "";
	}
}

//inputPackLoction自动分配返回结果  “位代码/2位精确码”
function setLocationInfo(locationDesc, mode) {
	//自动分配模式
	if (mode == 1) {
		var ary = locationDesc.split("/");
		forLocationPack.location_desc = ary[0];
		forLocationPack.loc_view_id = ary[1];
		forLocationPack.auto_loc_pack = "1"; //自动分配到库位
	}
	//手工输入模式
	if (mode == 0) {
		forLocationPack.location_desc = locationDesc;
		forLocationPack.loc_view_id = getInputLocViewId(locationDesc);
		forLocationPack.auto_loc_pack = "0"; //手工输入库位
	}

}

//add by penglei 最近捆包  弹出框
mui(document.body).on('longtap', '#recent_pack_id', function() {
	//TODO 需要根据捆包信息重绘捆包信息弹出层 #pop_pack_info
	$("#pop_bg").toggleClass('show');
	$("#pop_pack_info").toggleClass('show');
	var recent_pack_id = $("#recent_pack_id").text();
	popWind(recent_pack_id);
});

function popWind(pack_id) {
	//console.log(JSON.stringify(putinPackList));
	var index = getIndexById(pack_id, putinPackList);
	//console.log(putinPackList[index].pack_id);
	var pack = putinPackList[index];
	$("#npack_id").html(pack.pack_id);
	$("#npack_location").html(pack.location_desc);
	$("#nspec").html(pack.spec);
	$("#nfactory_product_id").html(pack.factory_product_id);
	$("#nweight").html(pack.putin_weight);
}

function checkBeforeback() {
	var flag = false;
	if ($("#pop_bg").hasClass('show')) {
		$("#pop_bg").toggleClass('show');
		$("#pop_pack_info").toggleClass('show');
	} else {
		flag = true;
	}
	return flag;
}

//页面加载时判断库位开关
function queryConfigPDAVoucherCountMax() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","switch_type":"IF_JUDGE_PACK_LOCATION"}';
	var method = "exeConfigPDAvoucherMaxCount";
	//console.log("params"+params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			// console.log("switch_con:"+ data.switch_con);
			if_judge_pack_location = data.switch_con;
		}
	})
};

//判断库位是否存在
function judgeLocationExist() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_location":"' + $("#pack_location").val() + '","wprovider_id":"' +
		wprovider_id + '"}';
	var method = "exeQueryLocation";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (null != data) {
			console.log("resultStatus:" + data.resultStatus);
			console.log("locationList:" + JSON.stringify(data.locationList));
			if (data.resultStatus == "0") {
				mui.alert("库位不存在", "提示", "确认", null, "div");
				$("#pack_location").val("");
				$("#pack_location")[0].focus();
				return;
			} else {
				$("#pack_id")[0].focus();
			};
			if (data.resultStatus == "1") {
				if (data.locationList.length > 0) {
					$.each(data.locationList, function(i, item) {
						location_id = item.location_id;
					});
				}
			}
		}
	});
}

/**
 * 判断除“司机姓名”、“质量描述”、“身份证号”、“库位信息”之外的其他属性是否被修改
 */
function judgeWhetherEdit(pack_id, spec, inner_diameter, factory_product_id, weight, qty) {
	if (pack_id != global_pack_id) {
		global_edit_flag = "1";
		return;
	}
	if (spec != global_spec) {
		global_edit_flag = "1";
		return;
	}
	if (inner_diameter != global_inner_diameter) {
		global_edit_flag = "1";
		return;
	}
	if (factory_product_id != global_factory_procudt_id) {
		global_edit_flag = "1";
		return;
	}
	if (weight != global_weight) {
		global_edit_flag = "1";
		return;
	}
	if (qty != global_qty) {
		global_edit_flag = "1";
		return;
	}

}

// add by lal
// 智慧仓库:显示首道加工工序列表
mui(document.body).on('tap', '#product_process_id_btn', function() {
	document.activeElement.blur();
	setProductProcessListHtml();
});

//智慧仓库: 显示首道加工工序
function setProductProcessListHtml() {
	var productProcessObject = $("#product_process_id");
	if (productProcessObject.attr("readOnly") != "readonly") {
		if (!productProcessListHtml) {
			loadProductProcess(); //需要的时候才加载卷内径下拉框列表
		}
		$("#ProductProcessIdDiv").toggleClass('show');
	}
}

//智慧仓库:加载首道加工工序
function loadProductProcess() {
	for (var idx in productProcessAry) {
		productProcessListHtml = productProcessListHtml +
			'<li class="mui-table-view-cell">' +
			'<a class="mui-navigate-right">' +
			'<div style="width: 48%; float: left; text-align: left;" >' +
			'<label class ="productProcessValue">' + productProcessAry[idx] + '</label>' +
			'</div>' +
			'</a>' +
			'</li>';
	};
	$("#ProductProcessIdList").html(productProcessListHtml);
}

//智慧仓库:查询首道加工工序值集
function queryProductProcessId() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '"}';
	var method = "exeQueryProductProcessId";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 2000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if (data != null) {
				$.each(data, function(i, item) {
					productProcessAry[i] = item.product_process_id;
				});
				//console.log("productprocessid>>>>>>>>>>>>>>>>>>>>>>>" + JSON.stringify(productProcessAry));
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				console.log("查询首道加工工序值集处理异常" + JSON.stringify(data));
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			if (textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
};

//智慧仓库:首道加工工序确认按钮点击事件
mui(document.body).on('tap', '#productProcessIdConfirm', function() {

	if (!product_process_id) {
		mui.alert("请选择首道加工工序", "提示", "确认", function() {}, "div");
		return false;
	} else {
		$("#ProductProcessIdDiv").toggleClass('show');
		$("#product_process_id").val(product_process_id);
		product_process_id = $("#product_process_id").val();
		console.log("选中的加工工序: " + product_process_id);
		//add by yangzemin 长春收货的捆包查询逻辑
		if (JSON.stringify(packObj) != "{}" && packObj.pack_type == "changchun") {
			//长春收货捆包
			packObj.product_process_id = product_process_id;
			putinPackList.push(packObj);
			reSum(putinPackList, $("#pack_id").val());
			$("#pack_id").val("");
			$("#spec").val("");
			$("#product_process_id").val("");
			$("#weight").val("");
			$("#qty").val("");
			packObj = {};
			$("#pack_id")[0].focus();
		} else {
			if (material_shape == 'J') {
				if ($("#inner_diameter").val() == 0) {
					plus.nativeUI.toast("请选择或输入卷内径", {
						'verticalAlign': 'center'
					});
					setInputReadOnly("#inner_diameter", false); //如果捆包卷内径不在常规的卷内径列表中则需要手工输入
					setInnerDiameterListHtml();
					return false;
				} else {
					afterGetPackObj();
				}
			} else {
				setInputReadOnly("#pack_location", false);
				$("#pack_location").focus();
				return false;
			}
			$("#product_process_id")[0].focus();
		}
	}

});

//智慧仓库:首道加工工序按钮点击事件
mui(document.body).on('tap', '#process_cancel', function() {
	$("#ProductProcessIdDiv").toggleClass('show');
});

//智慧仓库:绑定首道加工工序列表选中事件
mui(document.body).on('selected', '#ProductProcessIdInfo .mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el); //dom元素转化成jquery对象
	product_process_id = el_J.find(".productProcessValue").text();
});

//add by tangli 20191126 ERP_58339 湛江现货入库查询账号对应外库库区
function getPacklocation_zj() {
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutInService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '"}';
	var method = "exeQueryPackLoction_zj";
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 2000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("exeQueryPackLoction_zj->返回：" + JSON.stringify(data));
			if (data != null) {
				if (data.resultStatus == '1') {
					if (data.resultMap != "") {
						pack_location_zj = data.resultMap
						$("#pack_location").val(data.resultMap);
						$("#pack_id").focus();
						$('#pack_location').attr("disabled", true);
						$("#pack_location").css("background-color", "#CCCCCC");
					}
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				console.log("exeQueryPackLoction_zj处理异常" + JSON.stringify(data));
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			if (textStatus == "timeout") {
				queryPackNetNotConnect();
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
};
//end by tangli 20191126 ERP_58339 湛江物流入库查询账号对应外库库区


mui(document.body).on('tap', '#unload', function() {

	mui.openWindow({
		url: '../unload/unload_pack.html',
		id: 'unload',
		createNew: true
	});

});


/**
 * 调用工贸接口，打印入库捆包标签 yangzemin
 */
function doPutinLabelPrint(seqNo) {

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	var params = {};
	params['seg_no'] = segNo;
	params['seq_no'] = seqNo; //上传批次号
	params['user_id'] = user_id;
	params['print_type'] = "putin"; //打印类型：入库标签

	var settings = {};
	settings['orientation'] = "1";
	settings['marginLeft'] = "0";
	settings['marginRight'] = "0";
	settings['marginTop'] = "0";
	settings['marginBottom'] = "0";
	settings['height'] = "120";
	settings['width'] = "100";
	settings['printServiceName'] = "ZDesigner ZT230-300dpi ZPL";
	params['settings'] = settings;

	params = JSON.stringify(params);
	console.log("doPDAPrint参数：" + params);
	//params=encodeURI(params,'utf-8');
	var method = "doPDAPrint";
	var waiting = plus.nativeUI.showWaiting(); //显示等待框

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		timeout: 100 * 1000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log("doPDAPrint接口返回：" + JSON.stringify(result));
			waiting.close();
			if (result != null) {
				if (result.out_result == "1") {
					// mui.alert("捆包标签" + result.out_result_desc, "提示", "确定", function() {

					// }, "div");
					onPrintLabelFinish("捆包标签" + result.out_result_desc, seqNo);
				} else {
					// mui.alert("捆包标签打印失败：" + result.out_result_desc, "提示", "确定", function() {}, "div");
					onPrintLabelFinish("捆包标签打印失败：" + result.out_result_desc, seqNo);
				}
			} else {
				// mui.alert("捆包标签打印失败", "提示", "确定", function() {}, "div");
				onPrintLabelFinish("捆包标签打印失败", seqNo);
			}
		},
		error: function() {
			// mui.alert("捆包标签打印失败", "提示", "确定", function() {}, "div");
			onPrintLabelFinish("捆包标签打印失败", seqNo);
		}
	});

}

function onPrintLabelFinish(out_result_desc, seqNo) {
	mui.alert(out_result_desc, "提示", "确定", function() {
		//打印吊装清单
		doHoistingPrint(seqNo);
	}, "div");
}

/**
 * 调用工贸接口，打印吊装清单 yangzemin
 */
function doHoistingPrint(seqNo) {

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	var params = {};
	params['seg_no'] = segNo;
	params['seq_no'] = seqNo; //上传批次号
	params['user_id'] = user_id;
	params['print_type'] = "hoisting"; //打印类型：入库标签

	//标签打印参数，仅用于参考，表里对应有配置
	//构建报表打印参数
	var settings = {};
	settings['orientation'] = "0";
	settings['marginLeft'] = "10";
	settings['marginRight'] = "10";
	settings['marginTop'] = "10";
	settings['marginBottom'] = "10";
	settings['height'] = "305";
	settings['width'] = "215";
	settings['printServiceName'] = "EPSON LQ-630KII ESC/P2";
	params['settings'] = settings;

	params = JSON.stringify(params);
	console.log("doPDAPrint参数：" + params);
	//params=encodeURI(params,'utf-8');
	var method = "doPDAPrint";
	var waiting = plus.nativeUI.showWaiting(); //显示等待框

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		timeout: 100 * 1000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log("doPDAPrint接口返回：" + JSON.stringify(result));
			waiting.close();
			if (result != null) {
				if (result.out_result == "1") {
					mui.alert("吊装清单" + result.out_result_desc, "提示", "确定", function() {

					}, "div");
				} else {
					mui.alert("吊装清单打印失败：" + result.out_result_desc, "提示", "确定", function() {}, "div");
				}
			} else {
				mui.alert("吊装清单打印失败", "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui.alert("吊装清单打印失败", "提示", "确定", function() {}, "div");
		}
	});

}
