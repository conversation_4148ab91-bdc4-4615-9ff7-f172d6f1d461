mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	mui.openWindow({
		url: '../public/index.html',
		id: "index",
		createNew: true
	});
}

window.onload = function onload() {
	var seg_no = localStorage.getItem("segNo");
	
	if(seg_no == "00131"){
		var selectHtml = "";
		selectHtml = selectHtml + '<button type="button" id="install_unload_money" class="mui-btn mui-btn-primary mui-icon mui-icon-search mui-btn-block">' +
								'装卸货管理' +							
								'</button>';
		$("#vehicle_list").html(selectHtml);
	}	
	if(seg_no == "00118" || seg_no == "00152"){
		$("#create_allocate_vehicle_id").hide();
	}else if(seg_no == "00129" || seg_no == "00145"){
		$("#create_allocate_vehicle_id").hide();
		$("#youni_work").hide();
	}
}
//配车单管理
mui(document.body).on('click', '#add_voucher', function() {
	mui.openWindow({
		url: 'patch_allocate_vehicle.html',
		id: "patch_allocate_vehicle",
		createNew: true
	});
});

// 预发货清单管理
mui(document.body).on('click', '#youni_work', function() {
	mui.openWindow({
		url: 'pda_youni_work.html',
		id: "pda_youni_work",
		createNew: true
	});
});

//装卸货管理
mui(document.body).on('click', '#install_unload_money', function() {
	mui.openWindow({
		url: 'install_unload_money.html',
		id: "install_unload_money",
		createNew: true
	});
});

//新增配车单
mui(document.body).on('click', '#create_allocate_vehicle_id', function() {
	mui.openWindow({
		url: 'create_allocate_vehicle_id.html',
		id: "create_allocate_vehicle_id",
		createNew: true
	});
});