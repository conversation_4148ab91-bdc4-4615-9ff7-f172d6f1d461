#companyList label {
	font-size: 22px;
}

#car_no {
	width: 100%;
}

.mui-placeholder {
	margin-top: 10px;
}

.mui-placeholder span {
	font-size: 20px;
	color: #000000;
}

.search_btn {
	width: 19%;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 38px;
	height: 38px;
	text-align: center;
	color: #999;
}

/* 半透明的遮罩层 */
.overlay {
    background-color: #777777;
     opacity: 0.5; /* 透明度 */
    /*filter: alpha(opacity=50); /* IE的透明度 
    
    display: none;
   
    top: 0px;
    left: 0px;*/
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 99999; /* 此处的图层要大于页面 */
    /*display:none;*/
}