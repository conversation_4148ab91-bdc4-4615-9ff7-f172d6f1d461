			.mui-control-content {
				background-color: white;
				min-height: 280px;
			}
			
			.mui-segmented-control{
				font-size: 22px;
			}
			
			.mui-table-view-cell{
				font-size: 22px;
				padding: 8px 0px 8px 18px;
				text-align: left;
			}
			
			/** 明细样式 */
			.left{
				float: left;
				width: 50%;
			}
			
			#pack_id{
				font-size: 22px;
				margin-bottom: 4px;
				padding: 6px 0px 0px 0px;
				color: blue;
			}
			
			#location_desc{
				font-size: 18px;
				margin-bottom: 4px;
				padding: 6px 0px 0px 0px;
				color: blue;
			}
			
			.mui-navigate-right:after{
				content: '';
			}
			
			.select:after{
				content: '\e472';
				color: red;
				font-size: 50px;
				font-weight: 600;
				right: 10px;
			}
						
			#weight,#spec{
				font-size: 16px;
			}
			
			#spec span{
				background-color: #EC971F;
				color: white;
				margin-right: 6px;
			}
			
			
			#pack_id span{
				background-color: darkred;
				color: white;
				margin-left: 20px;
				font-size: 16px;
			}
			#pack_id label{
				color: #000000;
				margin-left: 6px;
				font-size: 16px;
			}
			
			#weight span{
				background-color: purple;
				color: white;
				margin-right: 6px;
			}
			
			#location_desc span{
				background-color: red;
				color: white;
				margin-right: 6px;
			}

			
			/** 弹出图层设置 */
			#pop_pack_info{
				position: absolute;
				z-index: 999;
				width: 220px;
				height: 280px;
				left: 50%;
				top: 50%;
				margin-left: -100px;
				margin-top: -122px;
				
				border-radius: 10px;
				background: #FFFFFF;
				box-shadow: 0px 10px 12px rgba(0,0,0,.4);
				
				/** 动画效果 */
				visibility: hidden;
				opacity: 0;
				
				/** 文字效果 */
				font-size: 20px;
				text-align: left;
			}
			
			#pop_pack_info.show{
				visibility: visible;
				opacity: 1;
			}
			
			#pop_pack_info > .title{
				text-align: center;
				padding: 8px 0px;
				font-size: 22px;
				border-bottom: 1px solid;
				border-color: #D8D8D8;
			}
			
			.pop_detail{
				margin: 6px 4px;
			}
			
			span.icon{
				background-color: #3D9EE5;
				color: white;
				margin-right: 4px;
			}
			
			
			span.icon-1{
				background-color: #800080;
				color: white;
				margin-right: 4px;
			}
			
			#pop_bg{
				position: absolute;
				top: 0px;
				left: 0px;
				z-index: 998;
				width: 100%;
				height: 100%;
				background:rgba(0,0,0,.3);
				visibility: hidden;
				opacity: 0;
			}
			
			#pop_bg.show{
				visibility: visible;
				opacity: 1;
			}
