var segNo;
var webServiceUrl;
var segNoInfo;
var isPrintVer = false; //是否是蓝牙打印版本
var clickNum = 0; //重写返回键
$(document).ready(function() {
	initParms();
	postBack();
	addListener();
	swipeBack(true);
});

function initParms() {
	segNo = localStorage.getItem("segNo");
	webServiceUrl = localStorage.getItem("webServiceUrl");
	segNoInfo = localStorage.getItem("segNoInfo");
}

function postBack() {
	$('#quyu').val(segNoInfo);
	mui.plusReady(function() {
		mui.currentWebview.show();
		var seg_no_info = plus.webview.currentWebview().seg_no_info;
		if(seg_no_info != null && seg_no_info != "") {
			$('#quyu').val(plus.webview.currentWebview().seg_no_info);
		}
	});
}

function addListener() {
	//选择区域组织
	mui(document.body).on('tap', '#quyu', function(e) {
		console.log("111");
		location.href = "select_seg_no.html";
	});
	//登录按钮
	mui(document.body).on('tap', '#login_button', function(e) {
		if(validate()) {
			initData();
		}
	});
}
/**
 * 验证
 */
function validate() {
	if('' == $('#quyu').val()) {
		mui.toast("请选择组织机构");
		return false;
	} else if("" == $("#account").val()) {
		mui.toast("请输入用户名");
		return false;
	} else if("" == $("#password").val()) {
		mui.toast("请输入密码");
		return false;
	}
	mui('#login_button').button('reset');
	return true;
}

function initData() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAUserService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + $("#account").val() + '","pass_word":"' + $("#password").val() +
		'"}';
	var method = "userAuthorizeCheck";
	console.log("outUri="+outUri);
	console.log("innerUri="+innerUri);
	console.log("params="+params);
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性   
	console.log("login返回:" + JSON.stringify(data));
		if(null != data) { //连接成功
			if(data.resultStatus == '1') { //登录成功 
				localStorage.setItem("account", $("#account").val());
				location.href = "index.html"; //跳转到登录成功的页面
			} else {
				mui.toast(data.resultDesc);
			}
		} else { //连接失败
			mui.toast("服务器连接异常");
		}
	});
}

//用户名
$("#account").keypress(function(e) {

	if(e.keyCode == 13) {
		if($("#account").val() == "dev") {
			$("#password").val("baotest2018");
		}
		$("#password").focus();

	}
});

//密码
$("#password").keypress(function(e) {
	if(e.keyCode == 13) {
		if(validate()) {
			initData();
		}
	}
});

mui.back = function(event) {
	clickNum++;
	if(clickNum > 1) {
		plus.runtime.quit();
	} else {
		mui.toast("再按一次退出应用");
	}
	setTimeout(function() {
		clickNum = 0
	}, 2000);
	return false;
}

// 扩展API加载完毕后调用onPlusReady回调函数 
document.addEventListener("plusready", onPlusReady, false);
// 扩展API加载完毕，现在可以正常调用扩展API
function onPlusReady() {
	// 获取本地应用资源版本号
	plus.runtime.getProperty(plus.runtime.appid, function(inf) {
		cur_version = inf.version;
		console.log("当前应用版本：" + cur_version);
		//检测更新如果有更新则下载安装包进行更新动作
		if(!isPrintVer) {
			checkVersionNo(cur_version);
		}
	});
}

//检查版本号
function checkVersionNo(cur_version) {
	//查询版本号信息
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAUserService';
	var params = '{"cur_version":"' + cur_version + '"}';
	var method = "exeQueryVersion";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		//console.log(JSON.stringify(data))
		if(null != data) { //连接成功
			console.log("新版本检测：" + JSON.stringify(data));
			if(data.returnStatus == '1') { //有新版本
				downWgt(data.downloadPath, data.versionInfo);
			}
		}
	});
}

// 下载wgt文件
function downWgt(downloadPath, versionInfo) {
	plus.nativeUI.showWaiting("下载更新包文件...");
	console.log("更新包下载路径：" + (domainName + downloadPath));
	plus.downloader.createDownload(domainName + downloadPath, {
		filename: "_doc/udpate/"
	}, function(d, status) {
		if(status == 200) {
			console.log("下载更新包文件成功：" + d.filename);
			installWgt(d.filename, versionInfo); // 安装wgt包
		} else {
			console.log("下载更新包文件失败！");
			plus.nativeUI.alert("下载更新包文件失败！");
		}
		plus.nativeUI.closeWaiting();
	}).start();
}

// 更新应用资源
function installWgt(path, versionInfo) {
	plus.nativeUI.showWaiting("安装更新包文件...");
	plus.runtime.install(path, {
		force: true
	}, function() {
		plus.nativeUI.closeWaiting();
		console.log("安装wgt文件成功！");
		//TODO 删除下载文件
		//		delAllDirectory("_doc/update");
		var s = getVersionInfo(versionInfo);
		plus.nativeUI.alert("本次更新内容:" + s, function() {
			plus.runtime.restart();
		});
	}, function(e) {
		plus.nativeUI.closeWaiting();
		console.log("安装更新包文件失败[" + e.code + "]：" + e.message);
		plus.nativeUI.alert("安装更新包文件失败[" + e.code + "]：" + e.message);
		//删除下载文件
		delAllDirectory("_doc/update");
	});
}

//处理更新内容
function getVersionInfo(versionInfo) {
	var infoArray = versionInfo.split("@");
	var s = "";
	$.each(infoArray, function(i, value) {
		s += ("\n" + value);
	});
	return s;
}

//删除目录下文件
function delAllDirectory(path) {
	plus.io.resolveLocalFileSystemURL(path, function(entry) {
		var directoryReader = entry.createReader();
		directoryReader.readEntries(function(entries) {
			var i;
			for(i = 0; i < entries.length; i++) {
				//console.log(entries[i].name);
				entries[i].remove();
			}
		}, function(e) {
			console.log("查找该目录下文件失败" + path)
		});
	});
}