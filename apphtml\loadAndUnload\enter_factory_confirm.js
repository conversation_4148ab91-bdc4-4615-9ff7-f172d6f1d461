/**
 * 变量定义
 */
var  car_num = "";

mui.init({
	swipeBack: true //启用右滑关闭功能  
});


mui.plusReady(function(){
	queryEnterFacoryVehicleNo();
});


//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	car_num = e.detail.el.innerText.trim();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	if(car_num == null || car_num == "") {
		mui.alert("请选择入厂车牌号", "提示", "确定",null,'div');
		return ;
	} else {
		mui("#confirm").button("loading");
		$("#overlay").addClass("overlay");
	     confirmVehicleEnterFactory();
//					mui.alert("当前选择的入厂车牌号为:"+ car_num, "提示", "确定",function(){
//					//入厂确认
//				     confirmVehicleEnterFactory();
//					},'div');
	}
});

//搜索
mui(document.body).on('tap', '#query_button',
	function() {
		queryEnterFacoryVehicleNo();
	});

//车辆入厂车牌信息查询方法
function queryEnterFacoryVehicleNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var  car_no = $("#car_no").val();
	var  segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var  webServiceUrl = localStorage.getItem("webServiceUrl");
//				var  segNo = "00112";
//				var  webServiceUrl = "10.30.184.231:7001";
	var  outUri = domainName+"webService.jsp?callback=?";
	var  innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';//PDABoardVehicleService
	var  params = '{"seg_no":"' + segNo + '","vehicle_no":"' + car_no + '"}';
	var  method = "queryVehicleLicense";

	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			//先清空当前列表中的牌号信息
			var  lihtml = "";
			$("#companyList").html(lihtml);
			//填充最新的牌号信息
			if(data.returnList.length>0){
				$.each(data.returnList, function(i, item) {
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;" >' +
						'<label>' + item.vehicle_no + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#companyList").html(lihtml);
			}else{
				mui.alert("未查询到对应的车牌号", "提示", "确定",function(){
			 	},'div');
			 	return ;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定",function(){
			 	},'div');
			 	return ;
		}
	});
}

//车辆入厂车确认
function confirmVehicleEnterFactory() {
	var  segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var  account = localStorage.getItem("account");//采用localStorage存储数据
	var  webServiceUrl = localStorage.getItem("webServiceUrl");
//				var  segNo = "00112";
//				var  account = "dev";
//				var  webServiceUrl = "10.30.184.231:7001";
	var  outUri = domainName+"webService.jsp?callback=?";
	var  innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService';
	var  params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","vehicle_no":"' + car_num + '"}';
	params = encodeURI(params,'utf-8');
	var  method = "exeVehicleEnteryFactory";
	//console.log("confirm is execute>>>>>>>>>>>>>"+params);  
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		//console.log("data>>>>>>>>>>>>>>>>>"+data.returnStatus);
		if(data !=null ) {
			mui("#confirm").button("reset");
			$("#overlay").removeClass("overlay");
			if(data.returnStatus == "1"){
			 	mui.alert("操作成功", "提示", "确定",function(){
			 	},'div');
			 	 return ;
			}else{
				  mui.alert("操作失败!原因："+data.returnDesc, "提示", "确定",function(){
			 	  },'div');
			 	  return ;
			    }
		}else{ //连接失败
			mui("#confirm").button("reset");
			$("#overlay").removeClass("overlay");
			mui.alert("连接服务器异常", "提示", "确定",function(){
			 	  },'div');
			 	 return ;
		    }
	});
}