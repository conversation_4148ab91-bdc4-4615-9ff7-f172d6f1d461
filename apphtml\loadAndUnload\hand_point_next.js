/**
 * 变量定义
 */
var hand_point_id = "";
var hand_point_name = "";
var seg_no = localStorage.getItem("segNo");
var webServiceUrl = localStorage.getItem("webServiceUrl");

mui.init({
	swipeBack: true //启用右滑关闭功能
});

$(function() {
	//调用webservices接口
	//queryHandPointName();
});
/*
 * 装货
 */
mui(document.body).on('tap', '#next_start_load', function() {
	//高强钢采用以前的
	if(seg_no == '00138') {
		mui.openWindow({
			url: 'putout_list_new.html',
			id: 'putout_list_new',
			extras: {
				openType: 'vehicle_load_manage',
			},
			createNew: true,

		});
	} else {
		mui.openWindow({
			url: 'hand_point_list.html',
			id: 'hand_point_list',
			createNew: true,
		});
	}

});
/*
 * 卸货
 */
mui(document.body).on('tap', '#next_start_unload', function() {
	mui.openWindow({
		url: 'putin_scan.html',
		id: 'putin_scan',
		extras: {
			openType: 'vehicle_load_manage',
		},
		createNew: true,
		//					show:{
		//						aotoShow:false
		//					}
	});
});
/*
 * 结束装卸货
 */
mui(document.body).on('tap', '#end_load', function() {
	mui.openWindow({
		url: 'hand_point_end.html',
		id: 'hand_point_end',
		createNew: true,
	});
});