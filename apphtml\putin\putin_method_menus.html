<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="HandheldFriendly" content="true" />
		<meta name="MobileOptimized" content="320" />
		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css"/>
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css"/>
		<title>入库方式</title>
		<style>
			.mui-btn.mui-icon {
			    font-size: 24px;
			    line-height: 1.9;
			    margin-top: 10px;
			    font-weight: 900;
			}
		</style>
	</head>
	<body>
		<div class="wrapper">
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">入库方式</h4>
		</div>
			<!--header end-->
			<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
		    <div class="mui-content-padded">
		        <button type="button" id="normal_putin" class="mui-btn mui-btn-primary mui-icon mui-icon-search mui-btn-block">
		           	 普通入库
		        </button>
		        <button type="button" id="transfer_putin" class="mui-btn mui-btn-primary mui-icon mui-icon-search mui-btn-block">
		                         转库入库              
		        </button>
		        <!--<button type="button" id="call_number_force" class="mui-btn mui-btn-primary mui-icon mui-icon-search mui-btn-block">
		         	 强制叫号             
		        </button>-->
		    </div>
		</div>
		</div>
	</body>
	<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
	<script src="../../js/mui.min.js"></script>
	<script src="../../js/pda/putin.js"></script>
	<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
	<script type="text/javascript">
		var segNo=localStorage.getItem("segNo");
			mui.init({
				//不启用右滑关闭功能
				swipeBack:false
			});
			//如果是高强钢直接按原来的界面
			$(function(){	
				/*if('00138'==segNo){
					mui.openWindow({
						url:'putout_list_new.html',
						id:'putout_list_new',
						createNew:true
					});
				}else{
					$("#call_number_force").css("display","none");
				};*/
			});
		mui(document.body).on('tap','#back',function(){
			mui.back();
		});
		mui.back = function(){
			mui.openWindow({
				id:"index",
			    url:"../public/index.html",
			    createNew: false
			});
		}
		
		//进入转库捆包入库扫描页面
		mui(document.body).on('click','#transfer_putin',function(){
			//window.location.href = "putin_scan_transfer.html";
		   	mui.openWindow({
				url:'putin_scan_transfer.html',
				id:"putin_scan_transfer",
				createNew:true
			});
		});
		
		//进入普通捆包入库扫描页面
		mui(document.body).on('click','#normal_putin',function(){
			mui.openWindow({
				url:'putin_scan.html',
				id:'putin_scan',
				createNew:true
			});
		});
	</script>
</html>
