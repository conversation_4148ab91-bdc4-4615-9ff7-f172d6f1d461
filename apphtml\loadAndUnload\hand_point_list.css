#voucher_id {
	margin-bottom: 0px;
	padding: 2px 10px;
	font-size: 22px;
}

.mui-input-row label,
a {
	color: black;
	font-size: 22px;
	width: 48%;
}

#voucher_num {
	font-size: 22px;
	color: blue;
	margin-right: 10%;
}

.icon {
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}

.row {
	margin: 5px;
	font-size: 18px;
}

#table .mui-table-view-cell {
	padding: 2px 15px;
}

.divhidden {
	position: static;
}

.icon-search {
	position: absolute;
	left: 270px;
	z-index: 5;
	background-image: url(../../resource/search.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 5px;
}


/** 修改、删除按钮样式 */

.mui-table-view-cell .mui-disabled {
	width: 35%;
}

.mui-table-view-cell .mui-disabled a {
	width: 100%;
	font-size: 25px!important;
	padding-left: 30%!important;
}

.opt_type-button-3 button {
	width: 32%;
	margin: 2px 0px;
	font-size: 18px;
	height: 36px;
}

.opt_type-button-5 button {
	width: 18.5%;
	margin: 2px 0px;
	font-size: 12px;
	height: 36px;
	padding: 0px;
}

.active {
	background-color: #2AC845;
	color: white;
}

#query_next_target {
	font-size: 30px;
	position: absolute;
	z-index: 10;
	top: 120px;
	right: 0;
	width: 45px;
	height: 38px;
}