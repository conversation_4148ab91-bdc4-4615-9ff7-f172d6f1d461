/**
 * 初始化变量信息 
 */
var putinPackList = new Array(); //入库捆包列表（已扫描列表）
var team_id = localStorage.getItem("team_id"); //班组代码
var work_shift = localStorage.getItem("class_id"); //班次代码
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var operatorType = ""; //操作类型
var putoutVoucherList = new Array(); //出库单据列表
var unUploadPack = new HashMap(); //未上传捆包信息  Map<voucher_id,packList>
var uploadPack = new HashMap(); //已上传捆包信息  Map<voucher_id,packList>
var packInfo = new HashMap(); //捆包信息  Map<pack_id,packInfo>
var contractSubInfo = new HashMap(); //形式提单子项信息  Map<contract_subid,weight_qty_info>
var labelInfo = new HashMap(); //标签号捆包号对照关系信息 
var vehicle_id = ""; //车牌号  成都宝钢的车牌号必须填写，
var pda_switch = ""; //铁托架开关
var psteel_support_id = ""; //扫描当前捆包信息上自带的铁托架号

var putinScanPage = "";
var pack_total_count = 0; //未扫描捆包个数
var pack_total_weight = 0; //未扫描捆包重量

//tangli add 
var if_auto_alloc_location_manage = 0; //判断是否提示实物出库窗口
var putout_confirm_flag = ""; //实物出库0 与 虚拟出库1
var mean_name = localStorage.getItem("name"); //判断是从菜单进来的还是出厂物流进来的
var hand_vehicle_id = localStorage.getItem("vehicle_id"); //车牌号 出厂物流
var car_trace_no = localStorage.getItem("car_trace_no");
var allocate_vehicle_id = localStorage.getItem("allocate_vehicle_id");
window.onload = function onload() {
	mui.plusReady(function() {
		putinPackList = plus.webview.currentWebview().putinPackList;
		console.log("捆包检查："+JSON.stringify(putinPackList));
		operatorType = plus.webview.currentWebview().opt_type;
		pda_switch = plus.webview.currentWebview().pda_switch;
		putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
		//HashMap类型直接用=赋值会报错  所以把['map']内容移动过去
		//unUploadPack = plus.webview.currentWebview().unUploadPack;
		unUploadPack['map'] = plus.webview.currentWebview().unUploadPack['map'];
		uploadPack['map'] = plus.webview.currentWebview().uploadPack['map'];
		packInfo['map'] = plus.webview.currentWebview().packInfo['map'];
		labelInfo['map'] = plus.webview.currentWebview().labelInfo['map'];
		contractSubInfo['map'] = plus.webview.currentWebview().contractSubInfo['map'];
		putinScanPage = plus.webview.getWebviewById("putout_scan_new");
		//$("#voucher_num").text(voucher_num);
		console.log("unUploadPack:" + JSON.stringify(putinPackList));
		//console.log("uploadPack:" + JSON.stringify(uploadPack));
		//TODO 在这关闭是否合适？？？   关闭出库单据窗口
		/*// 获取所有Webview窗口  
				var wvs=plus.webview.all();  
				for(var i=0;i<wvs.length;i++){  
				console.log("webview"+i+": "+wvs[i].getURL());  
				} */
		//关闭 出库清单页面
	
		//循环未扫描捆包信息，得到未扫描捆包重量和件数信息。
		//xunhuan();
		
		showPackList(putinPackList);
	});

	//showPackList(putinPackList);

	

}
$(function() {
	//捆包输入框获得焦点
	$("#pack_id")[0].focus();
	
});
mui.init({
	swipeBack: true, //关闭右滑关闭功能
	gestureConfig: {
		longtap: true,
		doubletap: true
	}
});
/*
function xunhuan() {
	pack_total_count = 0;
	var list = unUploadPack.keySet();
	$.each(list, function(i, item) {
		var packList = unUploadPack.get(list[i]);
		if (packList.length > 0) {
			$.each(packList, function(j, jitem) {
				pack_total_count++;
				var pack_info = packInfo.get(packList[j].pack_id);
				console.log(pack_info.putin_weight + "," + pack_total_count);
				pack_total_weight = parseFloat(pack_total_weight) + parseFloat(pack_info.putin_weight);
			});
		}
	});
	pack_total_weight = pack_total_weight.toFixed(3);
	console.log("pack_total_count:" + pack_total_count + "," + pack_total_weight);
	$("#wscan_qty").text(pack_total_count);
	$("#wscan_weight").text(pack_total_weight);
}
*/
mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});

//捆包号
$("#pack_id").keypress(function(e) {
	if (e.keyCode == 13) {
		var pack_id = $("#pack_id").val();
		pack_id = trimPackId(pack_id);
		//console.log($('#pack_id').val()+","+JSON.stringify(putinPackList))
		//校验捆包号是否重复
		if (selectById(pack_id, putinPackList)) {
			mui.alert("不能扫描重复的捆包号", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		//获取捆包信息后隐藏软键盘
		document.activeElement.blur();
		//输入或扫描捆包号之后，调用捆包查询接口
		//console.log(pack_id.length)
		initData();

	}
});

function initData() {
	//var outUri = domainName+"webService.jsp?callback=?";
	//add by penglei 湛江 20191014 ERP_56908
	var zjPackId = $("#pack_id").val();
	var params = '';

	params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","allocate_vehicle_id":"' + allocate_vehicle_id +
		'","pack_id":"' + $("#pack_id").val() + '"}';
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	console.log(params);
	var method = "exeDownLoadPutoutPackInfo";
	handLocationFlag = "0";
	forLocationPack = {};
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log("exeDownLoadPutinPackInfo返回：" + JSON.stringify(data));
			if (data != null) {
				if (data.returnStatus == "0") {
					mui.alert(data.returnDesc, "提示", "确定", null, 'div');
					console.log("exeDownLoadPutinPackInfo处理异常" + JSON.stringify(data));
					$("#pack_id").val("");
					return;
				} else {
					//console.log(JSON.stringify(data));
					forLocationPack = data; //modify by wangshengbo171211 定义成全局变量
					$("#pack_id").val(data.pack_id);
					$("#spec").val(data.spec);
					$("#putin_weight").val(data.putin_weight);
					$("#car_num").val(data.shopsign);
					$("#customer_id").val(data.customer_id);
					$("#product_id").val(data.product_id);
					$("#factory_product_id").val(data.factory_product_id);

					material_shape = data.material_shape;
					/*modify by wangshengbo 171207 库位自动分配-智慧仓库
					 * auto_loc_flag != 1手工录入库位
					 * */
					afterGetPackObj(); //当前扫描捆包添加到已扫描列表以及后续处理
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				console.log("exeDownLoadPutinPackInfo处理异常" + JSON.stringify(data));
				return;
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.alert("服务器连接异常", "提示", "确定", null, 'div');
		}
	});
}

function afterGetPackObj() {
	var pack_id = $("#pack_id").val();
	if (!selectById(pack_id, putinPackList)) {
		// add by lal 
		forLocationPack.pack_id = pack_id;
		forLocationPack.spec = $("#spec").val();
		forLocationPack.factory_product_id = $("#car_num").val();
		forLocationPack.putin_weight = $("#putin_weight").val();
		forLocationPack.inner_diameter = $("#inner_diameter").val();
		//货损类型
		forLocationPack.damage_type = '';
		//捆包类型
		forLocationPack.pack_type = "putin";
		/*
		forLocationPack.product_id = "";
		forLocationPack.shopsign = "";
		forLocationPack.putin_type = "";
		forLocationPack.putin_voucher_num = "";
		forLocationPack.product_type_id = "";
		forLocationPack.gross_weight = "";
		*/
		// end

		forLocationPack.putin_qty = $("#qty").val();
		forLocationPack.location_desc = $("#customer_id").val();
		forLocationPack.product_process_id = $("#product_process_id").val();
		putinPackList.push(forLocationPack);
		//更新合计框内信息
		//refreshSum(forLocationPack);
		reSum(putinPackList, pack_id);
	} else {
		var index = getIndexById(pack_id, putinPackList);
		putinPackList[index].location_desc = $("#customer_id").val();
		putinPackList[index].spec = $("#spec").val();
		putinPackList[index].factory_product_id = $("#car_num").val();
		putinPackList[index].putin_weight = $("#putin_weight").val();
		putinPackList[index].putin_qty = $("#qty").val();
		putinPackList[index].inner_diameter = $("#inner_diameter").val();
		putinPackList[index].product_process_id = $("#product_process_id").val();
		//捆包类型
		putinPackList[index].pack_type = "putin";
		console.log(" orLocationPack：" + JSON.stringify(putinPackList[index]));
		reSum(putinPackList, pack_id);
	}

	//清空信息焦点定位到捆包
	$("#pack_id").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#product_process_id").val("");
	$("#inner_diameter").val("");
	
}

function transLabelID2PackID(packId) {
	console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
	var pack_id = labelInfo.get(packId);
	console.log("pack_id:" + pack_id);
	if (pack_id != null && pack_id != packId) {
		$('#pack_id').val(pack_id);
	}
}

//将捆包信息从未扫捆包列表中移动到已扫捆包列表中
function addPack2Upload(pack_info) {
	//console.log("addPack2Upload pack_info: " + JSON.stringify(pack_info));
	var packList_un = unUploadPack.get(pack_info.voucher_id);
	var index_un = getIndexById(pack_info.pack_id, packList_un);

	//判断已上传捆包信息中是否有该单据(如果有单据必定有捆包list最多length为0)
	//并将捆包信息加到已扫捆包信息中
	if (uploadPack.get(pack_info.voucher_id) == null) {
		//添加单据及捆包list
		var list = new Array();
		list.push(packList_un[index_un]);
		uploadPack.put(pack_info.voucher_id, list);
	} else {
		//直接将捆包信息添加到对应单据下的捆包列表
		var list = uploadPack.get(pack_info.voucher_id);
		list.push(packList_un[index_un]);
	}

	//将未扫捆包list的捆包信息移除
	packList_un.splice(index_un, 1);
	console.log("after addPack2Upload : ");
	console.log(JSON.stringify(unUploadPack));
	console.log(JSON.stringify(uploadPack));

	//更新合计框内信息
	refreshSum(pack_info);
}
//单个捆包增加时调用
function refreshSum(pack_info) {
	//最近捆包
	$("#recent_pack_id").text(pack_info.pack_id);
	//统计合计信息
	var qty = $("#sum_qty > #sum_number").text();
	if (parseInt(qty) < 1) {
		qty = parseInt(qty) + 1;
	} else {
		qty = parseInt(qty);
	}
	var weight = $("#sum_weight > #sum_number").text();
	weight = parseFloat(weight) + parseFloat(pack_info.putin_weight * 1000);
	//取3位小数
	weight = weight.toFixed(3);
	$("#sum_qty > #sum_number").text(qty);
	$("#sum_weight > #sum_number").text(weight);
	//clearInput();
}

function reSum(packlist, pack_id) {
	var recent_pack_id = $("#recent_pack_id").text();
	var recent = 0;
	var sum_qty = 0;
	var sum_weight = 0;
	var ppid = "";
	$.each(packlist, function(i, value) {
		if (recent_pack_id == value.pack_id) {
			recent = recent + 1;
		}
		sum_qty = sum_qty + 1;
		ppid = value.pack_id
		sum_weight = parseFloat(sum_weight) + parseFloat(value.putin_weight * 1000);
	});
	if (pack_id == "" || pack_id == "undefine" || pack_id == null) {
		if (ppid != "" && ppid != "undefine" && ppid != null) {
			$("#recent_pack_id").text(ppid);
		} else {
			$("#recent_pack_id").text("无");
			$("#pack_id").val("");
			$("#car_num").val("");
			$("#putin_weight").val("");
			$("#spec").val("");
			$("#customer_id").val("");
		}
	} else {
		$("#recent_pack_id").text(pack_id);
	}

	//保留三位小数
	sum_weight = sum_weight.toFixed(3);
	$("#sum_qty > #sum_number").text(sum_qty);
	$("#sum_weight > #sum_number").text(sum_weight);

	//清空信息焦点定位到捆包
	clearInput();
}

function clearInput() {
	//清空信息焦点定位到捆包

	//$("#spec").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#quality_desc").val("");
	$("#driver_name").val("");
	$("#id_card").val("");
	//add by tangli 20191126 ERP_58339 湛江物流入库扫描焦点定义到捆包上
	//天津宝钢和天津宝井，扫描捆包之后焦点在捆包上 20210714 yangzemin

	$("#pack_id").val("");

	//end by tangli 20191126 ERP_58339 湛江物流入库扫描焦点定义到捆包上
	$("#inner_diameter").val("");
	$("#product_process_id").val("");
}
//查看入库捆包清单
mui(document.body).on('tap', '#detail', function() {
	mui.openWindow({
		url: 'putout_pack_record.html',
		id: 'putout_pack_record',
		extras: {
			putinPackList: putinPackList,
			transfer_flag: 0
			//vehicle_id: vehicle_id,

		}
	});
});

//绑定入库清单信息回写事件
window.addEventListener('back', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	//重算合计信息
	reSum(putinPackList, '');
	//从详情直接返回扫描页面
	setDefaultInputStyle();
	clearPreviousLocation(); //清空上一个捆包的库位
});

function clearPreviousLocation() {
	$("#pack_id").val("");
	$("#car_num").val("");
	$("#putin_weight").val("");
	$("#qty").val("");
	$("#customer_id").val("");
	$("#recent_pack_id").val("");
	$("#sum_number").val("");
	$("#spec").val("");
}
//扫描捆包时校验捆包号相关信息(是否在捆包列表中、是否在已扫捆包列表中、是否在未扫捆包列表中)
function checkPackInfo(pack_id) {
	//捆包信息中是否有该捆包信息
	var pack_info = packInfo.get(pack_id);
	if (pack_info == null) {
		/* mui.alert("该" + pack_id + "不在本次出库捆包清单中", "提示", "确定", function() {}, "div");
		return false; */
	}
	//捆包是否重复扫描
	var voucher_id = pack_info.voucher_id;

	//有可能已上传捆包map为null所以先校验是否拿得到单据对应的捆包列表
	if (uploadPack.get(voucher_id) != null) {
		var index1 = getIndexById(pack_id, uploadPack.get(voucher_id));
		console.log("index1" + index1);
		if (index1 != -1) {
			mui.alert("该" + pack_id + "捆包已经扫描,不可重复扫描", "提示", "确定", function() {}, "div");
			return false;
		}
	}


	//捆包是否在未扫捆包信息中
	var index2 = getIndexById(pack_id, unUploadPack.get(voucher_id));
	console.log(JSON.stringify(unUploadPack));
	console.log("index2" + index2);
	if (index2 == -1) {
		/* mui.alert("该" + pack_id + "捆包不在未扫捆包列表中", "提示", "确定", function() {}, "div");
		return false; */
	}

	//所有校验通过
	return true;
}

/**
 * 扫描捆包的时候，判断是否是形式提单，且判断是否超发
 * @param {Object} pack_info
 */
/* 
console.log("pack_info" + JSON.stringify(pack_info));
if (pack_info.advice_style == "20") { //捆包属于形式提单
	//根据订单子项号获取订单子项发货信息
	var voucherSubInfo = contractSubInfo.get(pack_info.voucher_subid);
	console.log("contractSubInfo before:" + JSON.stringify(contractSubInfo));
	//按重量发货
	if (pack_info.price_style == "10") {
		//发货通知重量
		var advice_weight = parseFloat(voucherSubInfo.advice_weight);
		//已出库量
		var act_weight = parseFloat(voucherSubInfo.act_weight);
		//当前捆包重量
		var putin_weight = parseFloat(pack_info.putin_weight);
		//超发量
		var super_weight = (act_weight + putin_weight) - advice_weight;
		console.log("aaaa" + advice_weight + ",    " + act_weight + ",     " + putin_weight + ",   " + super_weight);
		if (super_weight > 0) {
			mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_weight, "提示", "确认", function() {}, "div");
			//return false;
		} else {
			voucherSubInfo.act_weight = parseFloat(act_weight + putin_weight);
			contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
			console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
			//return true;
		}
		//数量
	} else if (pack_info.price_style == "20") { ////按数量发货
		//发货通知数量
		var advice_qty = parseFloat(voucherSubInfo.advice_qty);
		//已出库数量
		var act_qty = parseFloat(voucherSubInfo.act_qty);
		//当前捆包数量
		var putin_qty = parseFloat(pack_info.putin_qty);
		//超发量
		var super_qty = (act_qty + putin_qty) - advice_qty;
		if (super_qty > 0) {
			mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_qty, "提示", "确认", function() {}, "div");
			//return false;
		} else {
			voucherSubInfo.act_qty = parseFloat(act_qty + putin_qty);
			contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
			console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
			//return true;
		}
	}
} else {
	//return true;
} */


//单个捆包增加时调用
function refreshSum(pack_info) {
	//统计合计信息
	var qty = $("#scan_qty").text();
	qty = parseInt(qty) + 1;
	var weight = $("#scan_weight").text();
	weight = parseFloat(weight) + parseFloat(pack_info.putin_weight);
	var wqty = parseInt(pack_total_count) - parseInt(qty);
	var wweight = parseFloat(pack_total_weight) - parseFloat(weight);
	//取3位小数
	weight = weight.toFixed(3);
	wweight = wweight.toFixed(3);
	$("#scan_qty").text(qty);
	$("#scan_weight").text(weight);
	$("#wscan_qty").text(wqty);
	$("#wscan_weight").text(wweight);

}

/* function putoutPackUpload(pack_id) {
	<!--查询前先关闭软键盘-- >
	console.log("packQualityBlock1111>>>>>>>>>>>>>>>>" + advice_num);
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","label_id":"' + pack_id + '"}';
	//var params = initPutoutPackUploadParams();
	var method = "queryStockInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			if (data.saleOutList.length == 0) {
				mui.alert("未查询到对应的捆包信息", "提示", "确定", function() {
					$("#pack_id").val("");
					$("#pack_id").focus();
				}, 'div');
				return;
			} else {
				product_id = data.saleOutList[0].product_id;
				var factory_product_id = data.saleOutList[0].factory_product_id;
				var spec = data.saleOutList[0].spec;
				var putin_weight = data.saleOutList[0].putin_weight;
				var customer_id = data.saleOutList[0].customer_id;
				var packId = data.saleOutList[0].pack_id;
				//钢厂资源号
				$("#car_num").val(factory_product_id);
				//规格
				$("#spec").val(spec);
				$("#packId").val(packId);
				//重量
				$("#putin_weight").val(putin_weight);
				//客户
				$("#customer_id").val(customer_id);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
} */

//初始化上传出库捆包参数
/*function initPutoutPackUploadParams() {
	var params = {};
	params['seg_no'] = segNo;
	params['user_id'] = user_id;
	params['team_id'] = team_id;
	params['work_shift'] = work_shift;
	params['operatorType'] = operatorType;
	if (vehicle_id == null || vehicle_id == "null") {
		vehicle_id = "";
	}
	params['vechile_id'] = vehicle_id;
	//tangli  add
	params['putout_confirm_flag'] = putout_confirm_flag;
	//add by penglie  2018-10-26 18:21:40 佛宝将配车单写到出库中间表
	if (segNo == "00126") {
		if (allocate_vehicle_id != null && allocate_vehicle_id != "null") {
			params['allocate_vehicle_id'] = allocate_vehicle_id;
		}
	} else {
		params['allocate_vehicle_id'] = allocate_vehicle_id;
	}

	var voucherList = new Array();
	var voucherIdList = uploadPack.keySet();
	$.each(voucherIdList, function(i) {
		if (uploadPack.get(voucherIdList[i]).length > 0) { //该单据下有捆包要出库
			var voucher = {};
			//从出库单据列表中取单据验证码
			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i], putoutVoucherList)])
				.identify_code;
			console.info("identify_codeidentify_codeidentify_code::" + identify_code);
			console.log("identify_code:" + identify_code);
			voucher['identify_code'] = identify_code;
			voucher['voucher_id'] = voucherIdList[i];
			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
			//往单据列表中添加信息
			voucherList.push(voucher);
		}
	});
	params['voucherList'] = voucherList;

	return params;
}*/

//绑定上传按钮点击事件
mui(document.body).on('tap', '#query_button', function() {
	//add by gll
	if (putinPackList.length == 0) {
		//mui.alert("已扫描捆包列表无记录"," ","确定",function(){},'div');
		plus.nativeUI.toast("未添加扫描记录。请检查");
		$("#pack_id").val("");
		$("#pack_id")[0].focus();
		mui("#query_button").button('reset');
		return false;
		}
	console.log("55" + JSON.stringify(labelInfo['map']));
	console.log("labelInfo:" + JSON.stringify(putinPackList));
	$.each(putinPackList, function(i, value) {
		console.log("5578857" + value.pack_id);
	})

	var index = 0;
	var packIds = new Array();
	var upload = uploadPack.keySet();
	var pack = "";

	$.each(putinPackList, function(j, jitem) {
		pack = jitem.pack_id;
		console.log("捆包號=======" + pack);
		packIds[index++] = pack;


		console.log("捆包==---=======" + JSON.stringify(packIds));
		console.log("捆包=6595=---=======" + pack.pack_id);
		console.log("循环=---=======" + allocate_vehicle_id);
	});



	//console.log("labelInfo25:" + JSON.stringify(putoutPackList));
	//end by gll
	// packId += packList+']}';
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var pack_id = $("#packId").val();
	var mark = "通过pda 捆包检查";
	var segNo = localStorage.getItem("segNo");
	var params = '{"seg_no":"' + segNo + '","pack_id":' + JSON.stringify(packIds) + ',"mark":"' + mark +
		'","vehicle_id":"' + allocate_vehicle_id + '","checker":"' + user_id + '"}';
	//console.log(params);
	var method = "updatePackKunbao";
	//console.log(webServiceUrl);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		//console.log("查询捆包返回：" + JSON.stringify(data));
		if (null != data) {
			if (data.returnStatus == "1") {
				mui.alert("上传完成", "提示", "确定", function() {
					mui.openWindow({
						id: "putout_scan_new",
						url: "putout_scan_new.html",
					});
				}, 'div');
				var putinPackList1 = new Array();
				putinPackList1 = putinPackList;
				putinPackList = [];
			} else if (data.returnStatus == "2") {
				mui.alert(data.returnDesc, "提示", "确认", function() {}, "div");
				console.log(data.returnDesc);
			} else {
				mui.alert(data.returnDesc, "提示", "确认", function() {}, "div");
				var putinPackList1 = new Array();
				putinPackList1 = putinPackList;
				putinPackList = [];
				console.log(putinPackList.length);
				$("#recent_pack_id").html("无");
				$("#sum_qty > #sum_number").text(0);
				$("#sum_weight > #sum_number").text(0);
				//清空信息焦点定位到捆包
				clearInput();
				console.log(data.returnDesc);
				//清空信息焦点定位到捆包
				$("#pack_id").val("");
				$("#car_num").val("");
				$("#putin_weight").val("");
				$("#spec").val("");
				$("#customer_id").val("");

				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确认", function() {}, "div");
		}
	});
});
mui.back = function(){
	//console.log("transfer_flag:" + transfer_flag);
	
		mui.fire(putinScanPage,'back',{
			putinPackList: putinPackList,
			packtypes : 1
		});
		putinScanPage.show();
	console.log("transfer_flag:" + JSON.stringify(putinPackList));
	//var packListPage = plus.webview.getWebviewById("putin_pack_list");
	//packListPage.show();
	//putinScanPage.show();
	//packListPage.close();
}

function showPackList(packList){
	console.log("捆包检查————————showPackList："+packList);
	if(packList != null && packList !=undefined){
	if(packList.length > 0){
		var packInfo = packList[packList.length -1];
		var packId = packInfo.pack_id;
		console.log("捆包检查————————showPackList："+JSON.stringify(packInfo));
		reSum(packList,packId);
	}
	}
	
	
}