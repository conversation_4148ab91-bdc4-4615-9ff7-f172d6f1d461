<!doctype html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>生产领料</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="lingliao.css" type="text/css" />
	</head>

	<body>

		<div class="container">

			<div class="head">
				<div class="mui-bar mui-bar-nav">
					<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
					<h4 class="mui-title"></h4>
				</div>
			</div>


			<div class="content">

				<div id="gongdan-div">
					<input id="gongdan-input" type="text" placeholder="工单号" value="" />
					<img id="gongdan-search" src="../../resource/search.png"></img>
				</div>
				<div class="input-div">
					<input id="pack-id-input" type="text" placeholder="捆包号" value="" />
				</div>

				<span>捆包信息</span>

				<div class="pack-info">
					<div class="pack-label">
						<span id="pack-weight" class="left">重量：</span>
						<span id="pack-count" class="right">数量：</span>
					</div>

					<div class="pack-label">
						<span id="factory_id" class="left">钢厂资源号：</span>
						<span id="shopsign" class="right">牌号：</span>
					</div>

					<div class="pack-label">
						<span id="spec" class="left">规格：</span>
					</div>

					<div class="pack-label">
						<span id="warehouse_id" class="left">仓库：</span>
						<span id="variety" class="right">品种：</span>
					</div>
				</div>
				
				
				<span id="platpicker-title">料台信息</span>
				
				<div class="platpicker-info">
					<div class="platpicker-label">
						<span id="platpicker" class="left">料台号：</span>
						<span id="platnumpicker" class="right">垛位号：</span>
					</div>
				</div>
				
				<!-- <span id="table-span">料台号</span>

				<div id="table-div" class="table">
					<ul id="table-ul" class="mui-table-view mui-table-view-radio">
						<li class="mui-table-view-cell">
							<a class="mui-navigate-right">
								<label id="table_name">料台1</label>
							</a>
						</li>
					</ul>
				</div> -->

				<span>成品规格信息</span>

				<div class="table">
					<ul id="spec-ul" class="mui-table-view mui-table-view-radio">
						<!-- <li class="mui-table-view-cell">
							<a class="mui-navigate-right">
								<label id="spec_name">成品规格1：1.0*1056*2.0</label>
							</a>
						</li> -->
					</ul>
				</div>


			</div>

			<div class="bottom">
				<button id="commit_btn" type="button" class="mui-btn mui-btn-blue" style="width: 45%;">提交</button>
			</div>
			<div class="bottom3">
				<div class="bottom1">
					<button id="change_platpicker_btn" type="button" class="mui-btn mui-btn-blue" >切换料台</button>
				</div>
				<div class="bottom2">
					<button id="change_platnumpicker_btn" type="button" class="mui-btn mui-btn-blue" >切换垛位</button>
				<div class="bottom">
			</div>

		</div>

		<div id="pick-dialog-div">
			<div class="title">请选择工单</div>
			<div style="margin: 5px; height: 270px; overflow: auto; " id="ProductProcessIdInfo">
				<ul id="work-order-list" class="mui-table-view mui-table-view-radio" />
			</div>
			<div class="mui-input-row" style="margin-top: 1px;">
				<button id="picked-confirm" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">确认</button>
				<button id="picked-cancel" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">取消</button>
			</div>
		</div>


		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/pda/putin.js"></script>
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script src="lingliao.js" type="text/javascript" charset="utf-8"></script>
	</body>

</html>
