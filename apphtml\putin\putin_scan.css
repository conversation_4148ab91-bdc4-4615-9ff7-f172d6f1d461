/** 按钮样式 */


/*当前页面有未处理的数据,确认退出?*/

#edit {
	width: 48%;
	font-size: 14px;
	line-height: 1.8;
	/* float: left; */
	margin: 5px 4px 0px 0px;
}

#putin {
	width: 48%;
	font-size: 14px;
	line-height: 1.8;
	margin: 5px 0px 0px 2px;
}


/** 明细样式 */

.detail_row {
	height: 41px;
}

.mui-search {
	height: 41px;
}

.mui-input-row span {
	top: 10px!important;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}
.text-small {
	float: left;
	width: 22%;
	font-size: 20px;
	padding: 10px 0px;
	text-align: center;
}

#pack_location,
#pack_id,
#spec,
#factory_product_id,
#weight,
#qty,
#inner_diameter,
#quality_desc,
#driver_name,
#id_card,
#damage_input,
#product_process_id {
	width: 78%;
	padding: 0px 5px;
	font-size: 20px;
}


/** 合计框样式 */

.sum {
	border: solid 1px #CBCBCB;
	border-radius: 5px;
	font-size: 22px;
}


/** 合计显示样式 */

.sum_title {
	margin: 8px;
}


/** 具体合计信息样式 */

#sum_qty,
#sum_weight {
	padding: 0px 0px;
	margin: 0px;
}


/** 数字部分 */

#sum_number {
	width: 78%;
	float: left;
	color: blue;
	text-align: right;
	margin-right: 10px;
}


/** 文字部分 */

#sum_text {
	text-align: right;
}

#InnerDiameterDiv.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#InnerDiameterDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#InnerDiameterDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

.mui-input-row .mui-icon-search {
	font-size: 30px;
	position: absolute;
	z-index: 1;
	/*top: 10px;*/
	right: 0;
	width: 38px;
	/*height: 30px;*/
	/*text-align: center;*/
	color: #999;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	/*filter: alpha(opacity=50); /* IE的透明度 
			    
			    display: none;
			   
			    top: 0px;
			    left: 0px;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 999;
	/* 此处的图层要大于页面 */
	/*display:none;*/
}

.icon-setting {
	position: absolute;
	left: 220px;
	top: 10px;
	z-index: 5;
	background-image: url(../resource/setting.gif);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}


/*下一步*/

#next_step {
	width: 99%;
	font-size: 16px;
	line-height: 1.8;
	float: left;
	margin: 2px;
}

#wprovider_name_span {
	color: #000000;
	font-weight: bold;
	margin-left: 5px;
	font-size: 16px;
	margin-top: 6px;
}

#vehicle_no_span {
	color: #000000;
	font-weight: bold;
	float: right;
	font-size: 16px;
	margin-left: 175px;
	/* margin-top: -20px; */
	margin-top: 6px;
}

.icon-car {
	position: absolute;
	left: 270px;
	top: 10px;
	z-index: 5;
	background-image: url(../resource/car.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}


/** 弹出图层设置 */

#pop_pack_info {
	position: absolute;
	z-index: 999;
	width: 220px;
	height: 200px;
	left: 50%;
	top: 50%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pop_pack_info.show {
	visibility: visible;
	opacity: 1;
}

#pop_pack_info>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

.pop_detail {
	margin: 6px 4px;
}

span.icon {
	background-color: #3D9EE5;
	color: white;
	margin-right: 4px;
}

span.icon-1 {
	background-color: #800080;
	color: white;
	margin-right: 4px;
}

#pop_bg {
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 998;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, .3);
	visibility: hidden;
	opacity: 0;
}

#pop_bg.show {
	visibility: visible;
	opacity: 1;
}


/* add by lal*/

#ProductProcessIdDiv.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#ProductProcessIdDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#ProductProcessIdDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}
.gqg{
	display: none;
}
.damage{
	display: none;
}

#damageTypePickerDiv.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#damageTypePickerDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#damageTypePickerDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}


#unload {
	width: 99%;
	font-size: 16px;
	line-height: 1.8;
	float: left;
	margin: 2px;
	display: none;
}
