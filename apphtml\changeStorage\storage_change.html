<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>库位变更</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="storage_change.css" />
	</head>

	<body>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(-1)" style="color: white;"><i id="back" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">库位变更</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;">
				<!-- 具体内容 -->
				<!-- 库位(目标) -->
				<div class="mui-input-row detail_row">
					<div class="fourtext" style="color: red;">库位(新)</div>
					<div> <input id="pack_location_m" type="text" class="mui-input-clear" > </div>
				</div>

				<!-- 捆包 -->
				<div class="detail_row">
					<div class="fourtext">捆&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;包</div>
					<div> <input id="pack_id" type="text" class="mui-input-clear" > </div>
				</div>

				<!-- 库位(当前) -->
				<div class="detail_row">
					<div class="fourtext" style="color: blue;">库位(旧)</div>
					<div> <input id="pack_location_d" type="text" class="mui-input-clear" readonly="true"> </div>
				</div>
				<!-- 成品扫描 原料扫描 -->
				<div id="cpsm" class="mui-input-row detail_row">
					<div class="fourtext">成品扫描</div>
					<div id="mySwitch" class="mui-switch mui-active">
							<div class="mui-switch-handle" style="float:left;"></div>
					</div>
				</div>
				<!-- 捆包信息 -->
				<div class="pack_list" overflow: auto;>
					<ul class="mui-table-view" id="storage_list" >
					</ul>
				</div>
                <div style="margin-left:10px;margin-top:5px;font-size: 16px;">共计&nbsp;<span id="bale_count" style="color:red;font-size: 20px;font-weight: bolder;">0</span>&nbsp;个捆包</div>
				<!-- 按钮 -->
				<div class="mui-input-row">
					<button id="storage_but" type="button" class="mui-btn mui-btn-primary">倒&nbsp;&nbsp; &nbsp; &nbsp;库</button>
					<button id="storage_realloc" type="button" class="mui-btn mui-btn-primary">库位推荐</button>
				</div>
			</div>

		</div>
		
		<div id = "ReallocLocationDiv">
			<div class="title">请选择推荐库位</div>  
			<div style="margin: 5px; height: 170px; overflow: auto; " id="LocationInfo">
				<ul id="LocationList" class="mui-table-view mui-table-view-radio"/>
			</div>
			<div class="mui-input-row" style="margin-top: 1px;">
				<button id="LocationConfirm" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">确认</button>
				<button id="cancel" type="button" class="mui-btn mui-btn-primary" style="width: 50%;font-size: 15px; line-height: 1.8;">取消</button>
			</div> 
		</div>
		
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/pda/time.js" type="text/javascript" charset="utf-8"></script>
		<script src="../../js/util/public.js"></script>
		<script src="storage_change.js"></script>
	</body>

</html>