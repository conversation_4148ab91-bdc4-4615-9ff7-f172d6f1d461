/**
 * 初始化变量信息 
 */
var product_id = "";
var quality_grade_arr;
$(function(){
	mui.init({
		swipeBack:false //启用右滑关闭功能
	});
	//获取质量等级集合
	setquality_gradeData();
});

mui.plusReady(function(){
	$("#pack_id").focus();
});
	
//捆包号
$("#pack_id").keypress(function(e){
	if(e.keyCode == 13){
		var pack_id = $("#pack_id").val();
		//add by xuhuaijun 20161229 格式化捆包号
		pack_id = formatPackId(pack_id);
		$("#pack_id").val(pack_id);
		 //return false;
		 if(pack_id==null || pack_id==""){
		 	mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
		 	$("#pack_id").focus();
		 	return;
		 }
		 queryQualityRejudgePack(pack_id);
	}
});

document.querySelector('#new_quality_grade_name').addEventListener("tap", function() {
	var roadPick = new mui.PopPicker();//获取弹出列表组建，假如是二联则在括号里面加入{layer:2}
	console.info("quality_grade_arr:"+JSON.stringify(quality_grade_arr));
	if(!quality_grade_arr){
		mui.alert("没有配置质量等级数据，请联系管理员", "提示", "确定", function() {
		}, 'div');
	} 
	roadPick.setData(quality_grade_arr);
  	roadPick.show(function(item) {//弹出列表并在里面写业务代码
//					var itemCallback=roadPick.getSelectedItems();  
//					console.info(itemCallback[0].text);
//					$('#new_quality_grade_name').val(itemCallback[0].text);
//					$('#new_quality_grade').val(itemCallback[0].value);
   	});
});
//按钮绑定事件
mui(document.body).on('tap','#block_button',function(){
	var pack_id = $("#pack_id").val();
	if(pack_id==null || pack_id==""){
		mui.alert("请扫描或者输入捆包号","提示","确定",null,'div');
		$("#pack_id").focus();
		return;
	}
//				var quality_desc = $("#quality_desc").val();
//				if(quality_desc == "" || quality_desc == null){
//					mui.alert("请输入改判原因","提示","确定",null,"div");
//					$("#quality_desc").focus();
//					return;
//				}
//				if(product_id=="" || product_id == null){
//					mui.alert("手输捆包号之后请点击回车","提示","确定",null,"div");
//					$("#pack_id").focus();
//					return; 
//				}
	mui("#block_button").button('loading');
	$("#overlay").addClass("overlay");
	exePackQualityRejudgeUpload();
});

//查询牌号
mui(document.body).on('tap','#query_button',function(){
	var new_shopsign = $("#new_shopsign").val();
	mui("#block_button").button('loading');
	$("#overlay").addClass("overlay");
	exePackShopsign();
});

//捆包扫描方法
function queryQualityRejudgePack(pack_id) {
	<!-- 查询前先关闭软键盘-->
	//console.log("packQualityBlock1111>>>>>>>>>>>>>>>>");
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
//				var segNo = "00112";
//				var webServiceUrl = "10.30.184.231:7001";
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '"}';
	var method = "exeQueryRejudgePackInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if(data != null) {
			if(data.packList.length == 0){
				 mui.alert("未查询到对应的封闭捆包信息", "提示", "确定", function() {
				 	$("#pack_id").val("");
				 	$("#pack_id").focus();
				}, 'div');
				return ;
			}else{
				product_id = data.packList[0].product_id;
				var quality_grade =  data.packList[0].quality_grade;
				var quality_grade_name =  data.packList[0].quality_grade_name;
				var shopsign =  data.packList[0].shopsign;
				$("#old_quality_grade").val(quality_grade);
				$("#old_quality_grade_name").val(quality_grade_name);
				$("#new_quality_grade").val(quality_grade);
				$("#new_quality_grade_name").val(quality_grade_name);
				$("#old_shopsign").val(shopsign);
				$("#new_shopsign").val(shopsign);
				$("#quality_desc").focus();
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {
			}, 'div');
			return ;
		}
	});
}

//执行捆包封闭
function exePackQualityRejudgeUpload() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
//				var segNo = "00112";
//				var webServiceUrl = "10.30.184.231:7001";
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var pack_id = $("#pack_id").val();
//	var quality_desc = $("#quality_desc").val();
	$("#quality_desc").val($("#quality_desc").val().replace(/[\r\n]/g,""));//去掉回车换行
	var quality_desc = $("#quality_desc").val();
	var quality_grade = $("#new_quality_grade").val();
	var shopsign = $("#new_shopsign").val();
	var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","pack_id":"'+pack_id+'","remark":"'+quality_desc+'","new_quality_grade":"'+quality_grade+'","new_shopsign":"'+shopsign+'"}';
	params= encodeURI(params,'utf-8');
	var method = "exeRejudgePack";
	console.info("params:"+params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			mui("#block_button").button('reset');
			$("#overlay").removeClass("overlay");
			if(data.resultStatus == "1") {
				  mui.alert("改判成功", "提示", "确定", function() {
					  $("#pack_id").val("");
					  $("#old_quality_grade").val("");
					  $("#old_quality_grade_name").val("");
					  $("#new_quality_grade").val("");
					  $("#new_quality_grade_name").val("");
					  $("#old_shopsign").val("");
					  $("#new_shopsign").val("");
					  
					  $("#quality_desc").val("");
					  
					  product_id = "";
				      $("#pack_id").focus();
				}, 'div');
				return ;
			} else {
				var errorinfo = "";
				if(data.resultStatus != "0"){
					errorinfo = data.resultStatus;
				}else{
					errorinfo = data.resultDesc;
				}
				 mui.alert("改判失败！原因："+errorinfo, "提示", "确定", function() {
					  $("#pack_id").val("");
					  $("#quality_desc").val("");
					  product_id = "";
				      $("#pack_id").focus();
				}, 'div');
				 return ;
			}
		} else { //连接失败
			mui("#block_button").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("连接服务器异常", "提示", "确定", function() {
			}, 'div');
			return ;
		}
	});
}



function setquality_gradeData(){
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","code_type":"QUALITY_GRADE"}';
	var method = "QueryCodeVlue";
	console.info("params:"+params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method 
	}, function(data) { //如返回对象有一个username属性
		if(data != null) {
			if(data.resultStatus == "1") {
				quality_grade_arr = data.codeList;
			} else {
				mui.alert("没有配置质量等级数据，请联系管理员", "提示", "确定", function() {
				}, 'div');
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {
			}, 'div');
			return ;
		}
	});
	
}