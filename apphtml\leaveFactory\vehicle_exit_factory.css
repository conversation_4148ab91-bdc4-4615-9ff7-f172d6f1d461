/** 按钮样式 */
#vehicle_normal_leave {
	width: 100%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 4px 0px 0px;
}

#vehicle_unusual_leave {
	width: 100%;
	font-size: 22px;
	line-height: 1.8;
	margin: 5px 0px 0px 2px;
}

.mui-control-content {
	background-color: white;
	min-height: 280px;
}

.mui-segmented-control {
	font-size: 22px;
}

.mui-table-view-cell {
	font-size: 22px;
	padding: 6px 0px 8px 12px;
	text-align: left;
	width: 100%;
}
/** 明细样式 */

.left {
	float: left;
	width: 50%;
}

.mui-navigate-right:after {
	content: '';
}

.select:after {
	content: '\e472';
	color: red;
	font-size: 50px;
	font-weight: 600;
	right: 10px;
}

.icon {
	background-color: #EC971F;
	color: white;
	margin-right: 6px;
}

.row {
	margin: 5px;
	width: 49%;
	font-size: 18px;
}

#vehicle_no{
	font-size: 22px;
	margin-bottom: 4px;
	padding: 6px 0px 0px 0px;
	color: blue;
}

#status {
	font-size: 16px;
	width: 55%;
}

#status span{
	background-color: grey;
	color: white;
	margin-right: 6px;
}