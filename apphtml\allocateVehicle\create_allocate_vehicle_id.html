<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" type="text/css" href="../../css/mui.picker.min.css" />
		<link rel="stylesheet" type="text/css" href="create_allocate_vehicle_id.css" />
		<title>配车单新增</title>
	</head>

	<body>
		<div class="mui-bar mui-bar-nav">
			<i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left" style="color: white;"></i>
			<!--</a>-->
			<h4 class="mui-title">新增配车单</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;">
				<!--<div class="mui-input-row detail_row">
					配车单来源
					<div class="text" style="font-size: 19px;">配车单来源</div>
					<div> <input id="allocate_from" type="text" readonly="true" class="mui-input-clear" placeholder="PDA" style="background-color: #CCCCCC;"> </div>
				</div>-->
				<!--车牌号-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">车&nbsp;&nbsp;&nbsp;牌&nbsp;&nbsp;&nbsp;号</div>
					<div>
						<!--<select id="vehicle_id" class="mui-select pill" onchange="vehicle_id_change()">
						</select>-->
						<div> <input id="vehicle_id" type="text" class="mui-input-clear" readonly="true"> </div>
					</div>
				</div>
				<!--装卸类型-->
				<div>
					<div class="text">装卸类型</div>
					<select id="hand_type">
					</select>
				</div>
				<!--承运商代码-->
				<div class="mui-input-row detail_row" hidden="hidden">
					<div class="text" style="font-size: 19px;">承运商名称</div>
					<div>
						<select id="customer_name" class="mui-select pill" onchange="customer_name_change()">

						</select>
					</div>
				</div>
				<!--承运商名称-->
				<div class="mui-input-row detail_row" hidden="hidden">
					<div class="text" style="font-size: 19px;">承运商代码</div>
					<div> <input id="customer_id" type="text" readonly="true" style="background-color: #CCCCCC;"
							class="mui-input-clear"> </div>
				</div>
				<!--车辆到达时间-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 17px;">车辆到达时间</div>
					<input id="car_arrive_time" type="text" readonly="true" />
				</div>
				<!--交货方式-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">交货方式</div>
					<select id="deliver_type" onchange="deliver_type_change()">
						<option value="0">--请选择--</option>
						<option value="10">自提</option>
						<option value="20">代运</option>
						<option value="30">恰运</option>
					</select>
				</div>
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">配单维度</div>
					<select name="sel" id="peidan_type" style="background-color:#fff;" onchange="peidan_type_change()">
						<option value="10">按单据号</option>
						<option value="20">按捆包号</option>
					</select>
				</div>

				<!--驾驶员姓名-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">驾驶员姓名</div>
					<!--<select id="driver_name" class="mui-select pill" onchange="driver_name_change()">
						</select>-->
					<div> <input id="driver_name" type="text" class="mui-input"><i class="icon-search"></i> </div>
				</div>
				<!--身份证号-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">身份证号</div>
					<div> <input id="id_card" type="text" class="mui-input-clear"> </div>
				</div>
				<!--手机号-->
				<div class="mui-input-row detail_row">
					<div class="text" style="font-size: 19px;">手&nbsp;&nbsp;&nbsp;机&nbsp;&nbsp;&nbsp;号</div>
					<div> <input id="phone_number" type="text" class="mui-input-clear"> </div>
				</div>
			</div>
		</div>
		<!-- 按钮 -->
		<div>
			<button id="btn_new_sub" type="button" class="mui-btn mui-btn-primary" hidden="hidden">新增子项</button>
		</div>
		<div id="InnerVehicleDiv">
			<div class="title">车牌选择</div>
			<div>
				<button id="province_name">粤</button>
				<input id="car_num" type="text" oninput="getSelectionVehicleParams()"
					style="margin-left:3px;width:70%;float:left;" class="mui-input-clear" />
			</div>
			<div class="mui-content" style="margin-top: 50px; margin-bottom: 10px; margin-left: 7px;">
				<div id="InnerVehicleInfo"
					style="border-radius: 3px;border: 1px solid #CCCCCC;height: 130px;overflow-y: auto;">
					<ul class="mui-table-view mui-table-view-radio" id="vehicle_list">
					</ul>
				</div>
			</div>
			<div class="mui-input-row" style="margin-top: 1px;">
				<button id="innerVehicleConfirm" type="button" class="mui-btn mui-btn-primary"
					style="width: 50%;font-size: 15px; line-height: 1.8;">确认</button>
				<button id="innerVehicleCancel" type="button" class="mui-btn mui-btn-primary"
					style="width: 50%;font-size: 15px; line-height: 1.8;">取消</button>
			</div>
		</div>
		<div id="province">
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">粤</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">桂</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">琼</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">湘</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">赣</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">闽</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">浙</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">沪</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">川</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">贵</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">云</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">渝</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">鄂</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">皖</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">苏</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">鲁</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">豫</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">陕</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">甘</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">青</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">宁</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">晋</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">冀</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">京</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">津</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">藏</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">新</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">蒙</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">辽</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">吉</button>
			<button id="button_province" type="button" class="mui-btn" onclick="chooseProvince(this)">黑</button>
			<!--<button id="confirm_province" type="button" class="mui-btn mui-btn-primary">确认</button>-->
		</div>
		<div id="pop_car"></div>

		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/mui.picker.min.js"></script>
		<script src="../../js/util/public.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="create_allocate_vehicle_id.js"></script>

	</body>

</html>
