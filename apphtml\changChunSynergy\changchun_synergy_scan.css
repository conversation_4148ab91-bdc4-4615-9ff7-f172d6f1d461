/** 明细样式 */

#storage_but,
#storage_realloc {
	width: 99%;
	font-size: 22px;
	line-height: 1.8;
	margin: 10px 0px 5px 1px;
}

.detail_row {
	height: 45px;
}

.text {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}


/**四个字的 */

.fourtext {
	float: left;
	width: 22%;
	font-size: 22px;
	padding: 10px 0px;
	text-align: center;
}

#pack_id,
#spec,
#pack_location_m,
#product_process_id {
	width: 78%;
	padding: 0px 5px;
	font-size: 20px;
}

.pack_list {
	height: 156px;
	overflow: auto;
	border: 1px solid #AAAAAA;
}

.storage_pack {
	width: 60%;
	margin-top: 12px;
	text-align: left;
	padding-left: 4px;
}

.li-text {
	font-size: 20px;
	padding: 10px 0px;
	text-align: center;
	border-bottom: 1px solid #CBCBCB;
	height: 77px;
}

.li-text p span font {
	text-align: center;
	font-size: 20px;
	color: #000000;
}

.item a {
	display: block;
	float: left;
	background-color: #ff0000;
	color: #fff;
	width: 20%;
	margin-left: 0.16rem;
	font-size: 0.14rem;
	text-align: center;
	text-decoration: none;
}

.li-height {
	margin-top: 4px;
	text-align: left;
}


/** 列表样式 */

.pack_location_target {
	color: black;
	font-size: 20px;
	margin-right: 5px;
}

.pack_location_now {
	color: black;
	font-size: 20px;
	margin-top: 8px;
	margin-right: 5px;
}

.mui-btn-red {
	font-size: 22px;
}


/* 半透明的遮罩层 */

.overlay {
	background-color: #777777;
	opacity: 0.5;
	/* 透明度 */
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99999;
	/* 此处的图层要大于页面 */
}

#packLabel {
	background-color: darkblue;
	color: white;
	font-size: 20px;
	margin-right: 2px;
}

#LocationLabel {
	background-color: forestgreen;
	color: white;
	font-size: 20px;
	margin-right: 5px;
}

.sum {
	border: solid 1px #CBCBCB;
	border-radius: 5px;
	font-size: 22px;
}


/** 合计显示样式 */

.sum_title {
	margin: 8px;
}


/*
			 * comment by lal 
			 */

.icon-setting {
	position: absolute;
	left: 220px;
	top: 10px;
	z-index: 5;
	background-image: url(../../resource/setting.gif);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}

.icon-car {
	position: absolute;
	left: 270px;
	top: 10px;
	z-index: 5;
	background-image: url(../../resource/car.png);
	/*引入图片图片*/
	background-repeat: no-repeat;
	/*设置图片不重复*/
	background-position: right;
	/*图片显示的位置*/
	width: 40px;
	/*设置图片显示的宽*/
	height: 40px;
	/*图片显示的高*/
	right: 4px;
}

#wprovider_name_span {
	color: #000000;
	font-weight: bold;
	font-size: 16px;
	margin-left: 5px;
	margin-top: 20px;
}

#vehicle_no_span {
	color: #000000;
	font-weight: bold;
	font-size: 16px;
	/*
				 * float: left; 
				 */
	float: left;
	margin-left: 180px;
	margin-top: -21px;
}

.mui-search {
	height: 3px;
}

span.icon {
	background-color: #3D9EE5;
	color: white;
	margin-left: -10%;
}

#ProductProcessIdDiv.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#ProductProcessIdDiv {
	position: absolute;
	z-index: 999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	opacity: 0;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#ProductProcessIdDiv>.title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

.mui-input-row .mui-icon-search {
	font-size: 24px;
	position: absolute;
	z-index: 1;
	top: 10px;
	right: 0;
	width: 42px;
	/*text-align: left;*/
	color: #999;
}


/** 合计切换子页显示样式 */

.sum_title {
	margin: 8px;
}


/** 具体合计信息样式 */

#sum_qty,
#sum_weight {
	padding: 0px 0px;
	margin: 0px;
}

#sum_number {
	width: 78%;
	float: left;
	color: blue;
	text-align: right;
	margin-right: 10px;
}


/** 文字部分 */

#sum_text {
	text-align: right;
}