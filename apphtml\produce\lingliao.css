.container {
	display: -webkit-flex;
	display: flex;
	height: 100%;
	flex-direction: column;
}

.container .bottom {
	display: flex;
	display: -webkit-flex;
	flex-direction: row;
	justify-content: center;
	margin-top: 20px;
	padding-bottom: 20px;
}

.content {
	display: flex;
	display: -webkit-flex;
	flex: 1;
	flex-direction: column;
	overflow-y: scroll;
}

.container span{
	margin-left: 12px;
	font-size: 16px;
	color: #222222;
}

.container input{
	font-size: 16px;
	color: #222222;
}

#gongdan-div {
	display: -webkit-flex;
	margin-top: 12px;
	/* Safari */
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	width: 100%;
	padding-left: 12px;
	padding-right: 12px;
	padding-top: 0;
	padding-bottom: 0;
}

#gongdan-input {
	flex: 1;
	height: 45px;
	margin: 0;
}

#gongdan-search {
	margin-left: 8px;
}

.input-div {
	width: 100%;
	margin-top: 12px;
	padding-left: 12px;
	padding-right: 12px;
}

#pack-id-input {
	height: 45px;
}

	
.pack-info{
	background-color: white;
	border-radius: 4px;
	margin: 12px;
	padding-bottom: 8px;
	padding-right: 10px;
}

.pack-label{
	display: flex;
	display: -webkit-flex;
	flex-direction: row;
	margin: 8px 0 0 0 ;
	align-items: baseline;
}

.pack-label .left {
	flex: 1;
	font-size: 14px;
	color: #222222;
}

.pack-label .right {
	margin-left: 6px;
	font-size: 14px;
	color: #222222;
}

.table{
	background-color: white;
	border-radius: 4px;
	margin: 12px;
	min-height: 90px;
}

.table label{
	font-size: 14px;
}

#pick-dialog-div.show {
	visibility: visible;
	opacity: 1;
}


/** 弹出图层设置 */

#pick-dialog-div {
	position: absolute;
	z-index: 9999;
	width: 270px;
	/*height: 245px;*/
	left: 42%;
	top: 40%;
	margin-left: -100px;
	margin-top: -122px;
	border-radius: 10px;
	background: #FFFFFF;
	box-shadow: 0px 10px 12px rgba(0, 0, 0, .4);
	/** 动画效果 */
	visibility: hidden;
	/** 文字效果 */
	font-size: 20px;
	text-align: left;
}

#pick-dialog-div .title {
	text-align: center;
	padding: 8px 0px;
	font-size: 22px;
	border-bottom: 1px solid;
	border-color: #D8D8D8;
}

#table_value{
	visibility: hidden;
	display: none;
}


.container .bottom1 {
	float: left;
	margin-left: 10px;
}


.container .bottom1 {
	float: right;
	margin-right: 10px;
}


.container .bottom3 {
	margin-left: 5%;
}


.platpicker-info{
	background-color: white;
	border-radius: 4px;
	margin: 12px;
	padding-bottom: 8px;
	padding-right: 10px;
}


.platpicker-label{
	display: flex;
	display: -webkit-flex;
	flex-direction: row;
	margin: 8px 0 0 0 ;
	align-items: baseline;
}


.platpicker-label .left {
	flex: 1;
	font-size: 14px;
	color: #222222;
}

.platpicker-label .right {
	margin-left: 6px;
	font-size: 14px;
	color: #222222;
}

