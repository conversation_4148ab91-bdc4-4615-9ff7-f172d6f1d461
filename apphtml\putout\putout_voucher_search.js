/**
 * 初始化变量信息 
 */

var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var opt_type = ""; //业务类型
var restrict_date = ""; //时间范围
var customer_id = ""; //客户代码
var putoutVoucherList = new Array(); //出库单据信息
var queryVoucherList = new Array(); //查询出的单据信息

var factory_area_id = localStorage.getItem("factory_area_id"); //厂区
var factory_wprovider = ""; //localStorage.getItem("factory_wprovider");//厂区对应的仓库
var wprovider_id = localStorage.getItem("wprovider_id");
var maen_me = localStorage.getItem("name");
$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});

	if(segNo == "00166") { //湛江物流00166增加湛江出库、湛江返厂 按钮
		var html = $("#opt_type").html();
		html = html + '<button id="btn_zj_putout" type="button" class="mui-btn" data="ZJ_PUTOUT">湛江出库</button> <button id="btn_zjfc" type="button" class="mui-btn" data="ZJFC">湛江返厂</button> <button type="button" class="mui-btn" data="ZJXHCK">现货出库</button>';
		$("#opt_type").html(html);
	}

	if(segNo == "00137" || segNo == "00138") { //上海宝井高强钢增加股份出厂 按钮
		var html = $("#opt_type").html();
		html = html + '<button id="btn_gf_putout" type="button" class="mui-btn" data="GF_PUTOUT">股份出厂</button>';
		$("#opt_type").html(html);
	}
	console.log("maen_me:" + maen_me);
	//				if(segNo=="00138" && maen_me=="putout_scan"){
	//					factory_wprovider=wprovider_id;
	//				}
});

window.onload = function onload() {
	mui.plusReady(function() {
		opt_type = plus.webview.currentWebview().opt_type;
		voucher_count_max = plus.webview.currentWebview().voucher_count_max;
		putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
		console.log(" opt_type:" + opt_type +
			+" voucher_count_max:" + voucher_count_max +
			" putoutVoucherList:" + JSON.stringify(putoutVoucherList));
		if(opt_type == 'CC') {
			$('#btn_cc').addClass('active').siblings().removeClass('active');
		} else if(opt_type == 'ZK') {
			$('#btn_zk').addClass('active').siblings().removeClass('active');
		} else if(opt_type == 'XS') {
			$('#btn_xs').addClass('active').siblings().removeClass('active');
		} else if(opt_type == 'ZJ_PUTOUT') {
			$('#btn_zj_putout').addClass('active').siblings().removeClass('active');
		} else if(opt_type == 'ZJFC') {
			$('#btn_zjfc').addClass('active').siblings().removeClass('active');
		} else if(opt_type == 'GF_PUTOUT') {
			$('#btn_gf_putout').addClass('active').siblings().removeClass('active');
		} else {;
		}
	});
}

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	//返回
	var ws = plus.webview.currentWebview();
	plus.webview.close(ws);
	mui.openWindow({
		id: 'putout_list_new'
	});
}

/**
 * 用mui带的tap 好一些  如果元素重叠 会出现认为连续按下的感觉 
 */
//业务类型li点击事件
mui(document.body).on('click', '#opt_type button', function() {
	if(putoutVoucherList.length > 0 && (!$(this).hasClass('active'))) {
		mui.alert("有已扫描单据信息,不能切换业务", "提示", "确认", function() {}, "div");
		return false;
	}
	$(this).addClass('active').siblings().removeClass('active');
	opt_type = $(this).attr("data");
});

//时间范围li点击事件
mui(document.body).on('click', '#restrict_date button', function() {
	$(this).addClass('active').siblings().removeClass('active');
	restrict_date = $(this).attr("data");
	console.log(restrict_date);
});

//搜索按钮绑定事件
mui(document.body).on('tap', '#search', function() {
	customer_id = $('#customer_id').val();
	//TODO	1.根据条件查询单据信息
	//		2.打开单据明细页面
	if(opt_type == "") {
		mui.alert('', '请选择业务类型');
		return false;
	} else if(restrict_date == "") {
		mui.alert('', '请选择时间范围');
		return false;
	} else {
		//查询单据 如果有数据直接跳转到单据选择页面
		queryPutoutVoucher();
	}
});

//查询出库单据信息
function queryPutoutVoucher() {
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	/*if(segNo!="00138"){
		factory_wprovider="";
	}*/
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","operator_type":"' + opt_type + '","restrict_date":"' + restrict_date + '","customer_id":"' + customer_id + '","voucher_id":"","factory_wprovider":"' + factory_wprovider + '"}';
	console.log(params);
	var method = "exePutoutVoucherDownLoad";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		if(null != data) {
			console.log(JSON.stringify(data));
			if(data.resultStatus == "1") {
				console.log(JSON.stringify(data.putoutVoucherList));
				if(data.putoutVoucherList.length > 0) {
					queryVoucherList = data.putoutVoucherList;
					//进入单据选择页面
					mui.openWindow({
						url: 'putout_voucher_select.html',
						id: "putout_voucher_select",
						createNew: true,
						extras: {
							open_from_url: "putout_voucher_search.html",
							open_from_id: "putout_voucher_search",
							opt_type: opt_type,
							queryVoucherList: queryVoucherList,
							putoutVoucherList: putoutVoucherList,
							voucher_count_max: voucher_count_max
						}
					});
				} else {
					mui.alert('', "没有查询到单据信息");
					return false;
				}
			} else {
				mui.alert(data.resultDesc, "提示", "确认", function() {}, "div");
				return false;
			}
		} else { //连接失败
			mui.alert("没有下载到出库单据信息", "提示", "确认", function() {}, "div");
			return false;
		}
	});
}