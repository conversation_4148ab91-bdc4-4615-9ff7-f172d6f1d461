var segNo = localStorage.getItem("segNo");
var machineId = localStorage.getItem("machineId");
//领料选择的料台
var pickedPlat;
//领料选择的垛位号
var pickedPlatNum;


//监听自定义事件，接收回传参数
window.addEventListener("onPlatPicked", function(e) {
	pickedPlat = e.detail.pickedPlat;
	console.log("onPlatPicked接收到：" + pickedPlat);
	mui.openWindow({
		id: 'platnumpicker',
		url: 'platnumpicker.html',
		createNew: true,
		extras: {
			pickedPlat: pickedPlat
		}
	});
});


//监听自定义事件，接收回传参数
window.addEventListener("onPlatPickedChoose", function(e) {
	//pickedPlat = e.detail.pickedPlat;
	console.log("onPlatPicked接收到：" + pickedPlat);
	mui.openWindow({
		id: 'platpicker',
		url: 'platpicker.html',
		createNew: true
		// extras: {
		// 	pickedPlat: pickedPlat
		// }
	});
});


//监听自定义事件，接收回传参数
window.addEventListener("onPlatNumPicked", function(e) {
	pickedPlatNum = e.detail.pickedPlatNum;
	console.log("onPlatPicked接收到：" + pickedPlatNum);
	mui.openWindow({
		id: 'lingliao',
		url: 'lingliao.html',
		createNew: true,
		extras: {
			operation: 1,
			pickedPlat: pickedPlat,
			pickedPlatNum: pickedPlatNum
		}
	});
});

//监听自定义事件，接收料台
window.addEventListener("pickedChargePlatCode", function(e) {
	pickedPlat = e.detail.pickedPlat;
	console.log("onPlatPicked接收到：" + pickedPlat);
	mui.openWindow({
		id: 'lingliao',
		url: 'lingliao.html',
		createNew: true,
		extras: {
			operation: 1,
			pickedPlat: pickedPlat
		}
	});
});

//生产领料
mui(document.body).on('click', '#lingliao', function() {
	localStorage.setItem("pickedWorkerOrderId","");
	//选择料台
	mui.openWindow({
		id: 'platpicker',
		url: 'platpicker.html',
		createNew: true
	});
});

//生产退料
mui(document.body).on('click', '#tuiliao', function() {
	mui.openWindow({
		id: 'lingliao',
		url: 'lingliao.html',
		createNew: true,
		extras: {
			operation: 2
		}
	});
});
//实绩录入
mui(document.body).on('click', '#shiji', function() {

	//录入实绩前判断是否有机组代码
	if (localStorage.getItem("machineName") == null || localStorage.getItem("machineName") == "" ||
		localStorage.getItem("machineId") == null || localStorage.getItem("machineId") == "") {
		mui.openWindow({
			url: 'select_unit.html',
			createNew: true,
			extras: {
				openType: 'menu'
			}
		});
	} else {
		console.log(segNo)
		console.log(machineId)
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			machineId: machineId
		};
		params = JSON.stringify(params);
		var method = "queryOrderByMachineId";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				console.log(JSON.stringify(result));
				if (result.orderTotal == 1) {
					$.each(result.ordertList, function(i, e) {
						//production_order_code = e.productionOrderCode;
						//review(e.productionOrderCode)
						if (i == 0) {
							//$("#gdnumber").html(e.productionOrderCode)
							//review(e.productionOrderCode)
							mui.openWindow({
								url: 'record_entry.html',
								createNew: true,
								extras: {
									productionOrderCode: e.productionOrderCode
								}
							});
						}
					});
				} else {
					var url = "record_entry.html";
					if (machineId != "") {
						mui.openWindow({
							url: 'workorderoperation.html',
							createNew: true,
							extras: {
								machineId: machineId,
								packType : url
							}
						});
					}
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	}
});

//余料录入
mui(document.body).on('click', '#yuliao', function() {
	/*mui.openWindow({
		url: 'surplus_input.html',
		createNew: true
	});*/
	//录入余料废次材前判断是否有机组代码
	if (localStorage.getItem("machineName") == null || localStorage.getItem("machineName") == "" ||
		localStorage.getItem("machineId") == null || localStorage.getItem("machineId") == "") {
		mui.openWindow({
			url: 'select_unit.html',
			createNew: true,
			extras: {
				openType: 'menu'
			}
		});
	} else {
		console.log(segNo)
		console.log(machineId)
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			machineId: machineId
		};
		params = JSON.stringify(params);
		var method = "queryOrderByMachineId";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				console.log(JSON.stringify(result));
				if (result.orderTotal == 1) {
					$.each(result.ordertList, function(i, e) {
						//production_order_code = e.productionOrderCode;
						//review(e.productionOrderCode)
						if (i == 0) {
							//$("#gdnumber").html(e.productionOrderCode)
							//review(e.productionOrderCode)
							mui.openWindow({
								url: 'surplus_input.html',
								createNew: true,
								extras: {
									productionOrderCode: e.productionOrderCode
								}
							});
						}
					});
				} else {
					var url = "surplus_input.html";
					if (machineId != "") {
						mui.openWindow({
							url: 'workorderoperation.html',
							createNew: true,
							extras: {
								machineId: machineId,
								packType : url
							}
						});
					}
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	}
});
//成品废次材录入
mui(document.body).on('click', '#chengpin', function() {
	/*mui.openWindow({
		url: 'finished_waste.html',
		createNew: true
	});*/
	//录入成品废次材前判断是否有机组代码
	if (localStorage.getItem("machineName") == null || localStorage.getItem("machineName") == "" ||
		localStorage.getItem("machineId") == null || localStorage.getItem("machineId") == "") {
		mui.openWindow({
			url: 'select_unit.html',
			createNew: true,
			extras: {
				openType: 'menu'
			}
		});
	} else {
		console.log(segNo)
		console.log(machineId)
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			machineId: machineId
		};
		params = JSON.stringify(params);
		var method = "queryOrderByMachineId";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				console.log(JSON.stringify(result));
				if (result.orderTotal == 1) {
					$.each(result.ordertList, function(i, e) {
						//production_order_code = e.productionOrderCode;
						//review(e.productionOrderCode)
						if (i == 0) {
							//$("#gdnumber").html(e.productionOrderCode)
							//review(e.productionOrderCode)
							mui.openWindow({
								url: 'finished_waste1.html',
								createNew: true,
								extras: {
									productionOrderCode: e.productionOrderCode
								}
							});
						}
					});
				} else {
					var url = "finished_waste1.html";
					if (machineId != "") {
						mui.openWindow({
							url: 'workorderoperation.html',
							createNew: true,
							extras: {
								machineId: machineId,
								packType : url
							}
						});
					}
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	}
});
//原料废次材录入
mui(document.body).on('click', '#yuanliao', function() {
	/*mui.openWindow({
		url: 'rawmaterial_waste.html',
		createNew: true
	});*/
	//录入原料废次材前判断是否有机组代码
	if (localStorage.getItem("machineName") == null || localStorage.getItem("machineName") == "" ||
		localStorage.getItem("machineId") == null || localStorage.getItem("machineId") == "") {
		mui.openWindow({
			url: 'select_unit.html',
			createNew: true,
			extras: {
				openType: 'menu'
			}
		});
	} else {
		console.log(segNo)
		console.log(machineId)
		<!-- 查询前先关闭软键盘-->
		document.activeElement.blur();
		var webServiceUrl = localStorage.getItem("webServiceUrl");
		var imesWebServiceUrl = localStorage.getItem("imesWebServiceUrl"); //imes接口ip
		var targetnamespace = "http://service.pda.gmscservice.baosight.com"; //imes命名空间
		var outUri = domainName + "webService_imes.jsp";
		var innerUri = 'http://' + imesWebServiceUrl + '/gmsc-new-service/services/PDAProductionService?wsdl';
		var params = {
			segNo: segNo,
			machineId: machineId
		};
		params = JSON.stringify(params);
		var method = "queryOrderByMachineId";
		console.log("params" + params);
		$.ajax({
			type: "post",
			async: true,
			url: outUri,
			data: {
				innerUri: innerUri,
				params: params,
				method: method,
				targetnamespace: targetnamespace
			},
			dataType: "json",
			success: function(result) {
				console.log(JSON.stringify(result));
				if (result.orderTotal == 1) {
					$.each(result.ordertList, function(i, e) {
						//production_order_code = e.productionOrderCode;
						//review(e.productionOrderCode)
						if (i == 0) {
							//$("#gdnumber").html(e.productionOrderCode)
							//review(e.productionOrderCode)
							mui.openWindow({
								url: 'rawmaterial_waste.html',
								createNew: true,
								extras: {
									productionOrderCode: e.productionOrderCode
								}
							});
						}
					});
				} else {
					var url = "rawmaterial_waste.html";
					if (machineId != "") {
						mui.openWindow({
							url: 'workorderoperation.html',
							createNew: true,
							extras: {
								machineId: machineId,
								packType : url
							}
						});
					}
				}
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
				console.log(JSON.stringify(XMLHttpRequest));
				console.log(textStatus);
				console.log(errorThrown);
			}
		})
	}
});

//成品质量信息录入
mui(document.body).on('click', '#chengpinzhiliang', function() {
	localStorage.setItem("quality", "1");
	mui.openWindow({
		url: 'select_work_order.html',
		createNew: true
	});
});
//原料质量信息录入
mui(document.body).on('click', '#yuanliaozhiliang', function() {
	localStorage.setItem("quality", "0");
	mui.openWindow({
		url: 'select_work_order.html',
		createNew: true
	});
});
//选择机组
mui(document.body).on('click', '#xuanzejizu', function() {
	mui.openWindow({
		url: 'select_unit.html',
		createNew: true
	});
});
