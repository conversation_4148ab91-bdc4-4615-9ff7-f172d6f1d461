var segNo = localStorage.getItem("segNo");

window.onload = function onload() {
	mui.plusReady(function() {
		$("#trace").css("display", "none");
	});

}

$(document).ready(function() {
	if (segNo != "00126" && segNo != '00129' ) {
		$("#enter_factory").hide();
	}
	/*if(segNo == "00120") {
		$("#trace").hide();
	}
	if(segNo == "00120") {
		$("#pack_leave").hide();
	}
	if(segNo != "00120") {
		$("#leave_out").hide();
	}*/
});

//捆包出厂扫描
mui(document.body).on('click', '#pack_leave', function() {
	mui.openWindow({
		url: 'pack_leave_vehicle_scan.html',
		createNew: true
	});
});

mui(document.body).on("tap", "#enter_factory", function() {
	mui.openWindow({
		url: 'vehicle_enter_factory.html',
		ctreteNew: true,
		id: 'vehicle_enter_factory'
	});
});

console.info("segNo" + segNo);
if (segNo == '00138' || segNo == "00126" || segNo == "00181") {
	$("#divOpeNext").show();
	//车辆离厂
	mui(document.body).on('click', '#trace', function() {
		mui.openWindow({
			url: 'board_vehicle_trace.html',
			createNew: true
		});
	});
}
