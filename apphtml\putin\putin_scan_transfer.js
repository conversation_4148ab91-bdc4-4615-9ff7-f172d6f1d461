/**
 * 初始化变量信息 
 */

var putinPackList = new Array(); //入库捆包列表
var wprovider_id = ""; //仓库代码
var wprovider_name = ""; //仓库名称
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var putin_id = ""; //定义临时入库申请单号(流水号)
var edit_flag = "";


var mean_name = localStorage.getItem("name"); //判断是从菜单进来的还是出厂物流进来的
var factory_wprovider = localStorage.getItem("factory_wprovider"); //厂区对应的仓库
var team_id = localStorage.getItem("team_id"); //班组代码
//1为扫描捆包信息查询出来的捆包信息修改。 2 没有找到捆包号对应信息，手工录入的
var type = "2"; //2表示捆包未匹配到入库资源。从已扫描列表修改捆包回传到入库扫描页面后type=1 
var forLocationPack = {}; //当前扫描捆包对象

window.onload = function onload() {
	mui.plusReady(function() {
		wprovider_id = localStorage.getItem("wprovider_id");
		wprovider_name = localStorage.getItem("wprovider_name");
	});

	//change by yangzemin ********
	// if(wprovider_id == "1GQ01") {
	if(wprovider_id == "*********") {
		factory_wprovider = wprovider_id;
	}
	$("#wprovider_name_span").html("转入仓库：" + localStorage.getItem("wprovider_name"));
	
	
	mui.back = function() {
		if(!checkBeforeback()) {
			return false;
		}
		localStorage.setItem("wprovider_id", wprovider_id);
		localStorage.setItem("wprovider_name", wprovider_name);
		console.log(putinPackList.length > 0);
		console.log(putinPackList != null);
		console.log(putinPackList != []);
		if(putinPackList.length > 0 && putinPackList != null && putinPackList != []) {
			var btnArray = ['退出', '取消'];
			mui.confirm('存在已扫描未上传的数据,是否退出?', '提示', btnArray, function(e) {
				if(e.index == 0) {
					/*var thisPage = plus.webview.currentWebview();
					console.log("关闭转库扫描页面")
					thisPage.close();*/
					putinPackList = [];
					mui.openWindow({
						url:'putin_method_menus.html',
						id:'putin_method_menus',
						createNew:false
			    	});
				}
			}, 'div');
		} else {
			/*var thisPage = plus.webview.currentWebview();
			console.log("关闭转库扫描页面")
			thisPage.close();*/
			mui.openWindow({
				url:'putin_method_menus.html',
				id:'putin_method_menus',
				createNew:false
	    	});
		}
	}
	
}

mui.init({
	swipeBack: true, //关闭右滑关闭功能
	gestureConfig: {
		longtap: true,
		doubletap: true
	}
});

$(function() {
	$("#pack_location").focus();
});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});


mui(document.body).on("tap", ".icon-setting", function() {
	mui.openWindow({
		url: '../public/select_wprovider_id.html',
		id: 'select_wprovider_id',
		createNew: true,
		extras: {
			transfer_flag: 1 //转库入库标记
		}
	});
});

//增加keypress监听
/**
 * add by Luo Yinghui  库位文本框扫描，判断库位是否存在 
 */
$("#pack_location").keypress(function(e) {
	if(e.keyCode == 13) {
		document.activeElement.blur();
		var pack_id = $("#pack_id").val();
		if($("#pack_location").val() == "") {
			mui.alert("请扫描或者输入库位", "提示", "确定", null, "div");
			$("#pack_location")[0].focus();
			return;
		}
		$("#pack_id").focus();
		//判断库位是否存在
		//judgeLocationExist();
	}
});

/**
 * 修改 type :未找到扫描捆包明细type=1;从已扫描列表进入扫描页面 type=2
 */
mui(document.body).on('tap', '#edit', function() {
	var pack_qty = $("#qty").val();
	var pack_id = $("#pack_id").val();
	var spec = $("#spec").val();
	var factory_product_id = $("#factory_product_id").val();
	var weight = $("#weight").val();
	var qty = $("#qty").val();
	var pack_location = $("pack_location").val();
	if(type == "1") {
		var pack_id = $("#pack_id").val();
		if(!selectById(pack_id, putinPackList)) {
			mui.toast("没有找到可以修改的捆包,请检查捆包号", "提 示", "确定", function() {}, 'div');
			return false;
		}
		var tempWeight = $("#weight").val();
		if(tempWeight == null || tempWeight == "") {
			mui.toast("请输入重量", "提示", "确认", function() {}, "div");
			return false;
		}
		var index = getIndexById(pack_id, putinPackList);
		putinPackList[index].location_desc = $("#pack_location").val();
		putinPackList[index].spec = $("#spec").val();
		putinPackList[index].factory_product_id = $("#factory_product_id").val();
		putinPackList[index].putin_weight = $("#weight").val();
		putinPackList[index].putin_qty = pack_qty;
		console.log("121211111111111111:" + JSON.stringify(pack_info));
		reSum(putinPackList, pack_id);
		$("#upload_image").attr("disabled", "disabled");
		$("#edit").attr("disabled", "disabled");
		edit_flag = 0;
	} else if(type == "2") {
		if(vaildate()) {
			var pack_info = new Object();
			pack_info.pack_id = $("#pack_id").val();
			pack_info.location_desc = $("#pack_location").val();
			pack_info.spec = $("#spec").val();
			pack_info.factory_product_id = $("#factory_product_id").val();
			pack_info.putin_weight = $("#weight").val();
			pack_info.putin_qty = pack_qty;
			console.log("121211111111111111:" + JSON.stringify(pack_info));
			putinPackList.push(pack_info);
			//refreshSum(pack_info);
			reSum(putinPackList, $("#pack_id").val());
		}
	}
});

function vaildate() {
	var pack_id = $("#pack_id").val();
	if(pack_id == null || pack_id == "") {
		mui.alert("请输入捆包号", "提示", "确定", function() {}, "div");
		$("#pack_id").focus();
		return false;
	}
	var tempWeight = $("#weight").val();
	if(tempWeight == null || tempWeight == "") {
		mui.alert("请输入重量", "提示", "确定", function() {}, "div");
		return false;
	};
	return true;
}

/**
 * 转库入库
 */
mui(document.body).on('tap', '#putin', function() {
	//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
	mui("#putin").button('loading');
	$("#overlay").addClass("overlay");
	//扫描判断入库仓库和厂区仓库是否一致，不对应则提示
	console.log(segNo + "," + mean_name + "。");
	//调用入库接口
	if(putinPackList.length == 0) {
		//mui.alert("已扫描捆包列表无记录"," ","确定",function(){},'div');
		plus.nativeUI.toast("已扫描列表无捆包记录。请检查");
		$("#pack_id").val("");
		$("#pack_id")[0].focus();
		mui("#putin").button('reset');
		$("#overlay").removeClass("overlay");
		return false;
	} else {
		toPutinPackInfo();
	}
});

function toPutinPackInfo() {
	//ldet outUri = domainName+"webService.jsp?callback=?";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAZKService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","wprovider_id":"' + wprovider_id + '","team_id":"' + team_id + '","pack_list":' + JSON.stringify(putinPackList) + '}';
	console.log("921  params》》》》》》》》" + params);
	params = encodeURI(params, 'utf-8');
	var method = "exeUploadZKPackInfo";
	$.ajax({
		type: "post",
		async: true,
		//timeout: outtime,
		url: outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(result) {
			if(null != result) {
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
				console.log(JSON.stringify(result));
				mui.alert(result.resultDesc, "提示", "确定", function() {}, "div");
				putinPackList = [];
				console.log(putinPackList.length);
				$("#recent_pack_id").html("无");
				$("#sum_qty > #sum_number").text(0);
				$("#sum_weight > #sum_number").text(0);
				//清空信息焦点定位到捆包
				clearInput();
				$("#pack_location").val("");
				//$("#pack_location")[0].focus();
			} else { //连接失败
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if(textStatus == "timeout") {
					XMLHttpRequest.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				} else {
					mui.alert("连接服务器异常,请检查网络后重试", "提示", "确定", function() {}, "div");
				}
				mui("#putin").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}

//查看入库捆包清单
mui(document.body).on('tap', '#detail', function() {
	console.log("edit_flag:" + edit_flag);
	if(edit_flag == 1) {
		mui.toast("请先保存当前捆包信息！");
		return;
	}
	mui.openWindow({
		url: 'putin_pack_list.html',
		id: 'putin_pack_list',
		createNew: true,
		extras: {
			putinPackList: putinPackList,
			transfer_flag: 1 //转库入库标记
		}
	});
});
//绑定入库清单信息回写事件
window.addEventListener('back', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	//重算合计信息
	reSum(putinPackList, '');
	$("#pack_id").focus();
	//从详情直接返回扫描页面
	//setDefaultInputStyle();
	//clearPreviousLocation(); //清空上一个捆包的库位
});

//绑定入库清单信息修改事件
window.addEventListener('edit', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	reSum(putinPackList, '');
	var edit_pack = e.detail.edit_pack;
	type = e.detail.type;
	edit_flag = e.detail.edit_flag;
	console.log("修改标记：" + edit_flag);
	console.log("type:" + type);
	if(edit_flag == 1) {
		$("#upload_image").removeAttr("disabled");
		$("#edit").removeAttr("disabled");
	}
	refreshPackInfo(edit_pack);
	//clearPreviousLocation();//清空上一个捆包的库位
});

function refreshSum(pack_info) {
	//最近捆包
	$("#recent_pack_id").text(pack_info.pack_id);
	//统计合计信息
	var qty = $("#sum_qty > #sum_number").text();
	if(parseInt(qty) < 1) {
		qty = parseInt(qty) + 1;
	} else {
		qty = parseInt(qty);
	}
	var weight = $("#sum_weight > #sum_number").text();
	weight = parseFloat(weight) + parseFloat(pack_info.putin_weight * 1000);
	//取3位小数
	weight = weight.toFixed(3);
	$("#sum_qty > #sum_number").text(qty);
	$("#sum_weight > #sum_number").text(weight);
	//clearInput();
}

function reSum(packlist, pack_id) {
	var recent_pack_id = $("#recent_pack_id").text();
	var recent = 0;
	var sum_qty = 0;
	var sum_weight = 0;
	var ppid = "";
	$.each(packlist, function(i, value) {
		//console.log(value.pack_id);
		if(recent_pack_id == value.pack_id) {
			recent = recent + 1;
		}
		sum_qty = sum_qty + 1;
		ppid = value.pack_id
		sum_weight = parseFloat(sum_weight) + parseFloat(value.putin_weight * 1000);
		console.log("putin_weight:" + value.putin_weight);
		console.log("sum_weight:" + sum_weight);
		//console.log("12:"+ppid+"recent:"+recent+"，sum_qty："+sum_qty);
	});
	if(pack_id == "" || pack_id == "undefine" || pack_id == null) {
		if(ppid != "" && ppid != "undefine" && ppid != null) {
			$("#recent_pack_id").text(ppid);
		} else {
			$("#recent_pack_id").text("无");
		}
	} else {
		$("#recent_pack_id").text(pack_id);
	}

	//保留三位小数
	sum_weight = sum_weight.toFixed(3);
	$("#sum_qty > #sum_number").text(sum_qty);
	$("#sum_weight > #sum_number").text(sum_weight);

	//清空信息焦点定位到捆包
	clearInput();
}

/**
 * 
 * @param {Object} pack Json格式字符串
 */
function refreshPackInfo(pack) {
	//alert(pack.pack_id+","+pack.location_desc)
	$("#pack_location").val(pack.location_desc);
	$("#pack_id").val(pack.pack_id);
	$("#spec").val(pack.spec);
	$("#factory_product_id").val(pack.factory_product_id);
	$("#weight").val(pack.putin_weight);
	$("#qty").val(pack.putin_qty);
	$("#spec")[0].focus();
}

/**
 * 捆包查询
 */
$("#pack_id").keypress(function(e) {
	if(e.keyCode == 13) {
		var pack_location = $("#pack_location").val();
		console.log("pack_location:====>" + pack_location);
		if (pack_location == "" || pack_location == undefined || pack_location == null) {
			mui.toast("请先扫描库位！");
			return;
		}
		//add by xuhuaijun 20161229 格式化捆包号
		var pack_id = $("#pack_id").val();
		if(pack_id == "") {
			mui.alert("捆包号不能为空", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		if(selectById(pack_id, putinPackList)) {
			mui.alert("不能扫描重复的捆包号", " ", "确定", function() {}, 'div');
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		}
		//获取捆包信息后隐藏软键盘
		document.activeElement.blur();
		//输入或扫描捆包号之后，调用捆包查询接口
		mui.plusReady(function() {
			var curNetConnetType = plus.networkinfo.getCurrentType();
			if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
				curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
				queryPackNetNotConnect();
				plus.nativeUI.toast("无网络连接，请检查", {
					'verticalAlign': 'center'
				});
			} else {
				initData();
			}
		});
	}
});

/*
 * 查询捆包事件
 */
function initData() {
	//var outUri = domainName+"webService.jsp?callback=?";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAZKService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + $("#pack_id").val() + '"}';
	var method = "exequeryPackInfo";
	forLocationPack = {};
	$.ajax({
		type: "get",
		async: true,
		url: outUri,
		dataType: "json",
		timeout: 10000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			console.log(data.factory_product_id);
			if(data != null) {
				if(data.product_id == "" || data.product_id == null) {
					mui.alert("未找到待入库捆包信息，请确认条码是否有误！", "提示", "确定", function() {}, "div");
					$("#spec")[0].focus();
					type = "2";
					return false;
				} else {
					forLocationPack = data; //modify by wangshengbo171211 定义成全局变量
					forLocationPack.consignee_id = wprovider_id;
					forLocationPack.consignee_name = wprovider_name;
					forLocationPack.imageName = [];
					forLocationPack.putin_id = "";
					console.log(wprovider_id + "||ssssss||" + wprovider_name);
					$("#pack_id").val(data.pack_id);
					$("#spec").val(data.spec);
					$("#factory_product_id").val(data.factory_product_id);
					$("#weight").val(data.putin_weight);
					console.log("净重：" + forLocationPack.putin_weight);
					$("#qty").val(data.pack_qty);
					//$("#pack_location").val(data.location);
					afterGetPackObj(); //当前扫描捆包添加到已扫描列表以及后续处理
					if(putinPackList.length > 0) {
						$("#putin").removeAttr("disabled");
					}
				}
			} else { //连接失败
				mui.alert("工贸服务器处理异常", "提示", "确定", null, 'div');
				return;
			}
			//$("#pack_id").focus();
			//document.activeElement.blur();
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			//console.log("readyState>>"+XMLHttpRequest.readyState + " , textStatus>>>"+textStatus);
			//超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
			if(textStatus == "timeout") {
				if(isAutoAllocLocation()) {
					mui.alert("网络请求超时,请手工补充库位以及捆包信息后，点击【修改】", "提示", "确定", null, 'div');
					setInputReadOnly("#pack_location", false);
					$("#pack_location").focus();
				} else {
					queryPackNetNotConnect(); //请求超时PDA只记录捆包号和手工输入的库位
				}
			} else {
				mui.alert("服务器连接异常", "提示", "确定", null, 'div');
			}
		}
	});
}

/*智慧仓库:
 * 如果当前捆包已经存在，则更新捆包信息。
 * 否则添加一条新纪录到已扫描捆包列表
 */
function afterGetPackObj() {
	var pack_id = $("#pack_id").val();
	console.log("flag:" + !selectById(pack_id, putinPackList));
	if(!selectById(pack_id, putinPackList)) {
		forLocationPack.putin_qty = $("#qty").val();
		forLocationPack.location_desc = $("#pack_location").val();
		putinPackList.push(forLocationPack);
		reSum(putinPackList, pack_id);
		$("#pack_id").focus();
	} else {
		var index = getIndexById(pack_id, putinPackList);
		putinPackList[index].location_desc = $("#pack_location").val();
		putinPackList[index].spec = $("#spec").val();
		putinPackList[index].factory_product_id = $("#factory_product_id").val();
		putinPackList[index].putin_weight = $("#weight").val();
		console.log("putin_weight:===>" + putinPackList[index].putin_weight);
		putinPackList[index].putin_qty = $("#qty").val();
		//putinPackList[index].inner_diameter = $("#inner_diameter").val();
		reSum(putinPackList, pack_id);
	}

	//清空信息焦点定位到捆包
	$("#pack_id").val("");
	$("#spec").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	//clearPreviousLocation();
}

function clearInput() {
	//清空信息焦点定位到捆包
	$("#pack_id").val("");
	$("#spec").val("");
	$("#factory_product_id").val("");
	$("#weight").val("");
	$("#qty").val("");
	$("#pack_location")[0].focus();
}

//无网络连接时，只记录扫描捆包号
function queryPackNetNotConnect() {
	var pack_id = $("#pack_id").val();
	if(pack_id != "") {
		var packObj = {};
		packObj.location_desc = $("#pack_location").val();
		packObj.pack_id = $("#pack_id").val();
		packObj.factory_product_id = "";
		packObj.putin_weight = "";
		packObj.putin_qty = "1";
		packObj.spec = "";
		packObj.putin_voucher_num = "";
		packObj.loc_view_id = "";
		putinPackList.push(packObj);
		//更新合计框内信息
		refreshSum(packObj);
		//清空信息焦点定位到捆包
		$("#pack_id").val("");
		$("#spec").val("");
		$("#factory_product_id").val("");
		$("#weight").val("");
		$("#qty").val("");
		//clearPreviousLocation();
	}
}

//是否自动分配库位信息
/*function isAutoAllocLocation(){
	if(location_type > 0 && auto_loc_flag == '1'){
		return true;
	}else{
		return false;
	}
}*/

//add by penglei 最近捆包  弹出框
mui(document.body).on('longtap', '#recent_pack_id', function() {
	//TODO 需要根据捆包信息重绘捆包信息弹出层 #pop_pack_info
	$("#pop_bg").toggleClass('show');
	$("#pop_pack_info").toggleClass('show');
	var recent_pack_id = $("#recent_pack_id").text();
	popWind(recent_pack_id);
});

function popWind(pack_id) {
	var index = getIndexById(pack_id, putinPackList);
	var pack = putinPackList[index];
	$("#npack_id").html(pack.pack_id);
	$("#npack_location").html(pack.location_desc);
	$("#nspec").html(pack.spec);
	$("#nfactory_product_id").html(pack.factory_product_id);
	$("#nweight").html(pack.putin_weight);
}

function checkBeforeback() {
	var flag = false;
	if($("#pop_bg").hasClass('show')) {
		$("#pop_bg").toggleClass('show');
		$("#pop_pack_info").toggleClass('show');
	} else {
		flag = true;
	}
	return flag;
}


//判断库位是否存在
function judgeLocationExist() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDALocationChangeService';
	var params = '{"seg_no":"' + segNo + '","pack_location":"' + $("#pack_location").val() + '","wprovider_id":"' + wprovider_id + '"}';
	var method = "exeQueryLocation";
	console.log("params" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if(null != data) {
			console.log("resultStatus:" + data.resultStatus);
			console.log("locationList:" + JSON.stringify(data.locationList));
			if(data.resultStatus == "0") {
				mui.alert("库位不存在", "提示", "确认", null, "div");
				$("#pack_location").val("");
				$("#pack_location")[0].focus();
				return;
			} else {
				$("#pack_id")[0].focus();
			};
			if(data.resultStatus == "1") {
				if(data.locationList.length > 0) {
					$.each(data.locationList, function(i, item) {
						location_id = item.location_id;
					});
				}
			}
		}
	});
}

mui(document.body).on('tap', '#upload_image', function() {
	var pack_id = $("#pack_id").val();
	var product_id = $("#factory_product_id").val();
	console.log("=========>" + product_id);
	mui.openWindow({
		url: 'upload_image.html',
		id: 'upload_image',
		createNew: true,
		extras: {
			pack_id: pack_id,
			product_id: product_id
		}
	});
});

/**
 * 绑定图片捆包缺陷图片上传成功之后的页面跳转返回事件 add by Luo Yinghui
 */
window.addEventListener('uploadSuccessed', function(e) {
	//获得事件参数
	
	putin_id = e.detail.putin_id;
	var tempImageName = e.detail.imageName;
	var tempPackID = e.detail.pack_id;
	var index = getIndexById(tempPackID,putinPackList);
	var imageName = putinPackList[index].imageName;
	if (imageName == "" || imageName.length == 0) {
		imageName = [];
	}
	for (var i = 0; i < tempImageName.length; i++) {
		imageName.push(tempImageName[i]);
	}
	putinPackList[index].imageName = imageName;
	console.log(putinPackList[index].pack_id + "||" + putinPackList[index].imageName);
	imageName = [];
	putinPackList[index].putin_id = putin_id;
});