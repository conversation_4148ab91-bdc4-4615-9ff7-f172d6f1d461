			/**
			 * 初始化变量信息 
			 */
			var opt_type = "";//单据类型(备用暂时没用)
			var open_from_url = "";//通过哪个页面打开该页面
			var open_from_id = "";//通过哪个页面打开该页面
			var putoutVoucherList = new Array();//已选出库单据
			var voucher_count_max;//扫描单据上限数(页面传值)
			var queryVoucherList = new Array();//待选出库单据列表
			var selectVoucherList = new Array();//已选出库单据列表
			var voucherinfo =new HashMap();			
			var query_print_batch_id_list = 0;//是否从仓储过来
			var voucherinfo =new HashMap();	
			
			$(function(){
				mui.init({
					swipeBack:true, //启用右滑关闭功能
				});
			});
			(function($) {
				$('.mui-scroll-wrapper').scroll({
					indicators: true //是否显示滚动条
				});
			})(mui);
			window.onload = function onload(){
				mui.plusReady(function(){
					open_from_url = plus.webview.currentWebview().open_from_url;
					open_from_id = plus.webview.currentWebview().open_from_id;
					opt_type = plus.webview.currentWebview().opt_type;
					voucher_count_max = plus.webview.currentWebview().voucher_count_max;
					putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
					queryVoucherList = plus.webview.currentWebview().queryVoucherList;
					query_print_batch_id_list = plus.webview.currentWebview().query_print_batch_id_list;
					console.log("open_from_url:" + open_from_url 
								+ " open_from_id:" +  open_from_id
								+ " opt_type:" + opt_type
								+ " voucher_count_max:" + voucher_count_max
								+ " query_print_batch_id_list:"+query_print_batch_id_list
								+ " putoutVoucherList:" + JSON.stringify(putoutVoucherList)
								+ " queryVoucherList:" + JSON.stringify(queryVoucherList)); 
					//添加单据信息到ul中
					showVoucherList(queryVoucherList);
					//tangli add 出库单据默认全部勾选
					if(query_print_batch_id_list == 1){
						
						if(queryVoucherList.length > voucher_count_max){
							mui.alert("已扫描单据数已达到上限:"+voucher_count_max);
						}else{
							//将单据默认全部勾选  
							$.each(queryVoucherList, function(i,item) {
									var a = $('li').children('a');
									//新增已选单据
									if(addSelectVoucherList(item.voucher_id,item)){
										a.addClass("select");
									}
							});
						}
					}		
				});
			}
			
			mui(document.body).on('tap','#back',function(){
				mui.back();
			});
			
			
			mui.back = function(){
				//返回
				var ws=plus.webview.currentWebview();  
				plus.webview.close(ws);
				mui.openWindow({
					id:open_from_id
				});
			}
			
			function showVoucherList(putoutVoucherList){
				if(putoutVoucherList.length>0){
					var html="";
					$.each(putoutVoucherList, function(i,item) {
						voucherinfo.put(item.voucher_id,item);
						html=html +	'<li class="mui-table-view-cell">' +
										'<a class="mui-navigate-right">' +
											'<div>' +
												'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span></div>' +
												'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
												'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
											'</div>' +
										'</a>' +
									'</li>';
					});
					$("#voucher_list").html(html);
				}else{
					$("#voucher_list").html("");
				}
			}
			
			//绑定单据点击事件
			mui(document.body).on('tap','li',function(){
				var a = $(this).children('a');
				if(a.hasClass("select")==false){
					var voucher_id = a.children('div').children('div').children('span').html().trim();
					 var voucher =voucherinfo.get(voucher_id);
					if(addSelectVoucherList(voucher_id,voucher)){
						a.addClass("select");
					}
				}else if(a.hasClass("select")==true){
					a.removeClass("select");
					var voucher_id = a.children('div').children('div').children('span').html().trim();
					//删除已选单据
					delSelectVoucherList(voucher_id);
				}
				//console.log("selectVoucherList.length:" + selectVoucherList.length + JSON.stringify(selectVoucherList));
			});
			
			//绑定确定按钮点击事件
			mui(document.body).on('tap','#confirm',function(){
				if(open_from_id=='select_allocate_vehicle_no'){
					if(open_from_id != 'hand_point_list'){
						var ws2 = plus.webview.getWebviewById(open_from_id);
						plus.webview.close(ws2);
					}
					var ws = plus.webview.currentWebview();  
					plus.webview.close(ws);
					console.info("selectVoucherList::::"+selectVoucherList);
					var page = plus.webview.getWebviewById('hand_point_list');
					mui.fire(page,'addFromSelect',{
						optType : opt_type,
						voucherList : selectVoucherList
					});
					//跳转回出库单据页面
					mui.openWindow({
						id:'hand_point_list'	
					});
				}else{
					if(open_from_id != 'putout_list_new'){
						var ws2 = plus.webview.getWebviewById(open_from_id);
						plus.webview.close(ws2);
					}
					var ws = plus.webview.currentWebview();  
					plus.webview.close(ws);
					
					var page = plus.webview.getWebviewById('putout_list_new');
					mui.fire(page,'addFromSelect',{
						optType : opt_type,
						voucherList : selectVoucherList
					});
					
					//跳转回出库单据页面
					mui.openWindow({
						id:'putout_list_new'	
					});
				}
			});	
			
			//添加单据信息
			function addSelectVoucherList(voucher_id,voucher_info){
				//校验单据是否已在putoutVoucherList中存在    select本身根据样式区分不用校验
				var index = getIndexByVoucherId(voucher_id,putoutVoucherList);
				console.info("indexindexindexindexindex:::::::"+index);
				if(index != -1){
					mui.alert(voucher_id + "该单据已经在已扫单据列表中,不能重复添加","提示","确认",function() {}, "div");
					return false;
				}else{
					//可扫单据数量校验
					if((putoutVoucherList.length + selectVoucherList.length) == voucher_count_max){
						mui.alert("已扫描单据数已达到上限：" + voucher_count_max,"提示","确认",function() {}, "div");
						return false;
					}else{
						//提单安全性校验
						if(inputVerify(voucher_info)){
							var query_index = getIndexByVoucherId(voucher_id,queryVoucherList);
							selectVoucherList.push(queryVoucherList[query_index]);
							return true;
						}else{
							return false;
						}
					}
					
				}
			}
			
			//删除单据信息
			function delSelectVoucherList(voucher_id){
				var query_index = getIndexByVoucherId(voucher_id,selectVoucherList);
				selectVoucherList.splice(query_index,1);
			}
