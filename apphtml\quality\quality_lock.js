/**
 * 初始化变量信息 
 */
var product_id = "";

//照片选择相关
var pickedPhotos = new Array(); //定义数组存放被选中的图片
var base64ImageCode = new Array(); //定义数组存放被选中的图片的base64编码

$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});
	mui('.mui-scroll-wrapper').scroll({
		indicators: true, //是否显示滚动条
		deceleration: 0.0005
	});
});

mui.plusReady(function() {
	$("#pack_id").focus();
});

//捆包号
$("#pack_id").keypress(function(e) {
	if (e.keyCode == 13) {
		var pack_id = $("#pack_id").val();
		//add by xuhuaijun 20161229 格式化捆包号
		pack_id = formatPackId(pack_id);
		$("#pack_id").val(pack_id);
		//return false;
		if (pack_id == null || pack_id == "") {
			mui.alert("请扫描或者输入捆包号", "提示", "确定", null, 'div');
			$("#pack_id").focus();
			return;
		}
		queryQualityBlockPack(pack_id);
	}
});

//按钮绑定事件
mui(document.body).on('tap', '#block_button', function() {
	var pack_id = $("#pack_id").val();
	if (pack_id == null || pack_id == "") {
		mui.alert("请扫描或者输入捆包号", "提示", "确定", null, 'div');
		$("#pack_id").focus();
		return;
	}
	var process_bug_id = $("#process_bug_id").val();
	if (process_bug_id == null || process_bug_id == "") {
		mui.alert("请输入缺陷代码", "提示", "确定", null, 'div');
		$("#process_bug_id").focus();
		return;
	}
	var quality_desc = $("#quality_desc").val();
	if (quality_desc == "" || quality_desc == null) {
		mui.alert("请输入封闭原因", "提示", "确定", null, "div");
		$("#quality_desc").focus();
		return;
	}
	if (product_id == "" || product_id == null) {
		mui.alert("手输捆包号之后请点击回车", "提示", "确定", null, "div");
		$("#pack_id").focus();
		return;
	}
	mui("#block_button").button('loading');
	$("#overlay").addClass("overlay");
	exePackQualityBlockUpload();
});

//按钮缺陷代码事件
mui(document.body).on('tap', '#query_button', function() {
	var process_bug_id = $("#process_bug_id").val();
	queryQualityProcessBugId(process_bug_id);
});
//关闭下拉框事件
mui(document.body).on('tap', '#colse_process_bug_id', function() {
	$("#query_div").hide();

});
//按钮下拉框复值事件
mui(document.body).on('tap', '.mui-table-view-cell', function() {
	var process_bug = $(this).attr("p_id");
	var process_bug_name = $(this).html();
	//console.info(process_bug+"=="+process_bug_name);
	$("#process_bug_id").val(process_bug);
	$("#quality_desc").val(process_bug_name.substring(process_bug_name.indexOf("-") + 1, process_bug_name.length));
	$("#query_div").hide();
});

//捆包扫描方法

function queryQualityBlockPack(pack_id) {
	<!-- 查询前先关闭软键盘-->
	//console.log("packQualityBlock1111>>>>>>>>>>>>>>>>");
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	//				var segNo = "00112";
	//				var webServiceUrl = "10.30.184.231:7001";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","pack_id":"' + pack_id + '"}';
	var method = "exeQueryPackInfo";
	console.log(method + " 参数：" + params);
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		console.log(method + " 返回：" + JSON.stringify(data));
		if (data != null) {
			if (data.packList.length == 0) {
				mui.alert("未查询到对应的捆包信息", "提示", "确定", function() {
					$("#pack_id").val("");
					$("#pack_id").focus();
				}, 'div');
				return;
			} else {
				product_id = data.packList[0].product_id;
				$("#process_bug_id").focus();
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}

function queryQualityProcessBugId(process_bug_id) {
	<!-- 查询前先关闭软键盘-->
	//console.log("packQualityBlock1111>>>>>>>>>>>>>>>>");
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	//				var segNo = "00112";
	//				var webServiceUrl = "10.30.184.231:7001";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var params = '{"seg_no":"' + segNo + '","process_bug":"' + process_bug_id + '"}';
	var method = "QueryBugDesc";
	params = encodeURI(params, 'utf-8');
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性 
		if (data != null) {
			if (data.codeList.length == 0) {
				mui.alert("未查询到对应的捆包信息", "提示", "确定", function() {
					$("#process_bug_id").val("");
					$("#process_bug_id").focus();
				}, 'div');
				return;
			} else {
				var html = "";
				$.each(data.codeList, function(i, item) {
					html += "<li class='mui-table-view-cell' p_id='" + item.process_bug_id + "'>" + item.process_bug_id + "-" +
						item.process_bug_name + "</li>";
				});
				//	console.info(html);
				$("#query_div").show();
				$("#selectdata").html(html);
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}







//执行捆包封闭
function exePackQualityBlockUpload() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo"); //采用localStorage存储数据
	var account = localStorage.getItem("account");
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	//				var segNo = "00112";
	//				var webServiceUrl = "10.30.184.231:7001";
	// var outUri = domainName + "webService.jsp?callback=?";
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAManuFactureService';
	var pack_id = $("#pack_id").val();
	var quality_desc = $("#quality_desc").val();
	var process_bug_id = $("#process_bug_id").val();
	console.log("outUri =" + outUri);
	console.log("innerUri =" + innerUri);
	//照片base64数组
	// var base64Str = JSON.stringify(base64ImageCode);
	// console.log(method + " 参数base64Str：" + base64Str);

	//构建参数
	var packList = new Array();
	var pack = {
		"pack_id": pack_id,
		"block_desc": quality_desc,
		"product_id": product_id
	};
	packList.push(pack);
	var base64List = new Array();
	base64List.push("111111");
	var param = {
		"seg_no": segNo,
		"user_id": account,
		"process_bug_id": process_bug_id,
		"pack_list": packList,
		"pack_photos": base64ImageCode
	}

	$("#quality_desc").val($("#quality_desc").val().replace(/[\r\n]/g, "")); //去掉回车换行
	// var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","process_bug_id":"' + process_bug_id +
	// 	'","pack_list":[{"pack_id":"' + pack_id + '","block_desc":"' + quality_desc + '","product_id":"' + product_id +
	// 	'"}],"pack_photos":' + base64Str + '}';
	var params = JSON.stringify(param);

	var method = "exePackQualityBlockUpload";
	// console.log(method + " 参数：" + params);
	params = window.encodeURIComponent(params);
	$.ajax({
		type: "post",
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		timeout: 300 * 1000,
		async: true,
		cache: false,
		success: function(data) { //如返回对象有一个username属性
			console.log(method + " 返回：" + JSON.stringify(data));
			if (data != null) {
				mui("#block_button").button('reset');
				$("#overlay").removeClass("overlay");
				if (data.resultStatus == "1") {
					mui.alert("封闭成功", "提示", "确定", function() {
						$("#pack_id").val("");
						$("#quality_desc").val("");
						$("#process_bug_id").val("");
						product_id = "";
						$("#pack_id").focus();
						pickedPhotos = new Array();
						base64ImageCode = new Array();
						$("#photo_picker").val("选择照片");
					}, 'div');
					return;
				} else {
					var errorinfo = "";
					if (data.resultStatus != "0") {
						errorinfo = data.resultStatus;
					} else {
						errorinfo = data.resultDesc;
					}
					mui.alert("封闭失败！原因：" + errorinfo, "提示", "确定", function() {
						$("#pack_id").val("");
						$("#quality_desc").val("");
						$("#process_bug_id").val("");
						product_id = "";
						$("#pack_id").focus();
						pickedPhotos = new Array();
						base64ImageCode = new Array();
						$("#photo_picker").val("选择照片");
					}, 'div');
					return;
				}
			} else { //连接失败
				mui("#block_button").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
				return;
			}

		},
		error: function() {
			mui("#block_button").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("服务器异常，请稍后再试！", "提示", "确定", function() {}, 'div');
		}
	});
}

//选择照片
function startPhotoPicker() {
	var actionbuttons = [
		//{title: "拍照"}, 
		{
			title: "从相册中选择"
		}
	];
	var actionstyle = {
		title: "选择图片",
		cancel: "取消",
		buttons: actionbuttons
	};

	plus.nativeUI.actionSheet(actionstyle, function(e) {
		switch (e.index) {
			case 1: //从相册中选择
				plus.gallery.pick(function(e) {
					pickedPhotos = new Array();
					base64ImageCode = new Array();
					console.log("本次选中照片：" + e.files.length);
					//遍历选中的图片文件
					for (var i in e.files) {
						console.log("选中照片：" + e.files[i]);
						pickedPhotos.push(e.files[i]);
						var fileName = e.files[i].substr(e.files[i].lastIndexOf('/') + 1);
						var name = "_doc/upload/" + fileName;
						console.log("压缩：" + name);
						//压缩
						plus.zip.compressImage({
								src: e.files[i], //src: (String 类型 )压缩转换原始图片的路径    
								dst: name, //压缩转换目标图片的路径    
								quality: 40, //quality: (Number 类型 )压缩图片的质量.取值范围为1-100    
								overwrite: true //overwrite: (Boolean 类型 )覆盖生成新文件    
							},
							function(zip) {
								//页面显示图片  
								plus.io.resolveLocalFileSystemURL(zip.target, function(entry) {
									entry.file(function(file) {
										var fileReader = new plus.io.FileReader();
										fileReader.readAsDataURL(file);
										fileReader.onloadend = function(e) {
											var picUrl = e.target.result.toString();
											base64ImageCode.push(picUrl); //将被选中图片的base64编码放入数组
											console.log("base64ImageCode照片：" + base64ImageCode.length);
										}
									});
								}, function(e) {
									mui.toast('读取照片文件错误：' + e.message);
								});
							},
							function(error) {
								mui.toast("压缩图片失败，请稍候再试");
							});
					}

					console.log("pickedPhotos照片：" + pickedPhotos.length);
					$("#photo_picker").val("已选择" + pickedPhotos.length + "张照片");

				}, function(e) {
					console.log("没有选中的照片");
					pickedPhotos = new Array();
					base64ImageCode = new Array();
					$("#photo_picker").val("选择照片");
				}, {
					filter: "image", //定义只能选择图片的过滤器
					multiple: true, //设置支持多张上传
					animation: true, //
					maximum: 5,
					selected: pickedPhotos, //设置选中的图片放入files数组
					onmaxed: function() {
						mui.toast("最多选择5张照片");
					} //过滤器只要图片，多选
				});
				break;
		}
	});
}
