/**
 * 变量定义
 */
var putinList= new Array();//选中之后要入库的列表
var countsl ="0";
$(function(){
	mui.init({
		swipeBack:true //启用右滑关闭功能
	});
	
	mui.ready(function(){
		$("#steel_support_id").focus();
	});
	// add by gll 佛宝的时候显示入库数量
	var segNo = localStorage.getItem("segNo");
	if(segNo =="00126"){
		$("#shuliang").show();
		$("#kucount").html(countsl);
	}
	// end by gll 佛宝的时候显示入库数量
});


//铁托架号
$("#steel_support_id").keypress(function(e){
	if(e.keyCode==13){
		var steel_support_id = $("#steel_support_id").val();
		if(steel_support_id=="" || steel_support_id == null){
			mui.alert("扫描或手工输入的托架号不能为空"," ","确定",function(){},'div');
			return false;
		}
		mui.plusReady(function() {
			var curNetConnetType = plus.networkinfo.getCurrentType();
			if( curNetConnetType != plus.networkinfo.CONNECTION_UNKNOW
				&& curNetConnetType !=plus.networkinfo.CONNECTION_NONE){
						querySteelSupportList(steel_support_id);
				}else{
					plus.nativeUI.toast("无网络连接，请检查网络后再次上传");
				}
			});
	}
});

//点击入库按钮
mui(document.body).on('tap', '#confirm', function() {
	if(putinList.length ==0) {
		mui.alert("已扫描列表无托架记录", "提示", "确定",null,'div');
		return ;
	} else {
		mui("#confirm").button("loading");
		$("#overlay").addClass("overlay");
	     putinSteelSupport();
	}
});


mui(document.body).on('tap','#back',function(){
	mui.back();
});
    
mui.back = function () {   
	if(putinList.length > 0){
		var btnArray = ['退出', '取消'];
		mui.confirm('存在已扫描未上传的数据,是否退出', '提示', btnArray, function(e) {
			if(e.index==0){
				setTimeout(function() {
					var ws=plus.webview.currentWebview();
					plus.webview.close(ws)
				}, 0);
			}
		},'div');
	}else{
		setTimeout(function() {
			var ws=plus.webview.currentWebview();
			plus.webview.close(ws)
		}, 0);
	}
};


//查询铁托架号 是否是出库状态 并带出详细信息
function querySteelSupportList(steel_support_id) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var steel_support_status ="30";//铁托架状态，入库只有出库状态才可以入库，现在只查询出30出库的铁托架号
	var params = '{"seg_no":"' + segNo + '","steel_support_id":"' + steel_support_id + '","steel_support_status":"'+steel_support_status+'"}';
	var method = "querySteelSupportInfo";
	console.log("铁托架："+params);
	params = encodeURI(params,'utf-8');
	$.ajax({
		type:"get",
		async:true,
		url:outUri,
		dataType: "json",
		timeout:2000,
		data: {
		    innerUri:innerUri,
		    params:params,
		    method:method
	    },
		success:function(data){
			if(data != null) {
				if(data.resultStatus == "1"){
					var lihtml = "";
					console.log("铁托架数据======："+data.steelList);
					$.each(data.steelList, function(i, item) {
						if(getIndexBySupprotId(steel_support_id,putinList)>-1){
			        			mui.alert("不能扫描重复的铁托架号"," ","确定",function(){},'div');
			        			$("#steel_support_id").val("");
			        			return;
			        	}
						var obj = {
							steel_support_id	:item.steel_support_id,
							steel_support_num	:item.steel_support_num,
							steel_support_name	:item.steel_support_name,
							pack_id				:item.lock_pack_id,
							status				:item.status
						};
							
						putinList.push(obj);
						lihtml = lihtml +loadingContentList(item)
						$("#companyList").append(lihtml);
						//清空铁托架号
						$("#steel_support_id").val("");
						$("#steel_support_id").focus();
						//add by gll
						countsl = parseFloat(countsl)  + 1;
						console.log("铁托架数据55："+countsl);
						$("#kucount").html(countsl);
						//end by gll
					});
				}else{
					mui.alert(data.resultDesc, "提示", "确定", function() {}, 'div');
					return;
				}
			}else{
				mui.alert("工贸服务器处理异常", "提示", "确定", function() {}, 'div');
				return ;
			}
		},
		error:function(XMLHttpRequest,textStatus,errorThrown){
		 //超过设定时间2秒，则不再等待服务端的响应结果。直接记录捆包号
	  	 if(textStatus != "timeout"){
	  	 	mui.alert("服务器连接异常", "提示", "确定", null, 'div');
	  	 }
		}
	});
}

//铁托架入库
function putinSteelSupport() {
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var vehicle_no = ""; //车牌号
	var tprovider_id = document.getElementById("chengyun").value;  
	if(tprovider_id =="null" || tprovider_id ==null ||tprovider_id ==""){
		tprovider_id="0";
	}
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	//加开关，如果没开业务的公司给一个默认值
	var scon2 = getSwitchValue(segNo,"IF_STEEL_VEHICLE");
	//判断当前账套是否存在盘库开关
	console.log("scon2  ：" + scon2);
	if(scon2 == 1){
		vehicle_no = $("#paihao").val();
	}else{
		vehicle_no="1";
	}
	var params = '{"seg_no":"' + segNo + '","user_id":"' + account + '","vehicle_no":"' + vehicle_no + '","tprovider_id":"' + tprovider_id + '","steel_support_list":' + JSON.stringify(putinList) +'}';
	console.log("入库："+params);
	params = encodeURI(params,'utf-8');
	var method = "exePutinSteelSupport";
	
	var outtime = 30000;
	if(putinList.length > 50 && putinList.length <=100){
		outtime = 60000;
	}else if(putinList.length > 100){
		outtime = 12000;
	}
	
	$.ajax({
		type:"post",
		async:true,
		timeout:outtime,
		url:outUri,
		dataType: "json",
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		success: function(data) {
			if(data !=null ){
			    mui("#confirm").button("reset");
			    $("#overlay").removeClass("overlay");
				if(data.resultStatus == "1"){
				 	mui.alert("操作成功", "提示", "确定",null,'div');
				 	$("#steel_support_id").val("");
					$("#steel_support_id").focus();
				 	$("#companyList").html("");
				 	putinList.clear;
				}else{
					  mui.alert("操作失败!原因："+data.resultDesc, "提示", "确定",null,'div');
				 	  return;
				    }
			}else{ //连接失败
				mui("#confirm").button("reset");
				$("#overlay").removeClass("overlay");
				mui.alert("工贸服务器处理异常", "提示", "确定",null,'div');
				 return ;
		    }
		},
		error: function(xhr, type, errorThrown) {
			mui.plusReady(function() {
				var curNetConnetType = plus.networkinfo.getCurrentType();
				if(curNetConnetType == plus.networkinfo.CONNECTION_UNKNOW ||
					curNetConnetType == plus.networkinfo.CONNECTION_NONE) {
					//mui.alert("无网络连接。请检查网络后再次上传", "提示", "确定", function() {}, "div");
					plus.nativeUI.toast("无网络连接。请检查网络后再次上传");
				} else if(type == "timeout") {
					xhr.abort();
					mui.alert("请求超时,请检查网络后再次上传", "提示", "确定", null, 'div');
				}else{
					mui.alert("连接服务器异常", "提示", "确定", function() {}, "div");
				}
				mui("#confirm").button('reset');
				$("#overlay").removeClass("overlay");
			});
		}
	});
}
	
function getIndexBySupprotId(steel_support_id,scanList){
	var index = -1;
	$.each(scanList, function(i,value) {
		if(value.steel_support_id == steel_support_id){
			index = i;
			return index;
		}
		if(value.steel_support_num == steel_support_id){
			index = i;
			return index;
		}
	});
	return index;
}

//删除
function deleteLi(ele, deletesupport_id) {
	var btnArray = ['确认', '取消'];
	var elem = ele;
	var li = elem.parentNode.parentNode;
	mui.confirm('确认删除该条记录？', '警告', btnArray, function(e) {
		if(e.index == 0) {
			var indexd = getIndexBySupprotId(deletesupport_id, putinList);
			if(indexd > -1) {
				putinList.splice(indexd, 1);
			}
			//删除捆包
			li.parentNode.removeChild(li);
			//add by gll
			countsl = parseFloat(countsl)  - 1;
			console.log("铁托架数据55："+countsl);
			$("#kucount").html(countsl);
			//end by gll
		} else {
			setTimeout(function() {
				$.swipeoutClose(li);
			}, 0);
		}
	});
}

	
function loadingContentList(item){
	var lihtml= '<li class="mui-table-view-cell">'+ 
					'<div class="mui-slider-right mui-disabled">'+
							'<a class="mui-btn mui-btn-red mui-icon" onclick='+'deleteLi(this,"'+item.steel_support_id+'")'+'>删除</a>'+
								'</div>'+
								'<div class="mui-slider-handle">'+
										'<div>'+
											'<div id="steel_support_id_1" data="'+item.steel_support_id+'">'+item.steel_support_id+'</div>'+
												'<div>'+
												    '<div id="spec" class="left"><span>编</span><label>'+item.steel_support_num+'</label></div>'+
													'<div id="steel_support_name"><span>名</span><label>'+item.steel_support_name+'</label></div>'+
											'</div>'+
								'</div>'+
						'</div>'+
				'</li>'; 
	return lihtml;
}

mui(document.body).on('click', '#paihao', function(e) {
	var segNo = localStorage.getItem("segNo");//采用localStorage存储数据
	var account = localStorage.getItem("account");//采用localStorage存储数据
	var webServiceUrl = localStorage.getItem("webServiceUrl");
	var outUri = domainName+"webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"' + segNo + '"}';
	params = encodeURI(params,'utf-8');
	var method = "exePutinVehicle";
	var tempHtml="";
	$.ajax({
		type: "post",
		url : outUri,
		data : {innerUri:innerUri,params:params,method:method},
		dataType : "json",
		timeout : 3000,
		async : true,
		cache : false,
		success : function (data) {
			console.log("sdfs"+data);
			if (data.returnStatus == "1") {
				$("#showname").show();
				$.each(data.returnList, function(i,item) {
					console.log("WWWW12324"+item.vehicle_no+"====="+item.tprovider_id);
					globalCount = data.returnList.length;
					tempHtml = tempHtml +	'<li class="mui-table-view-cell">' +
												'<a class="mui-navigate-right">' +
													'<div>' +
														'<div class="row"><span id="voucher_num">' + item.vehicle_no + '</span></div>' +
														'<div class="row"><input type="hidden" value="' + item.tprovider_id + '"/></div>' +
													'</div>' +
												'</a>' +
											'</li>';
					$("#customerList").html(tempHtml); 
				});
			}
			},
		error : function () {
 			mui.toast("网络超时，请稍后再试！");
			
		}
	});
})
//绑定单据点击事件
mui(document.body).on('tap','li',function(){
	 //var rdsObj = document.getElementsById("customer_id");
	//$("#customer_id").blur();
	var a = $(this).children('a');
	if(a.hasClass("select") == false){
		globalVehicleID = a.children('div').children('div').children('span').html().trim();
		globalSysID = a.children('div').children('div').next().children('input').val().trim();
		//console.log(globalSysID);
//		$.trim($("#customer_id").val()) = globalSysID;
	$("#paihao").val(globalVehicleID);
	$("#chengyun").val(globalSysID);
		$("#customerList").html("");
		$("#showname").hide();
		a.addClass("select"); //为当前的li元素添加勾选样式
	}else{
		globalVehicleID = "";
		globalSysID = ""; 
		a.removeClass("select");
	}
});