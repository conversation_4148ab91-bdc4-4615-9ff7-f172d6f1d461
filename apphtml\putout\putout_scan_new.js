/**
 * 初始化变量信息 
 */
var putinPackList = new Array(); //入库捆包列表（已扫描列表）
var team_id = localStorage.getItem("team_id"); //班组代码
var work_shift = localStorage.getItem("class_id"); //班次代码
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var operatorType = ""; //操作类型
var putoutVoucherList = new Array(); //出库单据列表
var unUploadPack = new HashMap(); //未上传捆包信息  Map<voucher_id,packList>
var uploadPack = new HashMap(); //已上传捆包信息  Map<voucher_id,packList>
var packInfo = new HashMap(); //捆包信息  Map<pack_id,packInfo>
var contractSubInfo = new HashMap(); //形式提单子项信息  Map<contract_subid,weight_qty_info>
var labelInfo = new HashMap(); //标签号捆包号对照关系信息 
var vehicle_id = ""; //车牌号  成都宝钢的车牌号必须填写，
var pda_switch = ""; //铁托架开关
var psteel_support_id = ""; //扫描当前捆包信息上自带的铁托架号

var toUploadPacks = new Array();


var pack_total_count = 0; //未扫描捆包个数
var pack_total_weight = 0; //未扫描捆包重量

//tangli add 
var if_auto_alloc_location_manage = 0; //判断是否提示实物出库窗口
var putout_confirm_flag = ""; //实物出库0 与 虚拟出库1
var mean_name = localStorage.getItem("name"); //判断是从菜单进来的还是出厂物流进来的
var hand_vehicle_id = localStorage.getItem("vehicle_id"); //车牌号 出厂物流
var car_trace_no = localStorage.getItem("car_trace_no");
var allocate_vehicle_id = localStorage.getItem("allocate_vehicle_id");
window.onload = function onload() {
	mui.plusReady(function() {
		operatorType = plus.webview.currentWebview().opt_type;
		pda_switch = plus.webview.currentWebview().pda_switch;
		putoutVoucherList = plus.webview.currentWebview().putoutVoucherList;
		//HashMap类型直接用=赋值会报错  所以把['map']内容移动过去
		//unUploadPack = plus.webview.currentWebview().unUploadPack;
		unUploadPack['map'] = plus.webview.currentWebview().unUploadPack['map'];
		uploadPack['map'] = plus.webview.currentWebview().uploadPack['map'];
		packInfo['map'] = plus.webview.currentWebview().packInfo['map'];
		labelInfo['map'] = plus.webview.currentWebview().labelInfo['map'];
		contractSubInfo['map'] = plus.webview.currentWebview().contractSubInfo['map'];
		//$("#voucher_num").text(voucher_num);
		//console.log("unUploadPack:" + JSON.stringify(unUploadPack));
		//console.log("uploadPack:" + JSON.stringify(uploadPack));
		//TODO 在这关闭是否合适？？？   关闭出库单据窗口
		/*// 获取所有Webview窗口  
				var wvs=plus.webview.all();  
				for(var i=0;i<wvs.length;i++){  
				console.log("webview"+i+": "+wvs[i].getURL());  
				} */
		//关闭 出库清单页面
		var ws = plus.webview.getWebviewById('putout_list_new');
		plus.webview.close(ws);
		//循环未扫描捆包信息，得到未扫描捆包重量和件数信息。
		xunhuan();
		if (segNo == "00138") {
			//高强钢不需要搜索
			$("#query_button").css("display", "none");
		}
	});

	// add tangli 得到开关值判断出库是否提示弹框
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","switch_type":"IF_AUTO_ALLOC_LOCATION_MANAGE"}';
	var method = "exeConfigPDAvoucherMaxCount";
	console.log("params" + params);

	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		console.log("data===========>" + JSON.stringify(data));
		if (null != data) {
			if_auto_alloc_location_manage = data.switch_con;
			console.log("if_auto_alloc_location_manage:" + if_auto_alloc_location_manage);
		}
	});

}

function xunhuan() {
	pack_total_count = 0;
	var list = unUploadPack.keySet();
	$.each(list, function(i, item) {
		var packList = unUploadPack.get(list[i]);
		if (packList.length > 0) {
			$.each(packList, function(j, jitem) {
				pack_total_count++;
				var pack_info = packInfo.get(packList[j].pack_id);
				console.log(pack_info.putin_weight + "," + pack_total_count);
				pack_total_weight = parseFloat(pack_total_weight) + parseFloat(pack_info.putin_weight);
			});
		}
	});
	pack_total_weight = pack_total_weight.toFixed(3);
	console.log("pack_total_count:" + pack_total_count + "," + pack_total_weight);
	$("#wscan_qty").text(pack_total_count);
	$("#wscan_weight").text(pack_total_weight);
}

mui.init({
	//不启用右滑关闭功能
	swipeBack: false
});
$(function() {
	//捆包输入框获得焦点
	$("#pack_id")[0].focus();
	if ('00118' == segNo || '00129' == segNo || '00145' == segNo || '00126' == segNo) {
		$("#car_num").val(hand_vehicle_id);
		$('#car_num').attr('disabled', true);
		vehicle_id = hand_vehicle_id;
		console.log(hand_vehicle_id + "," + vehicle_id);
		document.getElementById("car_num").style.backgroundColor = "#CCCCCC";
	}
	if ('00138' == segNo || '00181' == segNo) {
		$("#car_num").val(hand_vehicle_id);
		vehicle_id = hand_vehicle_id;
		console.log(hand_vehicle_id + "," + vehicle_id);
	}
	//				if(mean_name=="load_menus"){
	//					$("#divOpeNext").show();
	//				}
});

//捆包号
$("#pack_id").keypress(function(e) {
	if (e.keyCode == 13) {
		//add by xuhuaijun 20161229 格式化捆包号
		//下面有pack_id定义不要影响原先逻辑
		var packId = $("#pack_id").val();
		packId = formatPackId(packId);
		//add by xuhuaijun 20180925 武钢拼焊
		packId = trimPackId(packId);
		$("#pack_id").val(packId);
		//有可能扫描的时候扫描的标签号,这边做一次转换
		transLabelID2PackID(packId);

		var pack_id = $("#pack_id").val();
		//校验捆包号相关信息(是否在捆包列表中、是否在已扫捆包列表中、是否在未扫捆包列表中)
		if (!checkPackInfo(pack_id)) {
			$("#pack_id").val("");
			$("#pack_id")[0].focus();
			return false;
		} else {
			var pack_info = packInfo.get(pack_id);
			console.log("YYYYYYYYYpackList" + JSON.stringify(pack_info));
			//初步校验通过、如果形式提单校验超发否则直接处理捆包信息
			if (checkOutDeliverCheck(pack_info)) {
				$("#spec").val(pack_info.spec);
				$("#factory_product_id").val(pack_info.factory_product_id);
				$("#weight").val(pack_info.putin_weight);
				$("#qty").val(pack_info.putin_qty);
				$("#steel_support_id").val(pack_info.steel_support_id);
				//添加扫描时间到单据-捆包信息中
				var time = getnowtime();
				var unUploadPackList = unUploadPack.get(pack_info.voucher_id);
				var un_index = getIndexById(pack_info.pack_id, unUploadPackList)
				unUploadPackList[un_index]['scan_time'] = time;
				//测试是否是地址引用  是地址引用
				//console.log("扫描捆包通过超发校验后,unUploadPack:" + JSON.stringify(unUploadPack['map']))

				//将捆包从未扫捆包信息中移动到已扫捆包信息中
				addPack2Upload(pack_info);

				//pda_switch 公司开通铁托架功能，则扫描捆包之后 焦点调到铁托架号上。
				//未开通的则执行之前代码
				if (pda_switch == "1") {
					psteel_support_id = pack_info.steel_support_id;
					$("#steel_support_id")[0].focus();
				} else {
					//清空信息焦点定位到捆包
					$("#pack_id").val("");
					$("#spec").val("");
					$("#factory_product_id").val("");
					$("#weight").val("");
					$("#qty").val("");
					$("#pack_id")[0].focus();
				}
			} else {
				$("#pack_id").val("");
				$("#pack_id")[0].focus();
				return false;
			}
		}
	}
});

function transLabelID2PackID(packId) {
	console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
	var pack_id = labelInfo.get(packId);
	console.log("pack_id:" + pack_id);
	if (pack_id != null && pack_id != packId) {
		$('#pack_id').val(pack_id);
	}
}

//将捆包信息从未扫捆包列表中移动到已扫捆包列表中
function addPack2Upload(pack_info) {
	//console.log("addPack2Upload pack_info: " + JSON.stringify(pack_info));
	var packList_un = unUploadPack.get(pack_info.voucher_id);
	var index_un = getIndexById(pack_info.pack_id, packList_un);

	//判断已上传捆包信息中是否有该单据(如果有单据必定有捆包list最多length为0)
	//并将捆包信息加到已扫捆包信息中
	if (uploadPack.get(pack_info.voucher_id) == null) {
		//添加单据及捆包list
		var list = new Array();
		list.push(packList_un[index_un]);
		uploadPack.put(pack_info.voucher_id, list);
	} else {
		//直接将捆包信息添加到对应单据下的捆包列表
		var list = uploadPack.get(pack_info.voucher_id);
		list.push(packList_un[index_un]);
	}

	//将未扫捆包list的捆包信息移除
	packList_un.splice(index_un, 1);
	console.log("after addPack2Upload : ");
	console.log(JSON.stringify(unUploadPack));
	console.log(JSON.stringify(uploadPack));

	//更新合计框内信息
	refreshSum(pack_info);
}

//扫描捆包时校验捆包号相关信息(是否在捆包列表中、是否在已扫捆包列表中、是否在未扫捆包列表中)
function checkPackInfo(pack_id) {
	//捆包信息中是否有该捆包信息
	var pack_info = packInfo.get(pack_id);
	if (pack_info == null) {
		mui.alert("该" + pack_id + "不在本次出库捆包清单中", "提示", "确定", function() {}, "div");
		return false;
	}
	//捆包是否重复扫描
	var voucher_id = pack_info.voucher_id;

	//有可能已上传捆包map为null所以先校验是否拿得到单据对应的捆包列表
	if (uploadPack.get(voucher_id) != null) {
		var index1 = getIndexById(pack_id, uploadPack.get(voucher_id));
		console.log("index1" + index1);
		if (index1 != -1) {
			mui.alert("该" + pack_id + "捆包已经扫描,不可重复扫描", "提示", "确定", function() {}, "div");
			return false;
		}
	}

	if ('00166' == segNo) { //湛江出库添加外部捆包号扫描
		//捆包，外部捆包是否在未扫捆包信息中
		var pack_info_i = null;
		var packList = unUploadPack.get(voucher_id);
		if (packList.length > 0) {
			$.each(packList, function(i, item) {
				if (packInfo.get(packList[i].out_pack_id) != null) {
					pack_info_i = packInfo.get(packList[i].out_pack_id);
				}
				if (pack_info_i == null) {
					mui.alert("该" + pack_id + "捆包不在未扫捆包列表中", "提示", "确定", function() {}, "div");
					return false;
				}
			});
		}
	} else {
		//捆包是否在未扫捆包信息中
		var index2 = getIndexById(pack_id, unUploadPack.get(voucher_id));
		console.log(JSON.stringify(unUploadPack));
		console.log("index2" + index2);
		if (index2 == -1) {
			mui.alert("该" + pack_id + "捆包不在未扫捆包列表中", "提示", "确定", function() {}, "div");
			return false;
		}
	}
	//所有校验通过
	return true;
}

/**
 * 扫描捆包的时候，判断是否是形式提单，且判断是否超发
 * @param {Object} pack_info
 */
function checkOutDeliverCheck(pack_info) {
	console.log("pack_info" + JSON.stringify(pack_info));
	if (pack_info.advice_style == "20") { //捆包属于形式提单
		//根据订单子项号获取订单子项发货信息
		var voucherSubInfo = contractSubInfo.get(pack_info.voucher_subid);
		console.log("contractSubInfo before:" + JSON.stringify(contractSubInfo));
		//按重量发货
		if (pack_info.price_style == "10") {
			//发货通知重量
			var advice_weight = parseFloat(voucherSubInfo.advice_weight);
			//已出库量
			var act_weight = parseFloat(voucherSubInfo.act_weight);
			//当前捆包重量
			var putin_weight = parseFloat(pack_info.putin_weight);
			//超发量
			var super_weight = (act_weight + putin_weight) - advice_weight;
			console.log("aaaa" + advice_weight + ",    " + act_weight + ",     " + putin_weight + ",   " + super_weight);
			if (super_weight > 0) {
				mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_weight, "提示", "确认", function() {}, "div");
				return false;
			} else {
				voucherSubInfo.act_weight = parseFloat(act_weight + putin_weight);
				contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
				console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
				return true;
			}
			//数量
		} else if (pack_info.price_style == "20") { ////按数量发货
			//发货通知数量
			var advice_qty = parseFloat(voucherSubInfo.advice_qty);
			//已出库数量
			var act_qty = parseFloat(voucherSubInfo.act_qty);
			//当前捆包数量
			var putin_qty = parseFloat(pack_info.putin_qty);
			//超发量
			var super_qty = (act_qty + putin_qty) - advice_qty;
			if (super_qty > 0) {
				mui.alert(pack_info.voucher_subid + "本次发货量比可提货量超出了" + super_qty, "提示", "确认", function() {}, "div");
				return false;
			} else {
				voucherSubInfo.act_qty = parseFloat(act_qty + putin_qty);
				contractSubInfo.put(pack_info.voucher_subid, voucherSubInfo);
				console.log("contractSubInfo after add:" + JSON.stringify(contractSubInfo));
				return true;
			}
		}
	} else {
		return true;
	}
}

//铁托架号
//psteel_support_id捆包信息上的铁托架号
$("#steel_support_id").keypress(function(e) {
	if (e.keyCode == 13) {
		console.log("-----------steel_support_id：" + $("#steel_support_id").val());
		if ($("#steel_support_id").val() != "" && $("#steel_support_id").val() != null && $("#steel_support_id").val() !=
			"undefine") {
			//和捆包上带的是否一致
			if (psteel_support_id == "" || psteel_support_id == null || psteel_support_id == "undefine") {
				querySteelSupportInfo($("#steel_support_id").val());
			} else {
				if (psteel_support_id != $("#steel_support_id").val()) {
					querySteelSupportInfo($("#steel_support_id").val());
				}
			}
		} else {
			//如果输入铁托架为空认为进行下一个捆包的扫描
			//清空信息焦点定位到捆包
			$("#pack_id").val("");
			$("#spec").val("");
			$("#factory_product_id").val("");
			$("#weight").val("");
			$("#qty").val("");
			$("#pack_id")[0].focus();
		}
	}
});

//点击车牌号的文本框，弹出选择车牌号页面
mui(document.body).on('tap', '#car_num', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
	$("#car_no").val("");
	//如果湛江物流则车牌默认为沪开头
	if (segNo == "00166") {
		$("#car_no").val("沪");
	}
	//add by penglei gqg不用查询车牌号
	if (segNo != "00138") {
		//查询车牌号
		queryEnterFacoryVehicleNo();
	}

});

//按钮绑定事件 出库
mui(document.body).on('tap', '#putout', function() {

	//tangli add 判断是否弹出窗口
	if (if_auto_alloc_location_manage == 1) {
		var btnArray = ['虚拟出库', '实物出库'];
		mui.confirm('请确认实物是否出库', '', btnArray, function(e) {
			//实物出库0 与 虚拟出库1
			//console.log("e.index>>>>>>>>>>>"+e.index);
			if (e.index == 1) {
				putout_confirm_flag = 1;
			} else {
				putout_confirm_flag = 0;
			}

			console.log("operatorType==========》" + operatorType);

			//湛江的车牌号为必输项  股份业务车牌号必输
			if (operatorType != "ZJXHCK" && (operatorType.indexOf("ZJ") != -1 || operatorType.indexOf("GF") != -1)) {
				if (vehicle_id == "" || vehicle_id == null || vehicle_id == "undefine") {
					mui.alert("请输入车牌号！", "提示", "确认", function() {}, "div");
					$("#car_num")[0].focus();
					return false;
				} else {
					putoutPackUpload();
				}
			} else {
				//佛山宝钢先判断该车是否有过电子签名
				if (segNo == "00126" && mean_name == "load_menus") {
					querySignaturePutout();
				} else {
					putoutPackUpload();
				}

			}

		}, 'div');
	} else {
		//湛江的车牌号为必输项  股份业务车牌号必输
		if (operatorType != "ZJXHCK" && (operatorType.indexOf("ZJ") != -1 || operatorType.indexOf("GF") != -1)) {
			if (vehicle_id == "" || vehicle_id == null || vehicle_id == "undefine") {
				mui.alert("请输入车牌号！", "提示", "确认", function() {}, "div");
				$("#car_num")[0].focus();
				return false;
			} else {
				putoutPackUpload();
			}
		} else {
			//佛山宝钢先判断该车是否有过电子签名
			if (segNo == "00126" && mean_name == "load_menus" || segNo == "00181") {
				console.log(JSON.stringify(putoutVoucherList));
				querySignaturePutout();
			} else {
				putoutPackUpload();
			}
		}

	}
});

//查看入库捆包清单
mui(document.body).on('tap', '#detail', function() {
	mui.openWindow({
		url: 'putout_pack_list_new.html',
		id: 'putout_pack_list_new',
		createNew: true,
		extras: {
			unUploadPack: unUploadPack,
			uploadPack: uploadPack,
			packInfo: packInfo,
			contractSubInfo: contractSubInfo,
			putinPackList: putinPackList
		}
	});
});

//绑定入库清单信息回写事件
window.addEventListener('back', function(e) {
	//获得事件参数
	putinPackList = e.detail.putinPackList;
	unUploadPack['map'] = e.detail.unUploadPack['map'];
	uploadPack['map'] = e.detail.uploadPack['map'];
	packInfo['map'] = e.detail.packInfo['map'];
	contractSubInfo['map'] = e.detail.contractSubInfo['map'];
	console.log("unUploadPack:" + JSON.stringify(unUploadPack));
	console.log("uploadPack:" + JSON.stringify(uploadPack));
	console.log("packInfo:" + JSON.stringify(packInfo));
	console.log("contractSubInfo:" + JSON.stringify(contractSubInfo));
	console.log("putinPackList:" + JSON.stringify(putinPackList));
	//重算捆包合计信息
	reSum();
	//关闭捆包明细窗口
	var ws = plus.webview.getWebviewById('putout_pack_list_new');
	plus.webview.close(ws);
	//setDefaultInputStyle();
});

//单个捆包增加时调用
function refreshSum(pack_info) {
	//统计合计信息
	var qty = $("#scan_qty").text();
	qty = parseInt(qty) + 1;
	var weight = $("#scan_weight").text();
	weight = parseFloat(weight) + parseFloat(pack_info.putin_weight);
	var wqty = parseInt(pack_total_count) - parseInt(qty);
	var wweight = parseFloat(pack_total_weight) - parseFloat(weight);
	//取3位小数
	weight = weight.toFixed(3);
	wweight = wweight.toFixed(3);
	$("#scan_qty").text(qty);
	$("#scan_weight").text(weight);
	$("#wscan_qty").text(wqty);
	$("#wscan_weight").text(wweight);

}

//批量修改捆包信息后调用
function reSum(packlist) {
	var sum_qty = 0;
	var sum_weight = 0;
	var list = uploadPack.keySet();
	$.each(list, function(i, item) {
		var packList = uploadPack.get(list[i]);
		if (packList.length > 0) {
			$.each(packList, function(j, jitem) {
				sum_qty++;
				var pack_info = packInfo.get(packList[j].pack_id);
				sum_weight = parseFloat(sum_weight) + parseFloat(pack_info.putin_weight);
			});
		}
	});

	//保留三位小数
	sum_weight = sum_weight.toFixed(3);
	$("#scan_qty").text(sum_qty);
	$("#scan_weight").text(sum_weight);

	var wqty = parseInt(pack_total_count) - parseInt(sum_qty);
	var wweight = parseFloat(pack_total_weight) - parseFloat(sum_weight);
	wweight = wweight.toFixed(3);
	$("#wscan_qty").text(wqty);
	$("#wscan_weight").text(wweight);
}

/**
 * 
 * @param {Object} pack Json格式字符串
 */
function refreshPackInfo(pack) {
	//alert(pack.pack_id+","+pack.location_desc)
	//$("#pack_location").val(pack.location_desc);
	$("#pack_id").val(pack.pack_id);
	$("#spec").val(pack.spec);
	$("#factory_product_id").val(pack.factory_product_id);
	$("#weight").val(pack.putin_weight);
	$("#qty").val(pack.putin_qty);
	$("#spec")[0].focus();
}

//出库捆包上传post方法
function putoutPackUpload() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	console.log("name:" + mean_name);
	//点击入库按钮之后，整个按钮变为loading 整个页面加上一个蒙层，不允许任何操作。
	mui("#putout").button('loading');
	$("#overlay").addClass("overlay");
	//如果出现异常或者超时设置半分钟后可以再点一次
	setTimeout(function() {
		mui("#putout").button('reset');
		$("#overlay").removeClass("overlay");
	}.bind(this), 5000);

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	var params = initPutoutPackUploadParams();
	params = JSON.stringify(params);
	console.log("出库参数：" + params);
	//params=encodeURI(params,'utf-8');
	var method = "exeNewPutoutPackUpload";

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log("出库接口返回：" + JSON.stringify(result));
			if (result != null) {
				//console.log("data:"+JSON.stringify(data));
				mui("#putout").button('reset');
				$("#overlay").removeClass("overlay");
				if (result.returnStatus == "1") {
					mui.alert("" + result.returnDesc, "提示", "确定", function() {
						// var self = plus.webview.currentWebview();
						// var popwrovider = self.openType;
						// if (mean_name == "load_menus") {
						// 	mui.openWindow({
						// 		url: '../loadAndUnload/hand_point_end.html',
						// 		id: 'hand_point_end',
						// 		createNew: true
						// 	});
						// } else {
						// 	//关闭当前页面打开出库单据扫描页面
						// 	var ws = plus.webview.getWebviewById('putout_scan_new');
						// 	plus.webview.close(ws);
						// 	var ws_new = plus.webview.create('putout_list_new.html', 'putout_list_new');
						// 	ws_new.show();
						// }
						//添加PDA自助打印逻辑 yangzemin
						onPutoutSuccess(result.optBatchArray);
					}, "div");
				} else {
					mui.alert("出库失败：" + result.returnDesc, "提示", "确定", function() {}, "div");
				}
			} else {
				mui("#putout").button('reset');
				$("#overlay").removeClass("overlay");
				mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui("#putout").button('reset');
			$("#overlay").removeClass("overlay");
			mui.alert("出库失败请重试", "提示", "确定", function() {}, "div");
		}
	});

}

/**
 * 出库成功 yangzemin
 */
function onPutoutSuccess(optBatchArray) {
	var pdaPrint = getSwitchValue(segNo, 'PDA_PRINT_SERVER_PUTOUT');
	console.log("出库单pda自助打印开关：" + pdaPrint);

	if (pdaPrint == '1') {
		//开关打开
		if (!optBatchArray) {
			mui.alert("未获取到出库批次号", "提示", "确定", function() {}, "div");
			return;
		}
		doPDAPrint(optBatchArray);
	} else {
		goBack();
	}
}

/**
 * 返回上页
 */
function goBack() {
	var self = plus.webview.currentWebview();
	var popwrovider = self.openType;
	if (mean_name == "load_menus") {
		mui.openWindow({
			url: '../loadAndUnload/hand_point_end.html',
			id: 'hand_point_end',
			createNew: true
		});
	} else {
		//关闭当前页面打开出库单据扫描页面
		var ws = plus.webview.getWebviewById('putout_scan_new');
		plus.webview.close(ws);
		var ws_new = plus.webview.create('putout_list_new.html', 'putout_list_new');
		ws_new.show();
	}
}

/**
 * 调用工贸接口，打印出库单 yangzemin
 */
function doPDAPrint(optBatchArray) {

	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	//拼接参数信息
	var params = {};
	params['seg_no'] = segNo;
	params['user_id'] = user_id;
	params['print_type'] = "putout"; //打印类型：出库单
	params['opt_batch_array'] = optBatchArray;

	//构建报表打印参数
	var settings = {};
	settings['orientation'] = "0";
	settings['marginLeft'] = "10";
	settings['marginRight'] = "10";
	settings['marginTop'] = "10";
	settings['marginBottom'] = "10";
	settings['height'] = "305";
	settings['width'] = "215";
	settings['printServiceName'] = "EPSON LQ-630KII ESC/P2";
	params['settings'] = settings;

	params = JSON.stringify(params);
	console.log("doPDAPrint参数：" + params);
	//params=encodeURI(params,'utf-8');
	var method = "doPDAPrint";
	var waiting = plus.nativeUI.showWaiting(); //显示等待框

	$.ajax({
		type: "post",
		async: true,
		url: outUri,
		timeout: 100 * 1000,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(result) {
			console.log("doPDAPrint接口返回：" + JSON.stringify(result));
			waiting.close();
			if (result != null) {
				if (result.out_result == "1") {
					mui.alert("出库单" + result.out_result_desc, "提示", "确定", function() {
						goBack();
					}, "div");
				} else {
					mui.alert("出库单打印失败：" + result.out_result_desc, "提示", "确定", function() {}, "div");
				}
			} else {
				mui.alert("出库单打印失败", "提示", "确定", function() {}, "div");
			}
		},
		error: function() {
			mui.alert("出库单打印失败", "提示", "确定", function() {}, "div");
		}
	});

}

//初始化上传出库捆包参数
function initPutoutPackUploadParams() {
	var params = {};
	params['seg_no'] = segNo;
	params['user_id'] = user_id;
	params['team_id'] = team_id;
	params['work_shift'] = work_shift;
	params['operatorType'] = operatorType;
	// add by gll 上海不锈出库增加判断，改车是不是已经出库 2021年12月7日14:22:55
	//增加开关，，开的话，把车辆跟踪号传过去
	var pdaOut = getSwitchValue(segNo, 'PDA_PRINT_CAT_PUTOUT_SHBX');
	console.log("出库判断车辆是否离厂开关是否打开：" + pdaOut);
	if (pdaOut == '1') {
		//打开的话，把车辆跟踪号传过去
		params['car_trace_no'] = car_trace_no;
	}
	// end by gll 上海不锈出库增加判断，改车是不是已经出库 2021年12月7日14:22:55
	if (vehicle_id == null || vehicle_id == "null") {
		vehicle_id = "";
	}
	params['vechile_id'] = vehicle_id;
	//tangli  add
	params['putout_confirm_flag'] = putout_confirm_flag;
	//add by penglie  2018-10-26 18:21:40 佛宝将配车单写到出库中间表
	if (segNo == "00126") {
		if (allocate_vehicle_id != null && allocate_vehicle_id != "null") {
			params['allocate_vehicle_id'] = allocate_vehicle_id;
		}
	} else {
		params['allocate_vehicle_id'] = allocate_vehicle_id;
	}

	var voucherList = new Array();
	var voucherIdList = uploadPack.keySet();
	$.each(voucherIdList, function(i) {
		if (uploadPack.get(voucherIdList[i]).length > 0) { //该单据下有捆包要出库
			var voucher = {};
			//从出库单据列表中取单据验证码
			var identify_code = (putoutVoucherList[getIndexByVoucherId(voucherIdList[i], putoutVoucherList)]).identify_code;
			console.info("identify_codeidentify_codeidentify_code::" + identify_code);
			console.log("identify_code:" + identify_code);
			voucher['identify_code'] = identify_code;
			voucher['voucher_id'] = voucherIdList[i];
			voucher['pack_list'] = uploadPack.get(voucherIdList[i]);
			//往单据列表中添加信息
			voucherList.push(voucher);
		}
	});
	params['voucherList'] = voucherList;

	return params;
}

//车辆入厂车牌信息查询方法
function queryEnterFacoryVehicleNo() {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	vehicle_id = $("#car_no").val();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAVehicleTraceService'; //PDABoardVehicleService
	//var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDABoardVehicleService';//
	var params = '{"seg_no":"' + segNo + '","vehicle_no":"' + vehicle_id + '"}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "queryVehicleLicense";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("data:" + data);
		if (data != null) {
			//先清空当前列表中的牌号信息
			var lihtml = "";
			$("#carList").html(lihtml);
			//填充最新的牌号信息
			if (data.returnList.length > 0) {
				$.each(data.returnList, function(i, item) {
					lihtml = lihtml +
						'<li class="mui-table-view-cell">' +
						'<a class="mui-navigate-right">' +
						'<div style="width: 48%; float: left; text-align: left;" >' +
						'<label><span class="vehicle_no">' + item.vehicle_no + '</span>';
					if (item.vehicle_status_desc == "" || item.vehicle_status_desc == null) {

					} else {
						lihtml = lihtml + '<span class="vehicle_status_desc">' + item.vehicle_status_desc + '</span>';
					}
					lihtml = lihtml + '</label>' +
						'</div>' +
						'</a>' +
						'</li>';
				});
				$("#carList").html(lihtml);
			} else {
				//zjm$("#pop_car").toggleClass('show');
				mui.alert("未查询到可选择的车牌号,请手工输入！", "提示", "确定", function() {}, "div");
				$("#car_no").focus();
				return false;
			}
		} else { //连接失败
			mui.alert("查询车牌号失败", "提示", "确定", function() {}, "div");
			return false;
		}
	});
}

//绑定列表选中事件
mui(document.body).on('selected', '.mui-table-view.mui-table-view-radio', function(e) {
	var el = e.detail.el;
	var el_J = $(el);
	vehicle_id = el_J.find(".vehicle_no").text();
	$("#car_no").val(vehicle_id);
});

//搜索
mui(document.body).on('tap', '#query_button', function() {
	queryEnterFacoryVehicleNo();
});

//确认按钮绑定事件
mui(document.body).on('tap', '#confirm', function() {
	vehicle_id = $("#car_no").val();
	if (vehicle_id == "" || vehicle_id == null || vehicle_id == "undefine") {
		mui.alert("请输入出库车牌号", "提示", "确认", function() {}, "div");
		return false;
		//						if($("#car_no").val()=="" || $("#car_no").val()==null){
		//							mui.alert("请选择出库车辆","提示","确认");
		//						    return false;
		//						}else{
		//							vehicle_id=$("#car_no").val();
		//							$("#pop_car").toggleClass('show');
		//					        $("#pop_car_info").toggleClass('show');
		//					        $("#car_num").val(vehicle_id);
		//						}
	} else {
		$("#pop_car").toggleClass('show');
		$("#pop_car_info").toggleClass('show');
		$("#car_num").val(vehicle_id);
	}
});

mui(document.body).on('tap', '#pop_car', function() {
	$("#pop_car").toggleClass('show');
	$("#pop_car_info").toggleClass('show');
});
//点击下一步跳转
mui(document.body).on('tap', '#opeNext', function() {
	var vouchersize = JSON.stringify(uploadPack['map']).length;
	console.info("vouchersize1aaaaaaaaaa::::" + vouchersize);
	if (vouchersize > 2) {
		if (!confirm("有捆包已扫描，确定要返回吗？")) {
			return false;
		}
	}
	mui.openWindow({
		id: "hand_point_end",
		url: "../loadAndUnload/hand_point_end.html",
		createNew: true
	});
});

//铁托架扫描方法
function querySteelSupportInfo(steel_support_id) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDASteelSupportService';
	var params = '{"seg_no":"' + segNo + '","steel_support_id":"' + steel_support_id + '","steel_support_status":""}';
	console.log(params);
	params = encodeURI(params, 'utf-8');
	var method = "querySteelSupportInfo";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		if (data != null) {
			if (data.steelList.length > 0) {
				console.log(JSON.stringify(data.steelList));
				$.each(data.steelList, function(i, item) {
					//将铁托架号放入到已扫描的捆包信息中
					var num = getIndexById($("#pack_id").val(), putoutPackList);
					pack_info = putoutPackList[num];
					pack_info.steel_support_id = item.steel_support_id;
					putoutPackList.splice(num, 1);
					putoutPackList.push(pack_info);
					//putoutPackList 
					//$("#steel_support_id").val(item.steel_support_id);
					//继续进行扫描捆包的后续操作 
					//清空信息焦点定位到捆包
					$("#pack_id").val("");
					$("#spec").val("");
					$("#factory_product_id").val("");
					$("#weight").val("");
					$("#qty").val("");
					$("#steel_support_id").val("");
					$("#pack_id")[0].focus();
					console.log("有铁托架之后：" + JSON.stringify(putoutPackList));
				});
			} else {
				mui.alert("未查询到对应的铁托架信息", "提示", "确定", function() {}, 'div');
				$("#steel_support_id").val("");
				$("#steel_support_id")[0].focus();
				return;
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}
	});
}
/*mui(document.body).on('tap','#back',function(){
	mui.back();
});
			
mui.back = function(){
	var vouchersize = putoutVoucherList.length;
	var self = plus.webview.currentWebview();
	var popwrovider = self.openType;
	console.info("aaaaaaaaanihao 哈哈哈啊");
	if("hand_point_list"==popwrovider){
		mui.openWindow({
		url:'hand_point_end.html',
		id:'hand_point_end', 
		createNew:true
		});
	}
}*/
mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	var vouchersize = JSON.stringify(uploadPack['map']).length;
	console.info("vouchersize1aaaaaaaaaa::::" + vouchersize);
	if (vouchersize > 2) {
		if (!confirm("有捆包已扫描，确定要返回吗？")) {
			return false;
		}
	}
	//判断从装卸菜单进入
	if (mean_name == "load_menus") {
		mui.openWindow({
			url: '../loadAndUnload/hand_point_end.html',
			id: 'hand_point_end',
			createNew: true
		});
	} else {
		console.log("11111");
		var ws = plus.webview.currentWebview();
		plus.webview.close(ws);
		var w = plus.webview.getWebviewById('index');
		if (w != null) {
			plus.webview.show(w);
		} else {
			w = plus.webview.create('../public/index.html', 'index');
			plus.webview.show(w);
		}
		/* 直接修改登陆页面的 loacation.href 显示的菜单页面 这边当前窗口关闭后就直接显示index页面了
		 * mui.openWindow({
			id:"index"
		});*/
	}
}

function querySignaturePutout(steel_support_id) {
	<!-- 查询前先关闭软键盘-->
	document.activeElement.blur();
	//add by gll
	console.log("labelInfo:" + JSON.stringify(labelInfo['map']));

	var index = 0;
	var packIds = new Array();
	var upload = uploadPack.keySet();
	var pack = "";
	$.each(upload, function(i, item) {
		var list = uploadPack.get(item);
		$.each(list, function(j, jitem) {
			pack = packInfo.get(jitem.pack_id);
			console.log("捆包號=======" + pack.pack_id);
			packIds[index++] = pack.pack_id;


			console.log("捆包==---=======" + JSON.stringify(packIds));
			console.log("捆包=6595=---=======" + pack.pack_id);
			console.log("循环=---=======" + packIds);
		});
	});
	//console.log("labelInfo25:" + JSON.stringify(putoutPackList));
	//end by gll
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","pack_id":' + JSON.stringify(packIds) + ',"vehicle_id":"' + vehicle_id +
		'","car_trace_no":"' + car_trace_no + '","allocate_vehicle_id":"' + allocate_vehicle_id + '"}';
	console.log('' + params);
	params = encodeURI(params, 'utf-8');
	var method = "exeSignaturePutout";
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) {
		console.log("exeSignaturePutout->result：" + JSON.stringify(data));
		if (data != null) {
			if (data.resultstatus == "1") {
				putoutPackUpload();
			}
			//add by gll
			else if (data.resultstatus == "2") {
				mui.alert(data.returnDesc, "提示", "确定", function() {}, 'div');
				return;
			}
			//end by gll
			else {
				var params = initPutoutPackUploadParams();
				localStorage.setItem("vehicle_id", vehicle_id);
				localStorage.setItem("car_trace_no", car_trace_no);
				localStorage.setItem("unUploadPack", unUploadPack['map']);
				localStorage.setItem("uploadPack", uploadPack);
				localStorage.setItem("packInfo", packInfo);
				localStorage.setItem("contractSubInfo", contractSubInfo);
				localStorage.setItem("last_html", "putout_scan");
				localStorage.setItem("operatorType", operatorType);
				localStorage.setItem("putout_confirm_flag", putout_confirm_flag);
				localStorage.setItem("putoutVoucherList", putoutVoucherList);

				console.log("uploadPack->" + JSON.stringify(uploadPack));

				mui.openWindow({
					url: "../loadAndUnload/electric_signature.html",
					id: "electric_signature",
					extras: {
						//									vehicle_id:vehicle_id,
						//									car_trace_no:car_trace_no,
						//									unUploadPack : unUploadPack,
						uploadPack: uploadPack,
						//									packInfo : packInfo,
						//									contractSubInfo : contractSubInfo,
						//									last_html : "putout_scan"
						putoutVoucherList: putoutVoucherList,
						params: params
					},
					createNew: true
				});
			}
		} else { //连接失败
			mui.alert("连接服务器异常", "提示", "确定", function() {}, 'div');
			return;
		}

	});
}
//add by gll
mui(document.body).on('click', '#checkbtn', function() {
	//var advice_num = $(this).attr("data");
	var advice_num = "";
	mui.openWindow({
		url: '../putout/putout_bundle_verification.html',
		id: 'putout_bundle_verification',
		createNew: true,
		extras: {
			allocate_vehicle_id: allocate_vehicle_id,
			unUploadPack: unUploadPack,
			uploadPack: uploadPack,
			packInfo: packInfo,
			contractSubInfo: contractSubInfo,
			labelInfo: labelInfo,
			putinPackList: putinPackList
		}
	});

	console.log("QWE" + allocate_vehicle_id + "===" + advice_num);
});
//end by gll
