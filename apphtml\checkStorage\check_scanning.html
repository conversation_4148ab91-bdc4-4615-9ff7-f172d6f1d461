<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>盘点扫描</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="check_scanning.css" />
	</head>
	<body>
		<div id="overlay"></div>
		<div class="mui-bar mui-bar-nav">
			<a href="javascript:history.go(0)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
			<h4 class="mui-title">盘点扫描</h4>
		</div>
		<div class="mui-content" style="margin-top: 10px;padding-top: 0px;">
			<div style="text-align: left;"> 
				<div class="mui-input-row detail_row"><!-- 库位 -->
					<div class="text">库&nbsp;&nbsp;&nbsp;&nbsp;位</div>
					<div> <input id="packLocation" type="text" class="mui-input-clear" /> </div>
				</div>
				<div class="mui-input-row detail_row"><!-- 捆包 -->
					<div class="text">捆&nbsp;&nbsp;&nbsp;&nbsp;包</div>
					<div> <input id="packId" enterkeyhint="enter" type="text" class="mui-input-clear" /> </div>
				</div>
				<div class="mui-input-row detail_row"><!-- 规格 -->
					<div class="text">规&nbsp;&nbsp;&nbsp;&nbsp;格</div>
					<div> <input id="spec" type="text" class="mui-input-clear"/> </div>
				</div>
				<div class="mui-input-row detail_row"><!-- 重量/件数 -->
					<div class="text">重&nbsp;/&nbsp;件</div>
					<div> 
						<input id="weight" type="text" class="detail_row" style="width: 37.3%; margin-left: 0px; margin-right: 2px;" /> 
						<input id="qty" type="text" class="detail_row" style="width: 37%; margin-right: 0px; margin-left: 2px;" />
					</div>
				</div>
				<div class="mui-input-row detail_row "><!-- 质量异议 -->
					<div class="quality_dissent active2" id="qualityFlg" onclick="changeDissent()">无异议</div>
					<div><input id="qualityInfo" type="text" class="mui-input-clear dissent_input" placeholder="质量异议描述" />45</div>
				</div>
				<div class="mui-input-row sum" style="padding-left: 0px;"><!-- 合计框 -->
					<div>
						<div style="float: left; padding: 5px 5px;width: 40%;">最近捆包</div>
						<div style="float: left;width: 55%;"><label id="recentPackId" style="color: red; padding: 5px 0px;"></label></div>
					</div>
					<div style="float: left; width: 80%">
						<div style="clear: both;"></div>
						<div style="float: left;width: 30%;text-align: center;line-height: 230%;">
							<div class="sum_title">合计</div>
						</div>
						<div style="float: left; width: 70%;line-height: 120%;">
							<div id="sum_qty" class="qty">
								<div id="downLoadCount"></div>
							</div>
							<div id="sum_weight" class="weight">
								<div id="uploadingCount"></div>
							</div>
							<div id="sum_weight" class="weight">
								<div id="uploadedCount"></div>
							</div>
						</div>
					</div>
					<div id='detail' style="float: left;width: 20%; font-size: 46px;line-height: 100%;text-align: center;">
						>
					</div>
				</div>
				<div class="mui-input-row"><!-- 按钮 -->
					<button id="upload" type="button" class="mui-btn mui-btn-primary">上&nbsp; &nbsp; &nbsp;&nbsp;传</button>
					<button id="edit" type="button" class="mui-btn mui-btn-primary">修&nbsp; &nbsp; &nbsp;&nbsp;改</button>
				</div>	
			</div>
		</div>
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/util/public.js"></script>
		<script src="check_scanning.js"></script>
		<!--
        	作者：<EMAIL>
        	时间：2017-07-27
        	描述：
        <script type="text/javascript" charset="utf-8">  
			window.addEventListener('refresh', function(e) {  
				location.reload();   
			})  
		</script>-->
	</body>
</html>