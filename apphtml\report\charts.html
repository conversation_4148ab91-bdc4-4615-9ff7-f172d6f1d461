<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>查询条件</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../../css/mui.min.css" />
		<link rel="stylesheet" href="../../css/mui.picker.min.css" />
		
		<link rel="stylesheet" type="text/css" href="../../css/app.css" />
		<link rel="stylesheet" type="text/css" href="../../css/pad/all.css" />
		<link rel="stylesheet" href="../../css/pad/common.css" />
		<link rel="stylesheet" type="text/css" href="../../css/style.css" />
		<link rel="stylesheet" href="charts.css" />
	</head>
	<body>
		<!-- 侧滑导航根容器 -->
		<div class="mui-off-canvas-wrap mui-draggable">
		  <!-- 菜单容器 -->
		  <!--暂时屏蔽
		  <aside class="mui-off-canvas-left">
		    <div class="mui-scroll-wrapper">
		      <div class="mui-scroll">
		        <span class="android-only"></span>
		        	<div class="content"> </div>
                    <div id="menu1" class="menu">
                        <ul id="cateen1" class="mui-table-view mui-table-view-chevron mui-table-view-inverted">
                        	<li class="mui-table-view-cell">
	                            <a class="mui-navigate-right" href="javascript:void(0);" p_index="0">
									捆包查询
	                            </a>
	                        </li>
                            <li class="mui-table-view-cell">
	                            <a class="mui-navigate-right" href="javascript:void(0);" p_index="1">
									当前库存
	                            </a>
	                        </li>
	                        <li class="mui-table-view-cell">
	                            <a class="mui-navigate-right"href="javascript:void(0);" p_index="2">
									当前吞吐
	                            </a>
	                        </li>
                        </ul>
                    </div>
		      </div>
		    </div>
		    
		  </aside>-->
		  <!-- 主页面容器 -->
		  <div class="mui-inner-wrap">
		    <!-- 主页面标题 -->
		    <div class="mui-bar mui-bar-nav">
				<a href="javascript:history.go(0)" style="color: white;"><i class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></i></a>
				<h4 class="mui-title">查询</h4>
				<!--暂时屏蔽<span class="mui-pull-right" onclick="showmean()" style="color: #FFFFFF;line-height: 44px;">更多</span>-->
			</div>
		    <div class="mui-content mui-scroll-wrapper" style="margin-top: 50px;margin-left: 0px;padding-top: 0px;">
		      <div class="mui-scroll">
		        <div id="menu" class="menu">
				    <!--暂时屏蔽<button class="mean" onclick="showmean()"></button>-->
	                <div id="chartscontent" >
	                	
	                </div>
	                <div id="show_radar">
	                	<a onclick="showDayRadar(-1)">前一天</a>
	                	<input id="query_data" type="text" style="width: 50%;text-align: center;" readonly="readonly"/>
	                	<a onclick="showDayRadar(1)">后一天</a>
	                	<div id="char_radar" style="width: 100%;height:350px; "></div>
	                </div>
	                
	            </div>
		      </div>
		    </div>  
		  </div>
		</div>
		
		<div id="findpackinfo" style="display: none;">
        	<div style="margin-top: 10px;">
				<span class="putin_scanning" >
					<div class="mui-input-row mui-search">
						<input id="pack_id" class="mui-input query-input" style="margin-bottom: 0px; padding: 2px 10px; font-size: 20px;" type="text" placeholder="捆包号" />
						<a href="#"><span class="mui-icon mui-icon-search" id="query_button"></span></a>
					</div>
				</span>
				<div id="scroll" class="mui-scroll-wrapper" style="top: 50px; height: 310px;">  
		            <!--MUI默认是position是absolute-->  
		            <div class="mui-scroll">  
						<div style="text-align: left;">
							<div class="mui-input-row detail_row">
								<div class="text">钢厂资源号</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_factory_product_id" placeholder="钢厂资源号" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">资&nbsp;&nbsp;&nbsp;源&nbsp;&nbsp;&nbsp;号</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_product_id" placeholder="资源号" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">出&nbsp;库&nbsp;单&nbsp;号</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_putout_id" placeholder="出库单号" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">客&nbsp;户&nbsp;名&nbsp;称</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_cust_name" placeholder="客户名称" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">库&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_pack_location" placeholder="库位" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">牌&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_shopsign" placeholder="牌号" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">产&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;地</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_producing_area_name" placeholder="产地" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">规&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;格</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_spec" placeholder="规格" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">母&nbsp;&nbsp;&nbsp;卷&nbsp;&nbsp;&nbsp;号</div>
								<div class="text-content"> <input type="text" class="mui-input-clear p_m_pack_id" placeholder="母卷号" readonly="readonly"> </div>
							</div>
							<div class="mui-input-row detail_row">
								<div class="text">重&nbsp;&nbsp;&nbsp;&nbsp;/&nbsp;&nbsp;&nbsp;&nbsp;件</div>
								<div style="width: 70%;"> 
									<input type="text" class="detail_row p_putin_weight" style="width: 47%; margin-left: 0px; margin-right: 2px;"> 
									<input type="text" class="detail_row p_putin_qty" style="width: 47%; margin-right: 0px; margin-left: 2px;">
								</div>
							</div>
						</div>
					</div>
					
				</div>
				<div class="mui-input-row" style="margin-top: 320px;">
					<button id="query_button" type="button" class="mui-btn mui-btn-primary" style="width: 100%;font-size: 22px; ">捆包查询</button>
				</div>
			</div>
        </div>
		<!--当前库存 -->
		<div id="stock_pie" style="display: none;">
			<div id="ability">
				<div id="content" style="text-align: center;">
					<span class="simlefont" >当前库存</span>
					<div style="border-bottom: 1px solid #000;width: 180px;text-align: center;">
						<span class="bigfont" id="sum_weight">9203920</span><span class="simlefont">吨</span>
					</div>
					<span class="bigfont" id="weight_percent">56</span><span class="simlefont" >%</span>
				</div>
			</div>
		    <!-- 为ECharts准备一个具备大小（宽高）的Dom -->
		    <div id="main" style="width: 100%;height:300px;"></div>
		    <div style="bottom: 0px;" >
		    	<span id="wprovider_name"></span><br />
				<span >总库存：</span><span id="store_ability">10000000</span><span>吨</span>
			</div>
			<button id="pingzhong" type="button" class="mui-btn mui-btn-primary" style="width: 48%;font-size: 22px; ">品种</button>
			<button id="kuling" type="button" class="mui-btn mui-btn-primary" style="width: 48%;font-size: 22px; ">库龄</button>

		</div>
		
		<!-- 品种 start -->
		<div id="product_type_pie" style="display: none;">
			<div id="ability">
				<div id="content" style="text-align: center;">
					<span class="simlefont" >当前库存</span>
					<div style="border-bottom: 1px solid #000;width: 180px;text-align: center;">
						<span class="bigfont" id="sum_weight">9203920</span><span class="simlefont">吨</span>
					</div>
					<span class="bigfont" id="weight_percent">56</span><span class="simlefont" >%</span>
				</div>
			</div>
		    <!-- 为ECharts准备一个具备大小（宽高）的Dom -->
		    <div id="main" style="width: 100%;height:300px;"></div>
		    <div style="bottom: 0px;" >
		    	<span id="wprovider_name"></span><br />
				<span >总库存：</span><span id="store_ability">10000000</span><span>吨</span>
			</div>
			<button id="pingzhong" type="button" class="mui-btn mui-btn-primary" style="width: 48%;font-size: 22px; ">品种</button>
			<button id="kuling" type="button" class="mui-btn mui-btn-primary" style="width: 48%;font-size: 22px; ">库龄</button>

		</div>
		<!-- 品种 end -->
		
	</body> 
		<script type="text/javascript" src="../../js/pda/jquery-1.11.1.min.js"></script>
		<script src="../../js/mui.min.js"></script>
		<script src="../../js/mui.picker.min.js"></script>
		<script src="../../js/util/public.js"></script> 
		<script type="text/javascript" src="../js/echarts.js" charset="utf-8"></script>
		<script type="text/javascript" src="../js/charts/mycharts.js" charset="utf-8"></script>
		<script src="charts.js"></script>
</html>