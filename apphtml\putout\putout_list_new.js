/**
 * 初始化变量信息 
 */
var segNo = localStorage.getItem("segNo");
var user_id = localStorage.getItem("account");
var webServiceUrl = localStorage.getItem("webServiceUrl");
var opt_type = ""; //业务类型
var voucher_num = ""; //出库单据号
var scanPackList = new Array(); //选中的单据号的捆包信息（待扫描）
//var verify_id="";//输入的验证码
//var identify_code="";//接口的验证码
//var identify_expirydate="";//验证码有效期
var pda_switch = ""; //使用pda铁托架功能
var putoutVoucherList = new Array(); //出库单据列表
var voucher_count_max = 40; //扫描单据上限 update by tangli ERP_51022
var unUploadPack = new HashMap(); //未上传捆包信息  Map<voucher_id,packList>
var uploadPack = new HashMap(); //已上传捆包信息  Map<voucher_id,packList>
var packInfo = new HashMap(); //捆包信息  Map<pack_id,packInfo>
var contractSubInfo = new HashMap(); //形式提单子项信息  Map<contract_subid,weight_qty_info>
var confirm_flag = true;
var labelInfo = new HashMap(); //标签号、捆包号对照关系
var mean_name = localStorage.getItem("name"); //判断是从菜单进来的还是出厂物流进来的
var factory_area_id = localStorage.getItem("factory_area_id"); //厂区
var factory_wprovider = localStorage.getItem("factory_wprovider"); //厂区对应的仓库
var labelInfo = new HashMap(); //标签号、捆包号对照关系


var query_print_batch_id_list = 0; //是否从单据号扫描批次号进

function getJQueryObject(elem) {
	return $(elem);
}

window.onload = function onload() {
	mui.plusReady(function() {
		//单据号获得焦点
		$("#voucher_id")[0].focus();
		console.log(segNo+"lalalalala");
		//湛江物流设置业务类型显示
		if(segNo == "00166") {
			var html = $("#opt_type_icon").html();
			//add by tangli 20190122 湛江现货专项：增加按钮 湛江现货出库
			html = html + '<button type="button" class="mui-btn" data="ZJ_PUTOUT">湛江出库</button> <button type="button" class="mui-btn" data="ZJFC">湛江返厂</button> <button id="btn_zjxhck" type="button" class="mui-btn" data="ZJXHCK">湛江现货出库</button>';
			$("#opt_type_icon").html(html);
			$("#opt_type_icon").removeClass('opt_type-button-3');
			$("#opt_type_icon").addClass('opt_type-button-6');
		}

		//上海宝井高强钢股份业务设置业务类型显示
		if(segNo == "00137" || segNo == "00138") {
			var html = $("#opt_type_icon").html();
			html = html + '<button type="button" class="mui-btn" data="GF_PUTOUT">股份出厂</button>';
			$("#opt_type_icon").html(html);
			$("#opt_type_icon").removeClass('opt_type-button-3');
			$("#opt_type_icon").addClass('opt_type-button-4');
		}

		(function($) {
			//删除已扫描单据号
			$('#table').on('tap', '.mui-btn.mui-btn-red', function(event) {
				var elem = this;
				var li = elem.parentNode.parentNode;
				var li_J = getJQueryObject(li);
				//console.log(li_J.html());
				mui.confirm('确认删除该条记录？', '警告', ['确认', '取消'], function(e) {
					if(e.index == 0) {
						//TODO 删除出库单据
						//console.log(li_J.find("#voucher_num").text())
						delPutoutVoucherList(li_J.find("#voucher_num").text());
						li.parentNode.removeChild(li);
					} else {
						setTimeout(function() {
							$.swipeoutClose(li);
						}, 0);
					}
				}, 'div');
			});
		})(mui);
		var outUri = domainName + "webService.jsp?callback=?";
		var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
		var params = '{"seg_no":"' + segNo + '","switch_type":"CONFIG_PDA_VOUCHER_COUNT_MAX"}';
		var method = "exeConfigPDAvoucherMaxCount";
		console.log("params" + params);
		// add mengye  初始化开关控制是否配置了最大的扫描的提单数量 
		$.getJSON(outUri, {
			innerUri: innerUri,
			params: params,
			method: method
		}, function(data) {
			console.log("exeConfigPDAvoucherMaxCount 返回："+JSON.stringify(data));
			if(null != data) {
				if(data.switch_con == "1" && data.switch_desc != null) {
					console.log("switch_con:" + data.switch_con + "   switch_desc:" + data.switch_desc);
					voucher_count_max = data.switch_desc
				}
			}
		});
		//高强钢要求查询一厂查一仓库的提单，出厂物流查装卸点对应仓库的提单
		if(mean_name == "load_menus" && segNo == "00138" || segNo == "00181") {
			$("#divOpeNext").show();
			factory_wprovider = factory_wprovider;
		}
		if(mean_name == "" || mean_name == null || mean_name == "undefined") {
			if(segNo == "00138") {
				factory_wprovider = localStorage.getItem("wprovider_id");
			} else {
				factory_wprovider = "";
			}
		}
	});

}

$(function() {
	mui.init({
		swipeBack: true //启用右滑关闭功能
	});

});

mui(document.body).on('tap', '#back', function() {
	mui.back();
});

mui.back = function() {
	var vouchersize = putoutVoucherList.length;
	if(vouchersize != 0) {
		if(!confirm("有单据已扫描，确定要返回吗？")) {
			return false;
		}
	}
	if(segNo == "00138" && mean_name == "load_menus") {
		//高强钢要求返回建不能回到结束装卸货页面，只能通过 手工入库按钮
	} else if(segNo == "00126" && mean_name == "load_menus") {
		mui.openWindow({
			url: 'vehicle_load_manage.html',
			id: 'vehicle_load_manage',
			createNew: true
		});
	} else {
		var ws = plus.webview.currentWebview();
		var openType = ws.openType;
		console.info("openType:" + openType);
		if("vehicle_load_manage" == openType) {
			mui.openWindow({
				url: '../loadAndUnload/hand_point_end.html',
				id: 'hand_point_end',
				createNew: true
			});
		} else {
			plus.webview.close(ws);
			var w = plus.webview.getWebviewById('index');
			w = plus.webview.create('../public/index.html', 'index');
			plus.webview.show(w);

		}
	}
	/* 直接修改登陆页面的 loacation.href 显示的菜单页面 这边当前窗口关闭后就直接显示index页面了
	 * mui.openWindow({
		id:"index"
	});*/

}

//增加单据号的监听
$("#voucher_id").keypress(function(e) {
	if(e.keyCode == 13) {
		//判断是否达到单据扫描上限
		if(putoutVoucherList.length == voucher_count_max) {
			mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
			return false;
		} else {
			voucher_num = $("#voucher_id").val();
			if(voucher_num != "" && voucher_num != null) {
				putoutVoucherDownLoad();
			}
		}
	}
});

//点击搜索图标跳转页面
mui(document.body).on("tap", ".icon-search", function() {
	if(putoutVoucherList.length == voucher_count_max) {
		mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
		return false;
	}
	mui.openWindow({
		url: 'putout_voucher_search.html',
		id: 'putout_voucher_search',
		createNew: true,
		extras: {
			opt_type: opt_type,
			voucher_count_max: voucher_count_max,
			putoutVoucherList: putoutVoucherList
		}
	});
});

//选择业务类型
mui(document.body).on('click', '#opt_type_icon button', function() {
	//获取点击业务类型 和 当前出库单据业务类型进行比较	
	var opt_type_new = $(this).attr("data");
	if(opt_type == "" || putoutVoucherList.length == 0) {
		opt_type = opt_type_new;
		setOptTypeView($(this), opt_type);
	} else if(opt_type != opt_type_new) {
		var obj = $(this);
		mui.confirm('存在其他类型的单据,切换单据类型将清空已扫描单据，是否继续？', '警告', ['确认', '取消'], function(e) {
			if(e.index == 0) {
				//清空已扫单据
				putoutVoucherList = new Array();
				opt_type = opt_type_new;
				setOptTypeView(obj, opt_type);
				$('#table > li').remove();
				//empty()清除DOM元素内容不是删除所以还是用remove
				//$("#table").empty();
			} else {
				return false;
			}
		});
	} else {
		opt_type = opt_type_new;
		setOptTypeView($(this), opt_type);
	}
});

function setOptTypeView(obj, opt_type) {
	//前台页面显示
	//单据号输入框修改
	if(opt_type == "CC") {
		if(putoutVoucherList.length == 0) {
			$("#voucher_id").val("CA");
		} else {
			$("#voucher_id").val("");
		}
	} else if(opt_type == "ZK") {
		if(putoutVoucherList.length == 0) {
			if(segNo == "00162" || segNo == "00163") { //零部件ZK开头
				$("#voucher_id").val("ZK");
			} else {
				$("#voucher_id").val("DK");
			}
		} else {
			$("#voucher_id").val("");
		}
	} else if(opt_type == "XS") {
		if(putoutVoucherList.length == 0) {
			$("#voucher_id").val("EL");
		} else {
			$("#voucher_id").val("");
		}
	}else {
		$("#voucher_id").val("");
	}
	//按钮样式修改
	obj.addClass('active').siblings().removeClass('active');
}

/* 提单安全性校验移动到public.js中
			//提单安全性校验
			function inputVerify(voucher){
				//console.log(JSON.stringify(voucher));
				var send_to_fetch = voucher.send_to_fetch;
				var identify_code = voucher.identify_code;
				var identify_expirydate = voucher.identify_expirydate;
				
				if(send_to_fetch==1){
					var verify_id = prompt('请输入验证码');
					if(verify_id != null && verify_id != ""){
						if(identify_code == verify_id){
							//当前时间
							var ttime= new Date();//new Date(getnowtime().replace("-", "/").replace("-", "/"));
							//验证码有效期
							identify_expirydate=new Date(identify_expirydate.replace(/-/g, "/"));
							var days= identify_expirydate.getTime() - ttime.getTime();
   							var time = parseInt(days / (1000 * 60 * 60 * 24));
							//console.log(days+"，"+time);
							if(time<0){
								mui.alert("验证码已经过期，不能出库","提示","确认",function() {}, "div");//验证码已经过期，不能出库
								return false;
							}else{
								return true;
							}
						}else{
							mui.alert("输入的验证码为空或者验证码不正确，请重新输入！","提示","确认",function() {}, "div");
							return false;
						}
					}else{
						return false;
					}
				}else{
					return true;
				}
			}
			*/

/**
 * 出库凭单查询
 * 业务类型必填，时间范围必填，单据号不必填
 */
function putoutVoucherDownLoad() {
	console.log(webServiceUrl);
	var voucher_id = $("#voucher_id").val();
	var outUri = domainName + "webService.jsp?callback=?";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","operator_type":"' + opt_type + '","restrict_date":"","customer_id":"","voucher_id":"' + voucher_id + '","factory_wprovider":"' + factory_wprovider + '"}';
	console.log(params);
	var method = "exePutoutVoucherDownLoad";
	/* post方法提交
				var outUri = domainName+"webService_test.jsp";
				$.ajax({
					type: "post",
					async: true,
					url: outUri,
					data: {innerUri:innerUri,params:params,method:method},
					dataType: "json",
					//jsonp: "callback",//传递给请求处理程序或页面的，用以获得jsonp回调函数名的参数名(一般默认为:callback)
					//jsonpCallback:"flightHandler",//自定义的jsonp回调函数名称，默认为jQuery自动生成的随机函数名，也可以写"?"，jQuery会自动为你处理数据
					success: function(data){
					    console.log(JSON.stringify(json));
					},
		            error: function(){
		                 mui.alert("没有下载到出库单据信息","提示","确认",function() {}, "div");
		            }
		        });
		        */
	$.getJSON(outUri, {
		innerUri: innerUri,
		params: params,
		method: method
	}, function(data) { //如返回对象有一个username属性
		console.log("exePutoutVoucherDownLoad返回："+JSON.stringify(data));
		if(null != data) {
			if(data.resultStatus == "1") {
				console.log("PDA_switch:" + data.pda_switch + "," + JSON.stringify(data.putoutVoucherList));
				pda_switch = data.pda_switch;
				//判断下载的单据列表类型是否和已扫描的单据一致
				if(opt_type != "" && data.operator_type != opt_type) {
					mui.alert("下载的单据类型与选择的不一致\n请重新选择单据类型或查询同一类型单据", "提示", "确认", function() {}, "div");
					return false;
				} else {
					opt_type = data.operator_type;
				}
				if(data.putoutVoucherList.length == 1) {
					var chtml = $("#table").html();
					$.each(data.putoutVoucherList, function(i, item) {
						console.log("-----------------item:" + item)
						//提单安全性校验
						var result = inputVerify(item);
						if(result) { //通过校验
							if(addPutoutVoucherList(item)) { //如果可以添加
								chtml = chtml + '<li class="mui-table-view-cell">' +
									'<input id="identify_expirydate" value="' + item.identify_expirydate + '" hidden="hidden"/>' +
									'<input id="identify_code" value="' + item.identify_code + '" hidden="hidden"/>' +
									'<input id="send_to_fetch" value="' + item.send_to_fetch + '" hidden="hidden"/>' +
									'<div class="mui-slider-right mui-disabled">' +
									'<a class="mui-btn mui-btn-red mui-icon">删除</a>' +
									'</div>' +
									'<div class="mui-slider-handle">' +
									'<div>' +
									'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span>' + //<span class="icon">量</span>'+item.voucher_id+'</div>
									'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
									'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
									'</div>' +
									'</div>' +
									'</li>';
							} else {; //未添加成功
							}

						} else {; //未通过验证码校验
						}
					});
					$("#table").html(chtml);
					//清除输入框、根据所选业务类型重新设定开头
					if(opt_type == "CC") {
						$('#btn_cc').click();
					} else if(opt_type == "ZK") {
						$('#btn_zk').click();
					} else if(opt_type == "XS") {
						$('#btn_xs').click();
					} else if(opt_type == "ZJXHCK") {
						$('#btn_zjxhck').click();
					} else {
						$("#voucher_id").val("");
					}
				} else if(data.putoutVoucherList.length > 1) {
					//从单据号扫描进入出库单据页面 默认全部勾选
					query_print_batch_id_list = 1;
					//TODO 跳转到新页面
					mui.openWindow({
						url: "putout_voucher_select.html",
						id: "putout_voucher_select",
						createNew: true,
						extras: {
							open_from_url: "putout_list_new.html",
							open_from_id: "putout_list_new",
							opt_type: opt_type,
							queryVoucherList: data.putoutVoucherList,
							putoutVoucherList: putoutVoucherList,
							voucher_count_max: voucher_count_max,
							query_print_batch_id_list: query_print_batch_id_list
						}
					});
				}
			} else {
				mui.alert(data.resultDesc + '或与已扫描单据业务类型不一致', "提示", "确认", function() {}, "div");
			}
		} else { //连接失败
			mui.alert("没有下载到出库单据信息", "提示", "确认", function() {}, "div");
		}
	});
	//console.log(JSON.stringify(putoutVoucherList));
}

function putoutPackDownLoad(voucher, putout_count) {
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","voucher_id":"' + voucher.voucher_id + '","allocate_vehicle_id":"' + voucher.allocate_vehicle_id + '"}';
	console.log(params);
	var method = "exePutoutPackDownLoad";
	$.ajax({
		type: "post",
		async: false,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		//jsonp: "callback",//传递给请求处理程序或页面的，用以获得jsonp回调函数名的参数名(一般默认为:callback)
		//jsonpCallback:"flightHandler",//自定义的jsonp回调函数名称，默认为jQuery自动生成的随机函数名，也可以写"?"，jQuery会自动为你处理数据
		success: function(data) {
			console.log("出库单下载捆包数据: " + JSON.stringify(data));
			if(data != null) {
				if(data.resultStatus == "1") {
					if(data.packList.length > 0) {
						//输出出库需要扫描的捆包信息
						handleDownloadPack(voucher, data.packList); //处理下载的捆包信息
					}
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				} else {
					console.log("error");
					confirm_flag = false;
					mui.alert("单据" + voucher.voucher_id + "下载信息：" + data.resultDesc, "提示", "确认", function() {}, "div");
					//当单据捆包信息下载完毕后 判断是否进行后续操作
					afterPackDownload(putout_count);
				}
			}
		},
		error: function() {
			confirm_flag = false;
			mui.alert("单据" + voucher.voucher_id + "下载信息失败", "提示", "确认", function() {}, "div");
			//当单据捆包信息下载完毕后 判断是否进行后续操作
			afterPackDownload(putout_count);
		}
	});

}

//出库列表中添加记录putoutVoucherList
function addPutoutVoucherList(record) {
	//校验扫描单据数目
	if(putoutVoucherList.length == voucher_count_max) {
		mui.alert("已扫描单据数已达到上限：" + voucher_count_max, "提示", "确认", function() {}, "div");
		return false;
	}
	var index = getIndexByVoucherId(record.voucher_id, putoutVoucherList);
	if(index != -1) {
		mui.alert("已存在该单据" + record.voucher_id + ",不能重复扫描", "提示", "确认", function() {}, "div");
		return false;
	} else {
		putoutVoucherList.push(record);
		return true;
	}
}

//出库列表中删除记录putoutVoucherList
function delPutoutVoucherList(voucher_id) {
	var index = getIndexByVoucherId(voucher_id, putoutVoucherList);
	if(index != -1) {
		//从index处删除几个
		putoutVoucherList.splice(index, 1);
		return true;
	} else {
		return false;
	}
}

//单据选择页面触发新增单据事件
window.addEventListener('addFromSelect', function(e) {
	//获得事件参数
	var voucherList = e.detail.voucherList; //已选择单据列表
	var optType = e.detail.optType; //单据业务类型
	var flag = false;
	//console.log("opt_type" + opt_type + " optType:" + optType);
	//如果通过查询条件页面选择的单据返回到该页面时当前页面可能没有单据业务类型，需要进行处理
	if(opt_type != "") {
		if(opt_type != optType) {
			mui.alert("所选单据类型与已扫单据业务类型不匹配\n不能添加", "提示", "确认", function() {}, "div");
			return false;
		} else {
			flag = true; //延迟触发设定业务类型
		}
	} else {
		flag = true; //延迟触发设定业务类型
		opt_type = optType;
	}
	if(flag) {
		//因为这边已经完成验证码和单据数量的校验,直接添加到出库单据信息中
		var chtml = $("#table").html();
		$.each(voucherList, function(i, item) {
			//添加单据
			if(!addPutoutVoucherList(item)) {
				return true;
			} else {
				chtml = chtml + '<li class="mui-table-view-cell">' +
					'<input id="identify_expirydate" value="' + item.identify_expirydate + '" hidden="hidden"/>' +
					'<input id="identify_code" value="' + item.identify_code + '" hidden="hidden"/>' +
					'<input id="send_to_fetch" value="' + item.send_to_fetch + '" hidden="hidden"/>' +
					'<div class="mui-slider-right mui-disabled">' +
					'<a class="mui-btn mui-btn-red mui-icon">删除</a>' +
					'</div>' +
					'<div class="mui-slider-handle">' +
					'<div>' +
					'<div class="row"><span id="voucher_num">' + item.voucher_id + '</span>' + //<span class="icon">量</span>'+item.voucher_id+'</div>
					'<div class="row"><span class="icon">客</span>' + item.cust_name + '</div>' +
					'<div class="row"><span class="icon">收</span>' + item.consignee_addr + '</div>' +
					'</div>' +
					'</div>' +
					'</li>';
			}
		});
		$("#table").html(chtml);
		//清除输入框、根据所选业务类型重新设定开头
		if(opt_type == "CC") {
			$('#btn_cc').click();
		} else if(opt_type == "ZK") {
			$('#btn_zk').click();
		} else if(opt_type == "XS") {
			$('#btn_xs').click();
		} else if(opt_type == "ZJXHCK") {
			$('#btn_zjxhck').click();
	    } else {
			$("#voucher_id").val("");
		}
	}
	//console.log("after from selectToputout putoutVoucherList.length:" + putoutVoucherList.length + " voucherList.length:" + voucherList.length + JSON.stringify(voucherList));
});

//确认按钮事件绑定			
mui(document.body).on("tap", "#confirm", function() {
	mui(this).button('loading');

	//如果出现异常或者超时设置一分钟后可以再点一次
	setTimeout(function() {
		mui(this).button('reset');
	}.bind(this), 60000);
	//莫名其妙会把单据号输入框获得焦点 先干掉
	document.activeElement.blur();
	//校验已扫单据数量
	if(putoutVoucherList.length == 0) {
		mui.alert("请先查询单据信息", "提示", "确认", function() {}, "div");
		mui(this).button('reset');
		return false;
	}
	//如果非销售单据类型的,直接初始化信息;
	//销售单据类型如果不同形式提单包含相同订单子项不允许一起进行出库

	//初始化信息
	confirm_flag = true; //下载是否有报错
	//清空捆包下载信息
	unUploadPack.removeAll(); //未上传捆包信息
	uploadPack.removeAll(); //已上传捆包信息
	packInfo.removeAll(); //捆包信息
	contractSubInfo.removeAll(); //形式提单子项信息
	var putout_count = 0; //来统计遍历数量 标记是否是最后一次下载
	//循环遍历出库单据
	$.each(putoutVoucherList, function(i, item) {
		putout_count++;
		putoutPackDownLoad(item, putout_count);
	});
	console.log(">>>>>>>>>>>>>>>segNo:" + segNo);
	if(segNo == "00131") {
		console.log(">>>>>>>>>>>>>>>segNo:" + segNo);
		console.log("xinsongjiekou>>>>>>>>>>>" + JSON.stringify(putoutVoucherList));
		sendYtWarehousePackInfo2WsService();
	}
});

function handleDownloadPack(voucher, downloadPackList) {
	if(downloadPackList.length > 0) {
		if(voucher.advice_style == "20") { //形式提单先校验订单子项号是否重复
			var flag = true; //标记校验是否通过
			$.each(downloadPackList, function(i, item) {
				if(contractSubInfo.get(item.voucher_subid) != null) {
					mui.alert("形式提单:" + voucher.voucher_id + "与其他形式提单的订单子项号重复,不能在同一批次出库,已经自动移除该单据,请后续重新扫描", "提示", "确认", function() {}, "div");
					flag = false;
					return false;
				}
			});
			if(flag) {
				var packList = new Array(); //未扫捆包信息集合
				var xsweight = 0;
				$.each(downloadPackList, function(i, item) {
					//形式提单信息处理
					var info = {};
					info['price_style'] = item.price_style; //计价方式 按数量 按重量
					info['act_weight'] = item.act_weight; //act_weight 已出库重量
					info['act_qty'] = item.act_qty; //act_qty  已出库数量
					info['advice_weight'] = item.advice_weight; //advice_weight 发货通知重量
					info['advice_qty'] = item.advice_qty; //advice_qty  发货通知数量
					contractSubInfo.put(item.voucher_subid, info);

					//捆包信息
					var pack = {};
					pack["pack_id"] = item.pack_id;
					pack["label_id"] = item.label_id;
					pack["product_id"] = item.product_id;
					pack["scan_time"] = '';
					pack["steel_support_id"] = '';
					pack["out_pack_id"] = item.out_pack_id;//yss添加外部捆包号
					packList.push(pack); //添加捆包
					packInfo.put(item.pack_id, item); //添加捆包
					packInfo.put(item.out_pack_id, item); //yss添加外部捆包号
					labelInfo.put(item.label_id, item.pack_id); //添加标签号、捆包对照关系
				});
				unUploadPack.put(voucher.voucher_id, packList);
				//console.log("packList.length:" + packList.length + JSON.stringify(packList));
				//console.log("packInfo:" + JSON.stringify(packInfo['map']));
				//console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
				//console.log("contractSubInfo:" + JSON.stringify(contractSubInfo['map']));
				//console.log('advice_style:' + (packInfo.valueSet())[0].advice_style);
			}
		} else {
			var packList = new Array();
			$.each(downloadPackList, function(i, item) {
				var pack = {};
				pack["pack_id"] = item.pack_id;
				pack["label_id"] = item.label_id;
				pack["product_id"] = item.product_id;
				pack["out_pack_id"] = item.out_pack_id;//yss添加外部捆包号
				pack["scan_time"] = '';
				pack["steel_support_id"] = '';
				packList.push(pack); //添加捆包
				packInfo.put(item.pack_id, item); //添加捆包
				packInfo.put(item.out_pack_id, item); //添加捆包
				labelInfo.put(item.label_id, item.pack_id); //添加标签号、捆包对照关系
			});
			unUploadPack.put(voucher.voucher_id, packList);
			//console.log("packList.length:" + packList.length + JSON.stringify(packList));
			//console.log("packInfo:" + JSON.stringify(packInfo['map']));
			//console.log("labelInfo:" + JSON.stringify(labelInfo['map']));
			//console.log("unUploadPack:" + JSON.stringify(unUploadPack['map']));
			//console.log('advice_style:' + (packInfo.valueSet())[0].advice_style);
		}
	} else {
		console.log(voucher.voucher_id + "下载失败");
	}
}

//每反馈一次单据捆包下载信息调用一次
function afterPackDownload(putout_count) {
	//当单据信息下载完毕后 判断是否进行后续操作
	if(putout_count == putoutVoucherList.length) {
		//confirm_flag 校验在下载捆包信息过程中是否有报错
		if(!confirm_flag) {
			//清空捆包下载信息
			unUploadPack.removeAll(); //未上传捆包信息
			uploadPack.removeAll(); //已上传捆包信息
			packInfo.removeAll(); //捆包信息
			contractSubInfo.removeAll(); //形式提单子项信息

			mui($('#confirm')).button('reset');
			mui.alert("有单据捆包信息未下载成功,请删除单据或重试", "提示", "确认", function() {}, "div");
		} else {
			//console.log('下载完成');
			//打开出库捆包扫描页面
			openPutoutScan();
		}
	}
}
//下载捆包信息完成后打开出库捆包扫描页面
function openPutoutScan() {
	mui.openWindow({
		id: "putout_scan_new",
		url: "putout_scan_new.html",
		extras: {
			opt_type: opt_type,
			pda_switch: pda_switch,
			putoutVoucherList: putoutVoucherList,
			unUploadPack: unUploadPack,
			uploadPack: uploadPack,
			packInfo: packInfo,
			contractSubInfo: contractSubInfo,
			labelInfo: labelInfo
		},
		createNew: true
	});

	//var ws = plus.webview.getWebviewById('putout_list_new');  
	//plus.webview.close(ws);
}

//点击下一步跳转
mui(document.body).on('tap', '#opeNext', function() {
	mui.openWindow({
		id: "hand_point_end",
		url: "../loadAndUnload/hand_point_end.html",
		createNew: true
	});
	/*var ws = plus.webview.currentWebview();  
			        plus.webview.close(ws);*/
	var w = plus.webview.getWebviewById("vehicle_load_manage");
	plus.webview.close(w);
});

// 烟台宝井立体仓库出库捆包发送新松
function sendYtWarehousePackInfo2WsService() {

	var sendVoucherList = new Array();
	$.each(putoutVoucherList, function(i, item) {
		var voucherInfo = {};
		voucherInfo.voucher_id = item.voucher_id;
		sendVoucherList.push(voucherInfo);
	});
	console.log("xinsongjiekou>>>>>>>>>>>" + JSON.stringify(sendVoucherList));
	var outUri = domainName + "webService_test.jsp";
	var innerUri = 'http://' + webServiceUrl + '/sm/ws/PDAPutoutService';
	var params = '{"seg_no":"' + segNo + '","user_id":"' + user_id + '","sendVoucherList":' + JSON.stringify(sendVoucherList) + '}';
	console.log(params);
	var method = "exeSendYtWarehousePackInfoStart";
	$.ajax({
		type: "post",
		async: false,
		url: outUri,
		data: {
			innerUri: innerUri,
			params: params,
			method: method
		},
		dataType: "json",
		success: function(data) {

			if(data != null) {
				console.log("data>>>>>>>>>>>>" + JSON.stringify(data));
				if(data.resultStatus == "1") {

				} else {
					console.log("error");
					confirm_flag = false;
					mui.alert("单据" + voucher.voucher_id + "下载信息：" + data.resultDesc, "提示", "确认", function() {}, "div");
				}
			}
		},
		error: function() {
			confirm_flag = false;
			mui.alert("单据" + voucher.voucher_id + "下载信息失败", "提示", "确认", function() {}, "div");
		}
	});
}